import {<PERSON><PERSON><PERSON><PERSON>} from "@/components/ui/scroll-area";
import React, {useState} from "react";
import {PageLoader} from "@/components/custom-ui/loader";
import {DocumentStore} from "@/components/workspace/main/views/document/document";
import {View} from "@/typings/page";
import {ContentLocked, ViewWarning, WarningMessage} from "@/components/workspace/main/views/common/contentLocked";
import {CollaborativeUser, YJSDoc} from "@/components/workspace/main/common/YJSDoc";
import {useMaybeShared} from "@/providers/shared";
import {Button} from "@/components/ui/button";
import {Bars3CenterLeftIcon} from "@heroicons/react/24/outline";
import ErrorBoundary from "@/components/ui/errorBoundary";
import {useBroadcast} from "@/providers/broadcast";
import {useMaybeTemplate} from "@/providers/template";
import { CommentProvider } from "@/providers/comment";

interface DocumentContentProps {
    activeId: string
    setActiveId: (id:string) => void
    store: DocumentStore
    updateDocumentTitle: (documentId: string, name: string) => void
    updateDocumentContent: (documentId: string, contentText: string, contentJSON: object) => Promise<void>
    view: View
    canEdit: boolean
    isCollapsed: boolean
    setCollapsed: (c: boolean) => void
}

export const DocumentContent = (props: DocumentContentProps) => {

    const activeDoc = props.store[props.activeId]

    return <>
        {activeDoc ? <>
            <DocEditor {...props} key={props.canEdit ? '1' : '0'}/>
        </> : <>
             <PageLoader size='full' error={props.activeId ? 'Document not found' : ''}/>
         </>}
    </>

}

export enum DocConfig {
    namespace = 'document-view'
}

const DocEditor = (props: DocumentContentProps) => {
    const template = useMaybeTemplate()
    const shared = useMaybeShared()
    const activeDoc = props.store[props.activeId]
    const editable = props.canEdit && !props.view.definition.lockContent
    const roomName = `d:${props.view.pageId}|${props.view.id}|${props.activeId}`

    const {updateDocumentContent, updateDocumentTitle} = props


    const initialContent = Array.isArray(activeDoc.contentJSON) ? activeDoc.contentJSON as any : []
    const documentId = props.activeId

    const [isConnected, setConnected] = useState(false)
    const [isEditorReady, setIsEditorReady] = useState(false)

    const {sendMessage} = useBroadcast();

    // const clientsRef = useRef<CollaborativeUser[]>()
    // const handlerRef = useRef<(id: string) => void>()

    const onClients = (users: CollaborativeUser[]) => {
        sendMessage(DocConfig.namespace, `${props.view.pageId}:${props.view.id}:${documentId}`, {
            users
        })
    }

    return <>

        <ErrorBoundary>
            <div className={"flex-1 flex flex-col overflow-hidden"}>
                {!shared && !template && isEditorReady && !isConnected && <ViewWarning message={WarningMessage.ConnectionLost}/>}

                {!shared && !template && !props.canEdit && <>
                    <ContentLocked/>
                </>}
                <ScrollArea className="w-full flex-1">
                    <div className='px-1 w-full flex gap-1 items-center'>
                        <Button variant="ghost" className="p-2 rounded-full h-auto relative lg:hidden"
                                onClick={() => props.setCollapsed(!props.isCollapsed)}>
                            <Bars3CenterLeftIcon className="w-4 h-4"/>
                        </Button>
                        <input
                            onBlur={e => {
                                const val = e.target.value.trim()
                                if (val === activeDoc.name) return
                                updateDocumentTitle(documentId, val)
                            }}
                            readOnly={!!template}
                            className='text-2xl p-2 h-auto lg:text-4xl lg:p-8 lg:px-12 lg:pb-4 lg:h-24 font-black text-black border-none outline-none flex-1' placeholder='Untitled' defaultValue={activeDoc.name}/>
                    </div>
                    <YJSDoc
                        documentId={`${DocConfig.namespace}:${documentId}`}
                        roomName={roomName}
                        initialContent={initialContent}
                        readonly={!editable}
                        onEditorReady={() => setIsEditorReady(true)}
                        onClientList={onClients}
                        onConnectionStatusChanged={setConnected}
                        collaborationEnabled={!shared && !template} />
                </ScrollArea>
            </div>
        </ErrorBoundary>

    </>
}

