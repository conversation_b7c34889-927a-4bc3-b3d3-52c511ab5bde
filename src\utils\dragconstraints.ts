import { Modifier } from '@dnd-kit/core';
 
export const restrictToCalendarContainer: Modifier = ({ transform, draggingNodeRect, windowRect }: any) => {
  if (!draggingNodeRect || !windowRect) {
    return transform;
  }

  const calendarContainer = document.querySelector('[data-calendar-content="true"]');
  if (!calendarContainer) {
    return transform;
  }

  const containerRect = calendarContainer.getBoundingClientRect();
  
  const sideCard = document.querySelector('[data-side-card="true"]');
  let maxX = containerRect.right - draggingNodeRect.width;
  
  if (sideCard) {
    const sideCardRect = sideCard.getBoundingClientRect();
    maxX = Math.min(maxX, sideCardRect.left - draggingNodeRect.width);
  }
  
  const timeLabels = document.querySelector('[data-time-labels="true"]');
  const dayHeaders = document.querySelector('[data-day-headers="true"]');
  const allDayRow = document.querySelector('[data-all-day-row="true"]');
  
  let minX = containerRect.left;
  let minY = containerRect.top;
  const maxY = containerRect.bottom - draggingNodeRect.height;
  
  if (timeLabels) {
    const timeLabelsRect = timeLabels.getBoundingClientRect();
    minX = Math.max(minX, timeLabelsRect.right);
  }
  
  if (dayHeaders) {
    const dayHeadersRect = dayHeaders.getBoundingClientRect();
    minY = Math.max(minY, dayHeadersRect.bottom);
  }
  

  if (allDayRow) {
    const allDayRowRect = allDayRow.getBoundingClientRect();
    minY = Math.max(minY, allDayRowRect.bottom);
  }

// Get current pointer position.
  const currentX = transform.x + draggingNodeRect.left;
  const currentY = transform.y + draggingNodeRect.top;


  const constrainedX = Math.min(Math.max(currentX, minX), maxX);
  const constrainedY = Math.min(Math.max(currentY, minY), maxY);

  return {
    ...transform,
    x: constrainedX - draggingNodeRect.left,
    y: constrainedY - draggingNodeRect.top,
  };
}; 