/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: false,
    async rewrites() {
        return [
            {
                source: '/:username(@[a-zA-Z0-9]+)*',
                destination: "/templates/creator/:username*",
            }
        ]
    },
    async headers() {
        return [
            // Apply security headers to all paths
            {
                source: '/:path*',
                headers: [
                    {key: 'X-Frame-Options', value: 'DENY'},
                    {key: 'X-Content-Type-Options', value: 'nosniff'},
                    {key: 'Strict-Transport-Security', value: 'max-age=31536000; includeSubDomains'},
                    {key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin'},
                    {key: 'Permissions-Policy', value: 'geolocation=(), microphone=(), camera=()'},
                    {key: 'Content-Security-Policy', value: "frame-ancestors 'none';"},
                ],
            },

            // Override /embed/* path to remove/alter some headers
            {
                source: '/embed/:path*',
                headers: [
                    // Remove X-Frame-Options and allow iframe embedding
                    {key: 'X-Content-Type-Options', value: 'nosniff'},
                    {key: 'Content-Security-Policy', value: 'frame-ancestors *;'}, // ✅ allow all
                ],
            },
        ];
    },
}

let config = nextConfig


// Injected content via Sentry wizard below

const {withSentryConfig} = require("@sentry/nextjs");

config = withSentryConfig(
    config,
    {
        // For all available options, see:
        // https://github.com/getsentry/sentry-webpack-plugin#options

        org: "opendashboard",
        project: "opendashboard-frontend",

        // Only print logs for uploading source maps in CI
        silent: !process.env.CI,

        // For all available options, see:
        // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

        // Upload a larger set of source maps for prettier stack traces (increases build time)
        widenClientFileUpload: true,

        // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
        // This can increase your server load as well as your hosting bill.
        // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
        // side errors will fail.
        // tunnelRoute: "/monitoring",

        // Hides source maps from generated client bundles
        hideSourceMaps: true,

        // Automatically tree-shake Sentry logger statements to reduce bundle size
        disableLogger: true,

        // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
        // See the following for more information:
        // https://docs.sentry.io/product/crons/
        // https://vercel.com/docs/cron-jobs
        automaticVercelMonitors: true,
    }
);

module.exports = {
    ...config,
    experimental: {
        instrumentationHook: process.env.NODE_ENV === 'production',
    },
}
