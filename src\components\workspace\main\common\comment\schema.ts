import { MyWorkspaceMember } from "@/typings/workspace";
import { BlockNoteSchema, createBlockSpecFromStronglyTypedTiptapNode, createStronglyTypedTiptapNode, defaultBlockSpecs, defaultInlineContentSpecs, defaultStyleSpecs } from "@blocknote/core";
import { DefaultReactSuggestionItem } from "@blocknote/react";
import CommentInline, { Mention } from "./comment-inline";
import { commentStyleSpec } from "./commentMark";
import { createNotification } from "@/api/workspace";



const paragraph = createBlockSpecFromStronglyTypedTiptapNode(
    createStronglyTypedTiptapNode<"paragraph", "inline*">(
        defaultBlockSpecs.paragraph.implementation.node.config as any,
    ),
    // disable default props on paragraph (such as textalignment and colors)
    {},
);

const { textColor, backgroundColor, ...styleSpecs } = defaultStyleSpecs;


export const CommentSchema = BlockNoteSchema.create({
  blockSpecs: {
        paragraph,

  },
    inlineContentSpecs: {
        ...defaultInlineContentSpecs,
      mention: Mention,
    },
});
export const DocumentSchema = BlockNoteSchema.create({
  inlineContentSpecs: {
    ...defaultInlineContentSpecs,
    mention: Mention,
  },
  styleSpecs: {
    ...defaultStyleSpecs,
    comment: commentStyleSpec,
  },
});

export const getMentionMenuItems = (
  editor: typeof CommentSchema.BlockNoteEditor,
  members: MyWorkspaceMember[]
): DefaultReactSuggestionItem[] => {
  return members.map((u) => {
    const displayName =
      (u.user.firstName && u.user.lastName)
        ? `${u.user.firstName} ${u.user.lastName}`
        : u.user.email;
    return {
      title: displayName,
      onItemClick: () => {
        // createNotification(token, WorkspaceProvider, {
        //   userId: u.user.id,
        //   workspaceId: u.workspaceMember.workspaceId,
        //   options: {
        //     title: "You are mentioned",
        //     message:""
        // }
              
        //     })
        editor.insertInlineContent([
          {
            type: "mention",
            props: {
              user: displayName,
              id: u.user.id
            },
          },
          " ", 
        ]);
      },
    };
  });
};
