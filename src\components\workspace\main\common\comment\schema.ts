import { MyWorkspaceMember } from "@/typings/workspace";
import { BlockNoteSchema, createBlockSpecFromStronglyTypedTiptapNode, createStronglyTypedTiptapNode, defaultBlockSpecs, defaultInlineContentSpecs, defaultStyleSpecs } from "@blocknote/core";
import { DefaultReactSuggestionItem } from "@blocknote/react";
import CommentInline, { Mention } from "./comment-inline";



const paragraph = createBlockSpecFromStronglyTypedTiptapNode(
    createStronglyTypedTiptapNode<"paragraph", "inline*">(
        defaultBlockSpecs.paragraph.implementation.node.config as any,
    ),
    // disable default props on paragraph (such as textalignment and colors)
    {},
);

const { textColor, backgroundColor, ...styleSpecs } = defaultStyleSpecs;


export const CommentSchema = BlockNoteSchema.create({
  blockSpecs: {
        paragraph,

  },
    inlineContentSpecs: {
        ...defaultInlineContentSpecs,
      mention: Mention,
    },
});
export const DocumentSchema = BlockNoteSchema.create({
  inlineContentSpecs: {
    ...defaultInlineContentSpecs,
    mention: Mention,
  },
});

export const getMentionMenuItems = (
  editor: typeof CommentSchema.BlockNoteEditor,
  members: MyWorkspaceMember[]
): DefaultReactSuggestionItem[] => {
  // const users = ["Steve", "Bob", "Joe", "Mike"];


  return members.map((u) => ({
    title: (u.user.firstName + " " + u.user.lastName),
    onItemClick: () => {
      editor.insertInlineContent([
        {
          type: "mention",
          props: {
            user: (u.user.firstName + " " + u.user.lastName),
            id: u.user.id
          },
        },
        " ", // add a space after the mention
      ]);
    },
  }));
};
