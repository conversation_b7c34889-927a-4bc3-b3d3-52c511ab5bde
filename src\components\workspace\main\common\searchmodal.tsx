"use client"
import { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react"
import { <PERSON>alog, DialogContent } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { useWorkspace } from "@/providers/workspace"
import { useAuth } from "@/providers/user"
import { searchWorkspaces } from "@/api/workspace"
import { SearchResult } from "@/typings/workspace"
import { useRouter } from "next/navigation"
import { formatDistanceToNow } from "date-fns"
import debounce from "lodash/debounce"
import {MagnifyingGlassIcon, TimerIcon, XmarkIcon, BookIcon, TableIcon, UserGroupIcon, ClockThreeIcon, NoteIcon, SquareListIcon, WifiSlashIcon} from "@/components/icons/FontAwesomeRegular"
import { Loader } from "@/components/custom-ui/loader"
import { ViewIcon } from "../views/viewIcon"
import { ViewType } from "opendb-app-db-utils/lib/typings/view"

interface SearchModalProps { onClose: () => void; debounceTimeoutMS?: number }

const HighlightedContent = ({ content, highlight, query }:
  { content?: string; highlight?: { start: number; end: number }; query?: string }) => {
  if (!content) return null;

  if (query && query.trim()) {
    const searchTerm = query.trim().toLowerCase();
    const contentLower = content.toLowerCase();
    const parts = [];
    let lastIndex = 0;
    let currentIndex = contentLower.indexOf(searchTerm, lastIndex);

    if (currentIndex === -1) return <>{content}</>;
    while (currentIndex !== -1) {
      parts.push(content.substring(lastIndex, currentIndex));
      parts.push(
        <mark key={currentIndex} className="bg-yellow-200">
          {content.substring(currentIndex, currentIndex + searchTerm.length)}
        </mark>
      );
      lastIndex = currentIndex + searchTerm.length;
      currentIndex = contentLower.indexOf(searchTerm, lastIndex);
    }

    if (lastIndex < content.length) {
      parts.push(content.substring(lastIndex));
    }

    return <>{parts}</>;
  }

  if (highlight && highlight.start >= 0) {
    const start = Math.max(0, highlight.start);
    const end = Math.min(content.length, highlight.end);


    if (start < end && start < content.length) {
      return (
        <>{content.substring(0, start)}
          <mark className="bg-yellow-200">{content.substring(start, end)}</mark>
          {content.substring(end)}
        </>
      );
    }
  }
  return <>{content}</>;
};

interface GroupedResults {
  databases: SearchResult[]; pages: SearchResult[]; views: SearchResult[];
  documents: SearchResult[]; members: SearchResult[];
}

const groupResults = (results: SearchResult[]): GroupedResults => {
  const grouped = { databases: [], pages: [], views: [], documents: [], members: [] } as GroupedResults;
  results.forEach(result => {
    if (result.image || !result.source) {
      grouped.members.push(result);
    } else if (result.source) {
      if (result.source.databaseId && !result.source.viewId) grouped.databases.push(result);
      else if (result.source.viewId) grouped.views.push(result);
      else if (result.source.documentId) grouped.documents.push(result);
      else if (result.source.pageId) grouped.pages.push(result);
    }
  });
  return grouped;
};

interface ApiError extends Error {
  response?: { data?: { error?: string; message?: string; status?: number } };
  status?: number;
}

export default function SearchModal({ onClose, debounceTimeoutMS = 2500 }: SearchModalProps) {
  const [query, setQuery] = useState(""), [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false), [error, setError] = useState<string | null>(null)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1), [hasMore, setHasMore] = useState(true)
  const [totalItems, setTotalItems] = useState(0), [currentQuery, setCurrentQuery] = useState("")
  const { workspace, url } = useWorkspace(), { token } = useAuth(), router = useRouter()
  const workspaceId = workspace?.workspace?.id
  const resultsContainerRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const savedSearches = localStorage.getItem("recentSearches")
    if (savedSearches) setRecentSearches(JSON.parse(savedSearches))
  }, [])

  const getErrorMessage = (error: unknown): string => {
    if (error instanceof Error) {
      if ('response' in error) {
        const apiError = error as ApiError;
        return apiError.response?.data?.message || apiError.message;
      }
      return error.message;
    }
    return 'An unexpected error occurred';
  };

  const performSearch = useCallback(async (searchQuery: string, page = 1, append = false) => {
    if (!searchQuery.trim() || !workspaceId || !token) {
      setResults([]); setError(null); setIsLoading(false);
      setHasMore(false); setTotalItems(0); setCurrentPage(1);
      return;
    }

    const currentSearchQuery = searchQuery;

    if (page === 1) setHasMore(true);
    setIsLoading(true);
    setError(null);

    try {
      const response = await searchWorkspaces(token.token, workspaceId, currentSearchQuery, page, 25);
      if (!response.isSuccess) throw new Error(response.error || 'Search failed');

      const newResults = response.data.data.results.results || [];
      const pagination = response.data.data.results.pagination;

      if (currentSearchQuery === query) {
        setTotalItems(pagination.totalItems);
        setHasMore(page < pagination.totalPages);

        setResults(append ? prev => [...prev, ...newResults] : newResults);
        setCurrentPage(page);
        setCurrentQuery(currentSearchQuery);
      }
    } catch (error: unknown) {
      console.error("Search error:", error);
      if (!append && currentSearchQuery === query) {
        setError(getErrorMessage(error));
      }
    } finally {
      if (currentSearchQuery === query) {
        setIsLoading(false);
      }
    }
  }, [workspaceId, token, query]);

  const debouncedSearch = useCallback(
    debounce((searchQuery: string) => {
      if (searchQuery.trim()) {
        setCurrentPage(1);
        performSearch(searchQuery, 1, false);
      }
    }, debounceTimeoutMS),
    [performSearch, debounceTimeoutMS, setCurrentPage]
  );

  const loadMore = useCallback(() => {
    if (!isLoading && hasMore && currentQuery) {
      performSearch(currentQuery, currentPage + 1, true);
    }
  }, [isLoading, hasMore, currentQuery, currentPage, performSearch]);


  useEffect(() => {
    if (!resultsContainerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !isLoading && results.length > 0) {
          loadMore();
        }
      },
      {
        root: resultsContainerRef.current,
        rootMargin: '0px 0px 200px 0px',
        threshold: 0.1
      }
    );

    const sentinel = document.getElementById('search-results-sentinel');
    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel);
      }
      observer.disconnect();
    };
  }, [loadMore, hasMore, isLoading, results.length]);

  // Handle search input changes
  useEffect(() => {
    debouncedSearch.cancel();
    setResults([]);
    setCurrentPage(1);
    setHasMore(false);
    setTotalItems(0);
    setCurrentQuery("");

    if (query.trim()) {
      setIsLoading(true);
      debouncedSearch(query);
    } else {
      setIsLoading(false);
    }

    return () => debouncedSearch.cancel();
  }, [query, debouncedSearch])

  const handleResultClick = (result: SearchResult) => {
    router.push(url(result.path)); onClose(); saveRecentSearch(query);
  }

  const saveRecentSearch = (search: string) => {
    if (search.trim()) {
      const updatedSearches = [search, ...recentSearches.filter((s) => s !== search)].slice(0, 5)
      setRecentSearches(updatedSearches)
      localStorage.setItem("recentSearches", JSON.stringify(updatedSearches))
    }
  }

  const deleteRecentSearch = (search: string, e: React.MouseEvent) => {
    e.stopPropagation()
    const updatedSearches = recentSearches.filter(s => s !== search)
    setRecentSearches(updatedSearches)
    localStorage.setItem("recentSearches", JSON.stringify(updatedSearches))
  }

   const getIconForSource = (result: SearchResult) => {
    if (result.path?.includes('?tab=reminders')) return <ClockThreeIcon className="h-4 w-4 text-muted-foreground" />;
    if (result.path?.includes('?tab=notes')) return <NoteIcon className="h-4 w-4 text-muted-foreground"/>;

    if (result.viewType) return <ViewIcon type={result.viewType} className="h-4 w-4 text-muted-foreground" />;

    if (result.source?.databaseId && result.source.recordId) return <SquareListIcon className="h-4 w-4 text-primary" />;
    if (result.source?.databaseId) return <TableIcon className="h-4 w-4 text-primary" />;
    if (result.source?.documentId) return <BookIcon className="h-4 w-4 text-accent-foreground" />;
    if (result.source?.pageId) return <BookIcon className="h-4 w-4 text-secondary-foreground" />;

    if (result.image !== undefined || !result.source) return <UserGroupIcon className="h-4 w-4 text-muted-foreground" />;
    return <BookIcon className="h-4 w-4 text-muted-foreground" />;
  };

  const getResultType = (result: SearchResult) => {
    if (result.path?.includes('?tab=reminders')) return "Reminder";
    if (result.path?.includes('?tab=notes')) return "Note";
    if (result.image) return "Member";
    if (!result.source) return "Member";

    if (result.source.databaseId && result.viewType) {
      switch (result.viewType) {
        case ViewType.Table: return "Table View";
        case ViewType.Board: return "Board View";
        case ViewType.Dashboard: return "Dashboard";
        case ViewType.Document: return "Document View";
        case ViewType.Form: return "Form View";
        case ViewType.SummaryTable: return "Summary Table";
        case ViewType.ListView: return "List View";
        default: return "Database View";
      }
    }

    if (result.source.databaseId && result.source.recordId) return "Record";
    if (result.source.databaseId) return "Database";
    if (result.source.documentId) return "Document";
    if (result.source.pageId) return "Page";

    return "Document";
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="p-0 gap-0 w-[95vw] max-w-xl rounded-lg shadow-xl border border-gray-200 overflow-hidden sm:w-full max-h-[90vh]" hideCloseBtn>
        <div className="flex items-center border-b px-2 sm:px-3 relative bg-gray-50">
          <MagnifyingGlassIcon className="mr-2 h-4 w-4 shrink-0 text-gray-500" />
          <Input className="flex h-10 sm:h-12 rounded-md border-0 bg-transparent py-1.5 sm:py-2 text-xs outline-none placeholder:text-gray-500 focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50"
            placeholder={`Search ${workspace?.workspace?.name || 'workspace'}...`}
            value={query} onChange={(e) => setQuery(e.target.value)} autoFocus />
        </div>

        <div className="max-h-[60vh] sm:max-h-[65vh] overflow-y-auto pr-1" ref={resultsContainerRef}>
          {isLoading && (
            <div className="fixed top-0 left-0 right-0 h-0.5 z-50">
              <div className="bg-primary-500/30 h-full animate-pulse" style={{ width: '100%' }}></div>
            </div>
          )}
          {isLoading && currentPage === 1 && results.length === 0 ? (
            <div className="px-4 sm:px-6 py-6 sm:py-8 text-center">
              <Loader className="inline-block w-5 h-5 sm:w-6 sm:h-6 text-gray-600 animate-spin" />
              <p className="mt-2 sm:mt-3 text-xs text-gray-600">Searching workspace...</p>
            </div>
          ) : error ? (
            <div className="px-4 sm:px-6 py-6 sm:py-8 text-center">
              <div className="mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gray-100 mb-2 sm:mb-3">
                <WifiSlashIcon className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
              </div>
              <h3 className="text-xs font-medium text-gray-900">Network Error</h3>
              <p className="mt-1 text-xs text-gray-500">Please check your internet connection and try again</p>
            </div>
          ) : !query.trim() ? (
            <div className="px-4 py-4">
              <h3 className="text-[9px] font-medium text-gray-400 uppercase tracking-tight px-2 py-0.5 bg-gray-50">Recent Searches</h3>
              {recentSearches.length > 0 ? (
                recentSearches.map((search, index) => (
                  <div key={index} className="flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-50 rounded transition-colors"
                    onClick={() => setQuery(search)}>
                    <div className="flex items-center">
                      <TimerIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-xs text-gray-700">{search}</span>
                    </div>
                    <button onClick={(e) => deleteRecentSearch(search, e)} className="p-1 rounded-full hover:bg-gray-100 transition-colors">
                      <XmarkIcon className="h-3 w-3 text-gray-500" />
                    </button>
                  </div>
                ))
              ) : <div className="px-2 py-3 text-xs text-gray-500">No recent searches</div>}
            </div>
          ) : results.length === 0 ? (
            <div className="px-4 sm:px-6 py-6 sm:py-8 text-center">
              <div className="mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-gray-100 mb-2 sm:mb-3">
                <MagnifyingGlassIcon className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
              </div>
              <h3 className="text-xs font-medium text-gray-900">No results found</h3>
              <p className="mt-1 text-xs text-gray-500">We couldn't find anything matching "{query}"</p>
            </div>
          ) : (
            Object.entries(groupResults(results)).map(([category, items]) =>
              items.length > 0 && (
                <div key={category}>
                  <h3 className="text-[9px] font-medium text-gray-400 uppercase tracking-tight px-2 sm:px-3 py-0.5 sm:py-1 bg-gray-50">{category}</h3>
                  {items.map((result: SearchResult) => (
                    <div key={result.id} className="px-2 sm:px-3 py-1.5 sm:py-2 cursor-pointer hover:bg-gray-50 transition-colors flex items-start gap-1.5 sm:gap-3"
                      onClick={() => handleResultClick(result)}>
                      {result.image ? (
                        <img src={result.image} alt={result.name || ''} className="h-6 w-6 sm:h-7 sm:w-7 rounded-full object-cover mt-1" />
                      ) : (
                        <div className="flex-shrink-0 mt-0.5 p-1 sm:p-1.5 rounded-md sm:rounded-lg bg-gray-100 text-gray-600">
                          {getIconForSource(result)}
                        </div>
                      )}
                      <div className="min-w-0 flex-1">
                        <div className="flex justify-between items-baseline">
                          <h3 className="text-xs font-medium text-gray-900 truncate">
                            <HighlightedContent
                              content={result.title || result.name}
                              highlight={result.highlight}
                              query={query}
                            />
                          </h3>
                          {result.publishedAt && (
                            <span className="text-xs text-gray-500 ml-2 whitespace-nowrap hidden sm:inline">
                              {formatDistanceToNow(new Date(result.publishedAt), { addSuffix: true })}
                            </span>
                          )}
                        </div>
                        {result.content && (
                          <p className="mt-0.5 sm:mt-1 text-xs text-gray-500 line-clamp-1 sm:line-clamp-2">
                            <HighlightedContent
                              content={result.content}
                              highlight={result.highlight}
                              query={query}
                            />
                          </p>
                        )}
                        {result.source && (
                          <div className="mt-1 sm:mt-1.5">
                            <span className="inline-flex items-center px-1 py-0.5 rounded text-[9px] font-medium bg-gray-100 text-gray-800">
                              {getResultType(result)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )
            )
          )}

          {isLoading && currentPage > 1 && (
            <div className="py-2 sm:py-3 text-center">
              <Loader className="inline-block w-3 h-3 sm:w-4 sm:h-4 text-gray-400 animate-spin" />
            </div>
          )}

          {/* Sentinel element for infinite scroll */}
          <div id="search-results-sentinel" className="h-4 w-full"></div>

          {!isLoading && results.length > 0 && (
            <div className="py-2 sm:py-3 text-center">
              {hasMore ? (
                <div className="text-xs text-gray-500">
                  Scroll for more results
                </div>
              ) : (
                <div className="text-xs text-gray-500">
                  {totalItems > 0 ? `Showing all ${totalItems} results` : 'No more results'}
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}