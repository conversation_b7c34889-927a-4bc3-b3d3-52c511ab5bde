import { CommentBody, CommentData, ThreadData, ThreadStoreAuth, YjsThreadStoreBase } from "@blocknote/core/comments";
import * as Y from "yjs";
import { buildBlocknoteComment, commentToYMap, threadToYMap, yArrayFindIndex, yMapToComment, yMapToThread } from "./storeHelper";
import { httpRequest } from "@/utils/http";
import { Method } from "axios";
import { BackendAPIResponse, normalizeResponse } from "@/api/common";
import { Comment } from "@/typings/comment";

export class CustomRestjsThreadStore extends YjsThreadStoreBase {
  constructor(
      private readonly BASE_URL: string,
      private readonly queryStr:string,
    private readonly headers: Record<string, string>,
    threadsYMap: Y.Map<any>,
      auth: ThreadStoreAuth,
    private readonly userId:string
  ) {
    super(threadsYMap, auth);
  }

    private doRequest = async (path: string, method: Method, body?: any) => {
      const headers =  {
        "Content-Type": "application/json",
        ...this.headers,
      }
       const endpoint = `${this.BASE_URL}${path}?${this.queryStr}`;
       const response = await httpRequest(method, endpoint, headers,body);
        const normalizedRez = normalizeResponse(response) as BackendAPIResponse<{ comment: ThreadData | CommentBody }>;
      
      
    if (!normalizedRez.isSuccess) {
      throw new Error(`Failed to ${method} ${path}: ${response.error}`);
        }
        
        return normalizedRez.data.data.comment
    };

//  public addThreadToDocument = undefined
    public addThreadToDocument = async (options: {
    threadId: string;
    selection: {
      prosemirror: {
        head: number;
        anchor: number;
      };
      yjs: {
        head: any;
        anchor: any;
      };
    };
  }) => {
    const { threadId, ...rest } = options;
    // return this.doRequest(`/${threadId}/addToDocument`, "POST", rest);
  };


  public createThread = async (options: {
    initialComment: {
      body: CommentBody;
      metadata?: any;
    };
    metadata?: any;
  }) => {
    const reqBody = {
      content: options.initialComment.body,
      commentedText: options.initialComment.metadata.commentedText,
      selection: options.initialComment.metadata.range
    }

    // console.log(reqBody)

    const thread = await this.doRequest("/", "POST", reqBody)

    // console.log({thread})
    // if (thread) {
    //   console.log("add to docsssssssssss+++++++++")
    //    await this.addThreadToDocument({threadId:thread.id,selection:reqBody.range})
    //  }

    return thread
   
  };

  public addComment = async (options: {
    comment: {
      body: CommentBody;
      metadata?: any;
    };
    threadId: string;
  }) => {
    const { threadId, ...rest } = options;
    const reqBody = {
      content: rest.comment.body,
      parentId: threadId,
      commentedText: rest.comment.metadata.commentedText,
    }
    
   return this.doRequest("/", "POST", reqBody)
    
  };

  public updateComment = async (options: {
    comment: {
      body: CommentBody;
      metadata?: any;
    };
    threadId: string;
    commentId: string;
  }) => {
    const { threadId, commentId, ...rest } = options;
    const reqBody = {
      content: rest.comment.body,
    }
    
    await this.doRequest(`/${commentId}`, "patch", reqBody);
    
  
  };

  public deleteComment = async (options: {
    threadId: string;
    commentId: string;
    softDelete?: boolean;
  }) => {
    const { threadId, commentId, ...rest } = options;
    
    await this.doRequest(`/${commentId}`, "DELETE");
    
  };

  public deleteThread = async (options: { threadId: string }) => {
    if (
      !this.auth.canDeleteThread(
        yMapToThread(this.threadsYMap.get(options.threadId)),
      )
    ) {
      throw new Error("Not authorized");
    }

    await this.doRequest(`/${options.threadId}`, "DELETE");
    
  };

  public resolveThread = async (options: { threadId: string }) => {
    const yThread = this.threadsYMap.get(options.threadId);
    if (!yThread) {
      throw new Error("Thread not found");
    }

    if (!this.auth.canResolveThread(yMapToThread(yThread))) {
      throw new Error("Not authorized");
    }

    await this.doRequest(`/${options.threadId}`, "patch", {isResolved: true});
    
  };

  public unresolveThread = async (options: { threadId: string }) => {
    const yThread = this.threadsYMap.get(options.threadId);
    if (!yThread) {
      throw new Error("Thread not found");
    }

    if (!this.auth.canUnresolveThread(yMapToThread(yThread))) {
      throw new Error("Not authorized");
    }

    await this.doRequest(`/${options.threadId}`, "patch", {isResolved: false});
    
  };

  public addReaction = async (options: {
    threadId: string;
    commentId: string;
    emoji: string;
  }) => {
    const { threadId, commentId, ...rest } = options;

    const yThread = this.threadsYMap.get(options.threadId);
    if (!yThread) {
      throw new Error("Thread not found");
    }

    const yCommentIndex = yArrayFindIndex(
      yThread.get("comments"),
      (comment) => comment.get("id") === options.commentId,
    );

    if (yCommentIndex === -1) {
      throw new Error("Comment not found");
    }

    const yComment = yThread.get("comments").get(yCommentIndex);
    const key = `${this.userId}-${options.emoji}`;
    const reactionsByUser = yComment.get("reactionsByUser");

    if (reactionsByUser.has(key)) {
      // already exists
      return;
    }

    await this.doRequest(`/${commentId}/add-reaction`, "patch", rest);
    

  };

  public deleteReaction = async (options: {
    threadId: string;
    commentId: string;
    emoji: string;
  }) => {
    const { threadId, commentId, ...rest } = options;
 

    await this.doRequest(`/${commentId}/delete-reaction`, "patch", rest);
  };

}