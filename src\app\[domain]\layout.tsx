"use client"

import {usePathname, useRouter} from "next/navigation";
import {BaseLayout} from "@/components/workspace/main/baseLayout";
import {WorkspaceProvider} from "@/providers/workspace";
import React, {useEffect} from "react";
import {useAuth} from "@/providers/user";
import {PageLoader} from "@/components/custom-ui/loader";
import {setRedirectLocation} from "@/api/auth";
import {HelpWidget} from "@/components/custom-ui/HelpWidget";
import {FCMProvider} from "@/providers/fcm";
import {NotificationModal} from "@/components/workspace/main/settings/notificationsSettings";
import {DataStorageProvider} from "@/providers/dataStorage";
import BillingProvider from "@/providers/billing";
import {PeekStackProvider} from "@/providers/peekStack";
import { CommentProvider } from "@/providers/comment";

const Layout = ({children}: { children: React.ReactNode }) => {
    const {isAuthenticated, error, user} = useAuth()
    const pathname = usePathname();
    const pathSplit = (pathname || "").trim().split("/");
    const page = pathSplit[2]

    const skipLayout = page === "setup" || page === "settings"

    const router = useRouter()
    const ready = isAuthenticated() || !!error

    useEffect(() => {
        if (ready && !isAuthenticated()) {
            setRedirectLocation(window.location.href)
            router.push("/auth/sign-in")
        }
    }, [isAuthenticated, ready, router]);

    useEffect(() => {
        if (ready && isAuthenticated()) {
            // setRedirectLocation(window.location.href)
            // router.push("/auth/sign-in")
        }
    }, [isAuthenticated, ready, router]);

    return (
        <>
            {(!ready || !isAuthenticated()) && <>
                <PageLoader size="screen"/>
            </>}
            {ready && user && isAuthenticated() && <>
                <FCMProvider>
                    <NotificationModal src={'layout'}/>
                    <WorkspaceProvider>
                        <DataStorageProvider>
                            <BillingProvider>
                                <PeekStackProvider>
                                    <CommentProvider>
                                        <BaseLayout showSidebar={!skipLayout}>
                                            {children}
                                        </BaseLayout>
                                    </CommentProvider>
                                </PeekStackProvider>
                            </BillingProvider>

                        </DataStorageProvider>
                    </WorkspaceProvider>
                </FCMProvider>

                <HelpWidget user={user}/>
            </>}
        </>
    )
};

export default Layout;