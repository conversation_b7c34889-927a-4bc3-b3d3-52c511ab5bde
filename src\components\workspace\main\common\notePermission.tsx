import { ShareNote } from '@/api/workspace';
import ShareWrapper from '@/components/custom-ui/share'
import { Button } from '@/components/ui/button';
import { useAuth } from '@/providers/user';
import { useWorkspace } from '@/providers/workspace';
import { AccessLevel } from '@/typings/page';
import { WorkspaceNote } from '@/typings/workspace';
import React, { useCallback } from 'react'
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { WorkspaceMemberRole } from "@/typings/workspace";
import { AccessLevelSelect } from '../pages/pagePermission';

const ShareNotePermission = ({note}:{note:WorkspaceNote}) => {
    const { workspace, membersMap, members } = useWorkspace()

    console.log(note.document.assignedToUserIds?.split(/[, ]+/), "***************")
    const shareNotesUsers = useCallback(() => {
        const perms = [
            membersMap[note.document.createdById],
            ...((note.document.assignedToUserIds
                ? note.document.assignedToUserIds.split(/[, ]+/).map(id => membersMap[id]).filter(Boolean)
                : []) as typeof membersMap[keyof typeof membersMap][])

        ]
        return perms
    }, [note, membersMap])
    const {token} = useAuth()
        const handleShareNote = async (emails: string, accessLevel: AccessLevel) => {
            if (!token) return;
            const emailList = emails.split(",").map(email => email.trim()).filter(email => email);
            const response = await ShareNote(token.token, workspace.workspace.id, { id: note.document.id, emails: emailList, accessLevel })
            if (response.isSuccess) {
                toast.success("Note shared!")
            } else {
                toast.error(response.error)
                console.log(response.error)
            }
        }

    // const membersWithNotesPermission = note.document.assignedUserIds
  return (
    <ShareWrapper onShare={handleShareNote} showFooter
                                   trigger={
                                       <Button variant="outline"
                                           className=" size-6 p-1.5  mr-2 relative -top-1 text-xs px-3 w-auto rounded-full font-semibold shadow-none ">
                                           Share
                                       </Button>
                                   } >
          
           {workspace &&
                    <div className="p-3 overflow-y-auto h-auto max-h-96 border-t">
                        <div className="flex flex-col gap-2">
                            <div className="flex gap-3 items-center select-none">
                                <Avatar
                                    className="size-7 items-center justify-center group-hover:hidden rounded-sm">
                                    <AvatarImage src={workspace.workspace.logo} />
                                    <AvatarFallback>{workspace.workspace.name[0]}</AvatarFallback>
                                </Avatar>
                                <div className="flex-1 overflow-hidden">
                                    <div className="w-full overflow-hidden">
                                        <div className="w-full max-w-full truncate font-medium text-xs">
                                            Everyone at {workspace.workspace.name}
                                        </div>
                                    </div>
                                </div>
                                {/* <AccessLevelSelect
                                    accessLevel={page.accessLevel}
                                    onChange={(accessLevel) => updatePage({ accessLevel })}
                                    revocable
                                    onRevoke={() => updatePage({ visibility: Visibility.Private })}
                                /> */}
                            </div>

                            {/* {permissions.filter(p => !!membersMap[p.userId]).map((p, i) => { */}
                      {shareNotesUsers().map((p, i) => {      
                      const member = membersMap[p.user.id]
                                const name = `${member.user.firstName} ${member.user.lastName}`.trim()
                                return (
                                    <div key={i} className="flex gap-3 items-center select-none">
                                        <Avatar className="size-7 items-center justify-center group-hover:hidden">
                                            <AvatarImage src={member.user.profilePhoto} />
                                            <AvatarFallback>{name[0] || member.user.email[0]}</AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1 overflow-hidden">
                                            <div className="w-full overflow-hidden">
                                                <div className="w-full max-w-full truncate font-medium text-sm">
                                                    {name}
                                                    {member.user.id === workspace.workspaceMember.userId &&
                                                        <Badge variant='secondary'
                                                            className="text-[10px] bg-neutral-200 leading-tight p-0.5 px-1 ml-1 rounded-full">You</Badge>}
                                                </div>
                                                <div
                                                    className="w-full max-w-full font-medium truncate text-xs text-[11px] text-muted-foreground">
                                                    {member.user.email}
                                                    {member.workspaceMember.role === WorkspaceMemberRole.Collaborator && <> • <span
                                                        className='capitalize'>Guest</span></>}
                                                </div>
                                            </div>
                                        </div>

                                        <AccessLevelSelect
                                            accessLevel={AccessLevel.Full}
                                            revocable
                                            onChange={(accessLevel) => {}}
                                            onRevoke={() => {}}
                                        />
                                    </div>
                                )
                            })}
                        </div>

                    </div>}
                                   </ShareWrapper>
   
  )
}

export default ShareNotePermission