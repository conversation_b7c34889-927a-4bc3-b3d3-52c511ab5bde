import React, { useCallback } from 'react';
import { toast } from 'sonner';

import { RemoveNotePermssion, ShareNote } from '@/api/workspace';
import ShareWrapper from '@/components/custom-ui/share';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

import { useAuth } from '@/providers/user';
import { useWorkspace } from '@/providers/workspace';

import { WorkspaceNote } from '@/typings/workspace';
import { AccessLevel, PagePermission } from '@/typings/page';
import { WorkspaceMemberRole } from '@/typings/workspace';
import { AccessLevelSelect } from '../pages/pagePermission';
import { SharedNoteWith } from './workspaceNotes';


interface NotePermissionUsers {
    [userId: string]: SharedNoteWith;
}

interface ShareNotePermissionProps {
    note: WorkspaceNote;
    notePermissions: PagePermission[];
    sharedNotesMembers: NotePermissionUsers;
    onPermissionsChange: React.Dispatch<React.SetStateAction<PagePermission[]>>;
    onMembersChange: React.Dispatch<React.SetStateAction<NotePermissionUsers>>;
}

const ShareNotePermission: React.FC<ShareNotePermissionProps> = ({
    note,
    notePermissions,
    sharedNotesMembers,
    onPermissionsChange,
    onMembersChange
}) => {
    const { token } = useAuth();
    const { workspace, membersMap } = useWorkspace();

// Get members who have permission to view/edit the note
    const shareNotesUsers = useCallback(() => {
        // Get users from shared permissions
        const sharedUsers = Object.values(sharedNotesMembers).map(sharedNote => ({
            user: sharedNote.user,
            accessLevel: sharedNote.accessLevel,
            workspaceMember: membersMap[sharedNote.user.id]?.workspaceMember
        }));

        // Get creator
        const createdByMember = membersMap[note.document.createdById];
        if (createdByMember) {
            const creatorInShared = sharedUsers.find(u => u.user.id === note.document.createdById);
            if (!creatorInShared) {
                sharedUsers.push({
                    user: createdByMember.user,
                    accessLevel: AccessLevel.Full,
                    workspaceMember: createdByMember.workspaceMember
                });
            }
        }

        // Get assigned members
        const assignedMembers = note.document.assignedToUserIds
            ? note.document.assignedToUserIds
                .split(/[, ]+/)
                .map((id) => {
                    const member = membersMap[id];
                    if (!member) return null;

                    const assignedInShared = sharedUsers.find(u => u.user.id === id);
                    if (assignedInShared) return null; // Already in shared users

                    return {
                        user: member.user,
                        accessLevel: AccessLevel.Full,
                        workspaceMember: member.workspaceMember
                    };
                })
                .filter(Boolean)
            : [];

        return [...sharedUsers, ...assignedMembers];
    }, [note, sharedNotesMembers, membersMap]);

    // Share note with other users via email + access level
    const handleShareNote = async (emails: string, accessLevel: AccessLevel) => {
        if (!token) return;

        const emailList = emails
            .split(',')
            .map((email) => email.trim())
            .filter((email) => email);

        const response = await ShareNote(token.token, workspace.workspace.id, {
            id: note.document.id,
            emails: emailList,
            accessLevel,
        });

        if (response.isSuccess) {
            toast.success('Note shared!');
            // The socket will handle updating the permissions state
        } else {
            toast.error(response.error);
            console.error(response.error);
        }
    };

    // Handle access level change for a user
    const handleAccessLevelChange = async (userId: string, newAccessLevel: AccessLevel) => {
        if (!token) return
        try {

            // const res = await RemoveNotePermssion(token.token, workspace.workspace.id, note.document.id, { userId, accessLevel: "none" })

            // onPermissionsChange(prev =>
            //     prev.map(perm =>
            //         perm.userId === userId
            //             ? { ...perm, accessLevel: newAccessLevel }
            //             : perm
            //     )
            // );

            // onMembersChange(prev => ({
            //     ...prev,
            //     [userId]: {
            //         ...prev[userId],
            //         accessLevel: newAccessLevel
            //     }
            // }));
        } catch (error) {
            console.log(error)
        }
    };

    // Handle permission revocation
    const handleRevokePermission = async (userId: string) => {
        if (!token) return
        try {

            const res = await RemoveNotePermssion(token.token, workspace.workspace.id, note.document.id, { userId, accessLevel: "none" })

            // onPermissionsChange(prev => prev.filter(perm => perm.userId !== userId));

            // Remove from shared members
            // onMembersChange(prev => {
            //     const updated = { ...prev };
            //     delete updated[userId];
            //     return updated;
            // });
        } catch (error) {
            console.log(error)
        }
    };

    return (
        <ShareWrapper
            onShare={handleShareNote}
            showFooter
            trigger={
                <Button
                    variant="outline"
                    className="size-6 p-1.5 mr-2 relative -top-1 text-xs px-3 w-auto rounded-full font-semibold shadow-none"
                >
                    Share
                </Button>
            }
        >
            {workspace && (
                <div className="p-3 overflow-y-auto h-auto max-h-96 border-t">
                    <div className="flex flex-col gap-2">
                        {/* Everyone in workspace */}
                        <div className="flex gap-3 items-center select-none">
                            <Avatar className="size-7 items-center justify-center rounded-sm">
                                <AvatarImage src={workspace.workspace.logo} />
                                <AvatarFallback>
                                    {workspace.workspace.name[0]}
                                </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 overflow-hidden">
                                <div className="w-full overflow-hidden">
                                    <div className="w-full max-w-full truncate font-medium text-xs">
                                        Everyone at {workspace.workspace.name}
                                    </div>
                                </div>
                            </div>
                            {/* Optional Access Level control for everyone */}
                            {/* <AccessLevelSelect ... /> */}
                        </div>

                        {/* Individual users with access */}
                        {shareNotesUsers().map((userInfo, i) => {
                            if (!userInfo) return null;

                            const { user, accessLevel, workspaceMember } = userInfo;
                            const name = `${user.firstName} ${user.lastName}`.trim();
                            const isCreator = user.id === note.document.createdById;
                            const isCurrentUser = user.id === workspace.workspaceMember.userId;

                            return (
                                <div
                                    key={`${user.id}-${i}`}
                                    className="flex gap-3 items-center select-none"
                                >
                                    <Avatar className="size-7 items-center justify-center">
                                        <AvatarImage src={user.profilePhoto} />
                                        <AvatarFallback>{name[0] || user.email[0]}</AvatarFallback>
                                    </Avatar>

                                    <div className="flex-1 overflow-hidden">
                                        <div className="w-full overflow-hidden">
                                            <div className="w-full max-w-full truncate font-medium text-sm">
                                                {name}
                                                {isCurrentUser && (
                                                    <Badge
                                                        variant="secondary"
                                                        className="text-[10px] bg-neutral-200 leading-tight p-0.5 px-1 ml-1 rounded-full"
                                                    >
                                                        You
                                                    </Badge>
                                                )}
                                            </div>
                                            <div className="w-full max-w-full font-medium truncate text-xs text-muted-foreground">
                                                {user.email}
                                                {workspaceMember?.role === WorkspaceMemberRole.Collaborator && (
                                                    <>
                                                        {' '}
                                                        • <span className="capitalize">Guest</span>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    <AccessLevelSelect
                                        accessLevel={accessLevel || AccessLevel.Full}
                                        revocable={!isCreator}
                                        onChange={(newAccessLevel) => handleAccessLevelChange(user.id, newAccessLevel)}
                                        onRevoke={() => handleRevokePermission(user.id)}
                                    />
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}
        </ShareWrapper>
    );
};

export default ShareNotePermission;