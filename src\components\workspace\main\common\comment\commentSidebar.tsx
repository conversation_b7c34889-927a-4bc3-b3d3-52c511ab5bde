
"use client";
import { useEffect, useMemo, useState,useCallback } from "react";
import * as Y from "yjs";
import {  Block, BlockNoteEditor } from "@blocknote/core";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MessageSquare, Check, Edit3, CheckCircle, XCircle, Clock, Plus, Minus, Replace } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/providers/user";
import { useWorkspace } from "@/providers/workspace";
import { toast } from "sonner";
import { CommentThread } from "./commentThread";
import { useComments } from "@/providers/comments";

import { Ch<PERSON>ronDown, Bell } from "lucide-react"

import * as React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FilterIcon } from "@/components/icons/FontAwesomeRegular";
import { cn } from "@/lib/utils";
import { NotificationChannelSetting, NotificationSettings, SettingsType } from "@/typings/user";
import { getSettings, updateSettings } from "@/api/account";
import { getNotes, GetNotesParams, updateDocumentNotification, updateNote } from "@/api/workspace";
import { DocumentNotificationType } from "@/typings/page";
import { PageLoader } from "@/components/custom-ui/loader";
import { useRecord } from "@/providers/record";
import { updateRecordNotification } from "@/api/database";


interface Suggestion {
  id: string;
  type: "insert" | "delete" | "replace";
  originalText?: string;
  suggestedText?: string;
  author: string;
  timestamp: Date;
  accepted?: boolean;
  rejected?: boolean;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  role: "editor" | "viewer";
  color: string;
  profilePhoto?: string;
}

interface CommentSidebarProps {
  recordId?: string;
  databaseId?: string;
  documentId?: string;
  className?:string;
}





export function CommentSidebar(props: CommentSidebarProps) {
  
 
  const { user,token } = useAuth();
  const { members, workspace, databaseStore, updateDatabaseRecordValues } = useWorkspace();
  const { filteredThreads, filterComments, commentFilter, threads, editor, isLoading: isCommentsLoading } = useComments()
  const [activeThread, setActiveThread] = useState<string | null>(null)
  // editor?._tiptapEditor.commands.setMark()
  const [isLoading, setLoading] = useState(true)
  const [error, setError] = useState("")

  const [commentSettings, setCommentSettings] = useState<DocumentNotificationType>(DocumentNotificationType.RepliesAndMentions)
  const [settings, setSettings] = useState<DocumentNotificationType | null>(null)




  const loadSettings = useCallback(async () => {
    if (!token || !user) return
    setError("")
    setLoading(true)

    const params: GetNotesParams = {

    }
    try {
      if (props.recordId && props.databaseId) {

      const dbStore = databaseStore[props.databaseId]
      const recordInfo = dbStore?.recordsIdMap?.[props.recordId]
      console.log({ recordInfo })
      const data = recordInfo.record.userNotificationSettings
      if (data && data[user.id]) {
        const allComments = data[user.id] === DocumentNotificationType.AllComments
        const replisAndMention = data[user.id] === DocumentNotificationType.RepliesAndMentions
        allComments && setCommentSettings(DocumentNotificationType.AllComments)
        replisAndMention && setCommentSettings(DocumentNotificationType.RepliesAndMentions)
        setSettings(data[user.id])
    } else {

        await saveSettings({
          notification: DocumentNotificationType.RepliesAndMentions
        })
      }
    } else {
      // load from document
      params.type = 'user'
      params.noteId = props.documentId
    }

      const res = await getNotes(token.token, workspace.workspace.id, params)
       setLoading(false)
      const data = res.data.data.notes.find(note => note.document.id === props.documentId)?.document.userNotificationSettings
    if (data && data[user.id]) {
      const allComments = data[user.id] === DocumentNotificationType.AllComments
      const replisAndMention = data[user.id] === DocumentNotificationType.RepliesAndMentions
      allComments && setCommentSettings(DocumentNotificationType.AllComments)
      replisAndMention && setCommentSettings(DocumentNotificationType.RepliesAndMentions)
      setSettings(data[user.id])
    } else {

      await saveSettings({
        notification: DocumentNotificationType.RepliesAndMentions
      })
    }
      console.log("Settings:", res.data.data.notes.find(note => note.document.id === props.documentId)?.document)

    } catch (error) {
      console.log(error)
    }

  }, [token])




  const saveSettings = async ({ notification }: { notification: DocumentNotificationType }) => {
    if (!token || !user) return

    if (props.documentId) {
      const res = await updateDocumentNotification(token.token, workspace.workspace.id, { notification, documentId: props.documentId })
      if (res.error) {
        toast.error(res.error)
        return
      }
      setSettings(notification)
    } else if (props.databaseId && props.recordId) {
      const res = await updateRecordNotification(token.token, workspace.workspace.id, props.databaseId, { notification, id: props.recordId })
    if (res.error) {
      toast.error(res.error)
      return
    }
      const userId = user!.id
      const userNotification = { userId: notification }
      updateDatabaseRecordValues(props.databaseId, [props.recordId], {}, {}, {}, false, userNotification)
    setSettings(notification)
    } else {
      return
    }
    toast.success("Notification settings updated")
  }

  useEffect(() => {
    loadSettings().then()
  }, [loadSettings]);

  if (isCommentsLoading && filteredThreads.length === 0) {
    return <div className={cn("flex flex-col h-full", props.className)}>
      <PageLoader size='full' />
    </div>
  }

  return (
    <div className={cn("flex flex-col h-full", props.className)}>
      <div className="flex-shrink-0 p-4 space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-sm font-semibold">Comments & suggestions</h2>
          <CommentFilterDropdown
            filterComments={filterComments}
            activeFilter={commentFilter}
            trigger={
              <button className="size-4 bg-transparent hover:bg-gray-100 rounded">
                <FilterIcon className="w-3 h-3" />
              </button>
            }
          />
        </div>
        {settings &&
          (<div>
          <p className="text-xs font-semibold text-muted-foreground mb-0.5">Notify me for</p>

          <NotificationSettingsDropdown
            value={commentSettings}
            onValueChange={(val) =>

              saveSettings({
                notification: val
              })
            }
          />

          </div>)
        }
      </div>


      <div className="flex-1 min-h-0 overflow-hidden">
        <ScrollArea className="h-full px-4 py-4">
            {filteredThreads.length === 0 || !editor ? (
              <div className="p-6 text-center text-muted-foreground">
                <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No comments yet</p>
                <p className="text-xs mt-1">Select text in the editor to add comments</p>
              </div>
            ) : (
              <div className="space-y-4 ">
                  {filteredThreads.length > 0 && (
                  <div>

                      <div className="space-y-3 w-full">
                        {filteredThreads.map((thread, idx) => (
                          <div key={idx} onClick={() => {
                            setActiveThread(thread.id)
                          }}>
                            <CommentThread
                              thread={thread}
                              user={user!}
                              workspaceMembers={members}

                            />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            
              </div>
            )}
          </ScrollArea>
        </div>


    </div>
  );
}




export function CommentFilterDropdown({
  filterComments,
  activeFilter,
  trigger,
}: {
  filterComments: (filter: {
    type?: "all" | "comments" | "suggestions"
    status?: "all" | "resolved" | "unresolved"
    authorId?: string
  }) => void
  activeFilter: {
    type: "all" | "comments" | "suggestions"
    status: "all" | "resolved" | "unresolved"
    userId?: string
  }
    trigger: React.ReactNode
}) {
  const { members } = useWorkspace()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="text-xs w-56">
        <DropdownMenuLabel className="text-muted-foreground text-xs">Filter by Type</DropdownMenuLabel>
        <DropdownMenuGroup>
          <DropdownMenuItem
            className={`text-sm flex items-center justify-between `}
            onClick={() => filterComments({ type: "all" })}
          >
            All
            {activeFilter.type === "all" && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
          <DropdownMenuItem
            className={`text-sm flex items-center justify-between`}
            onClick={() => filterComments({ type: "comments" })}
          >
            Comments Only
            {activeFilter.type === "comments" && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
          <DropdownMenuItem
            className={`text-sm flex items-center justify-between `}
            onClick={() => filterComments({ type: "suggestions" })}
          >
            Suggestions Only
            {activeFilter.type === "suggestions" && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-muted-foreground text-xs">Filter by Status</DropdownMenuLabel>
        <DropdownMenuGroup>
          {/* <DropdownMenuItem
            className={`text-sm flex items-center justify-between `}
            onClick={() => filterComments({ status: "all" })}
          >
            All
            {activeFilter.status === "all" && <Check className="h-4 w-4" />}
          </DropdownMenuItem> */}
          <DropdownMenuItem
            className={`text-sm flex items-center justify-between `}
            onClick={() => filterComments({ status: "unresolved" })}
          >
            Open
            {activeFilter.status === "unresolved" && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
          <DropdownMenuItem
            className={`text-sm flex items-center justify-between `}
            onClick={() => filterComments({ status: "resolved" })}
          >
            Resolved
            {activeFilter.status === "resolved" && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-muted-foreground text-xs">Filter by person</DropdownMenuLabel>

        {/* Scrollable members section */}
        <ScrollArea className="h-32">
          <DropdownMenuGroup>
            {members.map((member, idx) => {
              const user = member.user
              const isSelected = activeFilter.userId === user.id
              return (
                <DropdownMenuItem
                  key={idx}
                  className={`text-sm flex items-center justify-between `}
                  onClick={() => filterComments({ authorId: user.id })}
                >
                  {user.firstName + " " + user.lastName}
                  {isSelected && <Check className="h-4 w-4" />}
                </DropdownMenuItem>
              )
            })}
          </DropdownMenuGroup>
        </ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Demo component to show the dropdown in action
export default function Component() {
  const [activeFilter, setActiveFilter] = React.useState({
    type: "all" as "all" | "comments" | "suggestions",
    status: "all" as "all" | "resolved" | "unresolved",
    userId: undefined as string | undefined,
  })

  const filterComments = (filter: {
    type?: "all" | "comments" | "suggestions"
    status?: "all" | "resolved" | "unresolved"
    authorId?: string
  }) => {
    setActiveFilter((prev) => ({
      ...prev,
      ...filter,
      userId: filter.authorId || prev.userId,
    }))
  }

  return (
    <div className="p-8">
      <h2 className="text-lg font-semibold mb-4">Comment Filter Dropdown Demo</h2>
      <CommentFilterDropdown
        filterComments={filterComments}
        activeFilter={activeFilter}
        trigger={
          <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
            Filter Comments
          </button>
        }
      />
      <div className="mt-4 p-4 bg-muted rounded-md">
        <h3 className="font-medium mb-2">Current Filter:</h3>
        <pre className="text-sm">{JSON.stringify(activeFilter, null, 2)}</pre>
      </div>
    </div>
  )
}



interface NotificationOption {
  value: DocumentNotificationType
  label: string
  description?: string
}

interface NotificationSettingsDropdownProps {
  value?: DocumentNotificationType
  onValueChange?: (value: DocumentNotificationType) => void
  disabled?: boolean
  className?: string
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg"
  showIcon?: boolean
  placeholder?: string
}

const defaultOptions: NotificationOption[] = [
  {
    value: DocumentNotificationType.AllComments,
    label: "All comments",
    description: "Get notified for all comments and activity",
  },
  {
    value: DocumentNotificationType.RepliesAndMentions,
    label: "Mentions and replies",
    description: "Only get notified when mentioned or replied to",
  },
]

export function NotificationSettingsDropdown({
  value = DocumentNotificationType.RepliesAndMentions,
  onValueChange,
  disabled = false,
  className,
  variant = "outline",
  size = "default",
  showIcon = true,
  placeholder = "Select notification setting",
}: NotificationSettingsDropdownProps) {
  const [selectedValue, setSelectedValue] = useState<DocumentNotificationType>(value)

  const handleValueChange = (newValue: DocumentNotificationType) => {
    setSelectedValue(newValue)
    onValueChange?.(newValue)
  }

  const selectedOption = defaultOptions.find((option) => option.value === selectedValue)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          disabled={disabled}
          className={cn("justify-between min-w-[200px] w-full", className)}
        >
          <div className="flex items-center gap-2">
            {showIcon && <Bell className="h-4 w-4" />}
            <span className="truncate">{selectedOption?.label || placeholder}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[280px]">
        {defaultOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleValueChange(option.value)}
            className="flex items-start gap-3 p-3 cursor-pointer"
          >
            <div className="flex items-center justify-center w-4 h-4 mt-0.5">
              {selectedValue === option.value && <Check className="h-4 w-4 text-primary" />}
            </div>
            <div className="flex-1 space-y-1">
              <div className="font-medium text-sm">{option.label}</div>
              {option.description && <div className="text-xs text-muted-foreground">{option.description}</div>}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
