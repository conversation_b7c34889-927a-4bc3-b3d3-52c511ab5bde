
"use client";
import { useEffect, useMemo, useState,useCallback } from "react";
import * as Y from "yjs";
import {  Block, BlockNoteEditor } from "@blocknote/core";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MessageSquare, Check, Edit3, CheckCircle, XCircle, Clock, Plus, Minus, Replace } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/providers/user";
import { useWorkspace } from "@/providers/workspace";
import { FloatingCommentPopup } from "./commentPopup";
import { Select } from "@/components/ui/select";
import { SelectTrigger } from "@/components/custom-ui/select";
import { defaultAPIMessage, ItemDataLoad } from "@/api/common";
import { toast } from "sonner";
import { Comment, CommentEnum, Thread } from "@/typings/comment";
import { CommentThread } from "./commentThread";
import { useComments } from "@/providers/comment";

import { ChevronDown, Bell } from "lucide-react"


import * as React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FilterIcon } from "@/components/icons/FontAwesomeRegular";
import { cn } from "@/lib/utils";
import { NotificationChannelSetting, NotificationSettings, SettingsType } from "@/typings/user";
import { getSettings, updateSettings } from "@/api/account";


interface Suggestion {
  id: string;
  type: "insert" | "delete" | "replace";
  originalText?: string;
  suggestedText?: string;
  author: string;
  timestamp: Date;
  accepted?: boolean;
  rejected?: boolean;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  role: "editor" | "viewer";
  color: string;
  profilePhoto?: string;
}

interface CommentSidebarProps {
  recordId?: string;
  databaseId?: string;
  documentId?:string;
}





export function CommentSidebar(props: CommentSidebarProps) {
  
 
  const { user,token } = useAuth();
  const { members, workspace } = useWorkspace();
  const { filteredThreads, filterComments, commentFilter, threads, editor } = useComments()
  const [activeThread, setActiveThread] = useState<string | null>(null)

  const [isLoading, setLoading] = useState(true)
  const [error, setError] = useState("")

  const [commentSettings, setCommentSettings] = useState<NotificationSetting>("all")

  const [settings, setSettings] = useState<NotificationSettings | null>(null)

  console.log("filteredThreads", filteredThreads)
  console.log("threads", threads)
  const perPage = 24


  const loadSettings = useCallback(async () => {
    if (!token) return
    setError("")
    setLoading(true)
    const res = await getSettings(token.token)
    setLoading(false)
    if (res.error) {
      setError(res.error || defaultAPIMessage)
      return;
    }
    const data = res.data.data.settings.notification
    setSettings(data)
    data.email.notifyOn.replies && setCommentSettings('mentions')
    data.email.notifyOn.comment && setCommentSettings('all')

    console.log("Settings:", res.data.data.settings.notification)
  }, [token])

  const saveChannelSetting = (channel: string, channelSetting: NotificationChannelSetting) => {
    const update = { ...settings }
    update[`${channel}`] = channelSetting
    saveSettings(update).then()
  }


  const saveSettings = async (partial: Partial<NotificationSettings>) => {
    if (!token || !settings) return
    const update = { ...settings, ...partial } as NotificationSettings
    const res = await updateSettings(token.token, SettingsType.Notification, update)
    if (res.error) {
      toast.error(res.error)
      return
    }
    setSettings(update)
    toast.success("Notification settings updated")
  }

  useEffect(() => {
    loadSettings().then()
  }, [loadSettings]);

  return (
    <div className="p-4 space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-sm font-semibold">Comments & suggestions</h2>
        <CommentFilterDropdown filterComments={filterComments} activeFilter={commentFilter} trigger={
          <button className="size-4 bg-transparent" >
            <FilterIcon className="w-3 h-3"/>
            </button>
      }/>
      </div>
      {settings && <NotificationSettingsDropdown value={commentSettings} onValueChange={(val) =>
        saveChannelSetting('email', {
          ...settings.email, notifyOn: {
            ...settings.email.notifyOn,
            comment: val === "all",
            replies: val === "mentions",
            // mention: val === "mentions",
          }
        })

      } />}
     
      <div >
      <div className="mt-0">
          <ScrollArea className="min-h-[300px]">
            {filteredThreads.length === 0 ? (
              <div className="p-6 text-center text-muted-foreground">
                <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No comments yet</p>
                <p className="text-xs mt-1">Select text in the editor to add comments</p>
              </div>
            ) : (
              <div className="space-y-4 ">
                  {filteredThreads.length > 0 && (
                  <div>
                    {/* <div className="flex items-center gap-2 mb-3">
                      <Clock className="w-4 h-4 text-yellow-300" />
                      <span className="text-sm font-medium">Open ({filteredThreads.length})</span>
                    </div> */}
                      <div className="space-y-3 w-full">
                        {filteredThreads.map((thread, idx) => (
                          <div key={idx} onClick={() => {
                            setActiveThread(thread.id)
                          }}>
                            <CommentThread
                              thread={thread}
                              user={user!}
                              workspaceMembers={members}
                              // isSelected={thread.id === selectedThreadId}
                              // className={cn(activeThread === thread.id && "bg-muted-foreground", "hover:bg-muted-forground")}
                              // selectedText={selectedText ? decodeURIComponent(selectedText) : ""}
                            />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            
              </div>
            )}
          </ScrollArea>
        </div>

     
      </div>

    </div>
  );
}



export function CommentFilterDropdown({
  filterComments,
  activeFilter,
  trigger,
}: {
  filterComments: (filter: {
    type?: "all" | "comments" | "suggestions";
    status?: "all" | "resolved" | "unresolved";
    authorId?: string;
  }) => void;
  activeFilter: {
    type: "all" | "comments" | "suggestions";
    status: "all" | "resolved" | "unresolved";
    userId?: string;
  };
  trigger: React.ReactNode;
}) {
  const { members } = useWorkspace();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="text-xs">
        <DropdownMenuLabel className="text-muted-foreground text-xs">
          Filter by Type
        </DropdownMenuLabel>
        <DropdownMenuGroup>
          <DropdownMenuItem
            className={`text-sm ${activeFilter.type === "all" ? "font-bold bg-muted" : ""}`}
            onClick={() => filterComments({ type: "all" })}
          >
            All
          </DropdownMenuItem>
          <DropdownMenuItem
            className={`text-sm ${activeFilter.type === "comments" ? "font-bold bg-muted" : ""}`}
            onClick={() => filterComments({ type: "comments" })}
          >
            Comments Only
          </DropdownMenuItem>
          <DropdownMenuItem
            className={`text-sm ${activeFilter.type === "suggestions" ? "font-bold bg-muted" : ""}`}
            onClick={() => filterComments({ type: "suggestions" })}
          >
            Suggestions Only
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuLabel className="text-muted-foreground text-xs">
          Filter by Status
        </DropdownMenuLabel>
        <DropdownMenuGroup>
          <DropdownMenuItem
            className={`text-sm ${activeFilter.status === "all" ? "font-bold bg-muted" : ""}`}
            onClick={() => filterComments({ status: "all" })}
          >
            All
          </DropdownMenuItem>
          <DropdownMenuItem

            className={`text-sm ${activeFilter.status === "unresolved" ? "font-bold bg-muted" : ""}`}
            onClick={() => filterComments({ status: "unresolved" })}
          >
            Open
          </DropdownMenuItem>
          <DropdownMenuItem
            className={`text-sm ${activeFilter.status === "resolved" ? "font-bold bg-muted" : ""}`}
            onClick={() => filterComments({ status: "resolved" })}
          >
            Resolved
          </DropdownMenuItem>
          <DropdownMenuSeparator />
        </DropdownMenuGroup>
        <DropdownMenuLabel className="text-muted-foreground text-xs">
          Filter by person
        </DropdownMenuLabel>
        <DropdownMenuGroup>
          {members.map((member, idx) => {
            const user = member.user;
            const isSelected = activeFilter.userId === user.id;
            return (
              <DropdownMenuItem
                key={idx}
                className={`text-sm ${isSelected ? "font-bold bg-muted" : ""}`}
                onClick={() => filterComments({ authorId: user.id })}
              >
                {user.firstName + " " + user.lastName}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export type NotificationSetting = "all" | "mentions"

interface NotificationOption {
  value: NotificationSetting
  label: string
  description?: string
}

interface NotificationSettingsDropdownProps {
  value?: NotificationSetting
  onValueChange?: (value: NotificationSetting) => void
  disabled?: boolean
  className?: string
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg"
  showIcon?: boolean
  placeholder?: string
}

const defaultOptions: NotificationOption[] = [
  {
    value: "all",
    label: "All comments",
    description: "Get notified for all comments and activity",
  },
  {
    value: "mentions",
    label: "Mentions and replies",
    description: "Only get notified when mentioned or replied to",
  },
]

export function NotificationSettingsDropdown({
  value = "all",
  onValueChange,
  disabled = false,
  className,
  variant = "outline",
  size = "default",
  showIcon = true,
  placeholder = "Select notification setting",
}: NotificationSettingsDropdownProps) {
  const [selectedValue, setSelectedValue] = useState<NotificationSetting>(value)

  const handleValueChange = (newValue: NotificationSetting) => {
    setSelectedValue(newValue)
    onValueChange?.(newValue)
  }

  const selectedOption = defaultOptions.find((option) => option.value === selectedValue)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          disabled={disabled}
          className={cn("justify-between min-w-[200px] w-full", className)}
        >
          <div className="flex items-center gap-2">
            {showIcon && <Bell className="h-4 w-4" />}
            <span className="truncate">{selectedOption?.label || placeholder}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[280px]">
        {defaultOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleValueChange(option.value)}
            className="flex items-start gap-3 p-3 cursor-pointer"
          >
            <div className="flex items-center justify-center w-4 h-4 mt-0.5">
              {selectedValue === option.value && <Check className="h-4 w-4 text-primary" />}
            </div>
            <div className="flex-1 space-y-1">
              <div className="font-medium text-sm">{option.label}</div>
              {option.description && <div className="text-xs text-muted-foreground">{option.description}</div>}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
