import {Action, Property} from "@opendashboard-inc/integration-core";
import {DbCondition} from "opendb-app-db-utils/src/typings/db";
import { sendDirectEmail } from "@/api/workspace";
import { generateUUID } from "opendb-app-db-utils/lib/methods/string";


export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  profilePhoto?: string;
}

export interface WorkspaceMember {
  user: User;
  role?: string;
  permissions?: string[];
}

export interface WorkspaceData {
  id: string;
  name?: string;
  domain?: string;
  [key: string]: string | undefined;
}

export interface DatabaseData {
  id: string;
  name?: string;
  definition?: {
    columnsMap?: object;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export interface RecordData {
  id: string;
  recordValues?: Record<string, any>;
  values?: Record<string, any>;
  record?: {
    recordValues?: Record<string, any>;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export interface TokenData {
  token?: string;
  sub?: string;
  userId?: string;
}

export interface ActionContext {
  record: RecordData;
  database?: DatabaseData;
  workspace?: {
    workspace?: WorkspaceData;
    id?: string;
    domain?: string;
  };
  token?: TokenData;
  user?: User;
  meta?: {
    databaseId?: string;
    [key: string]: unknown;
  };
  databaseId?: string;
  parentRecord?: {
    id: string;
    databaseId: string;
  };
  databaseStore?: any;
  originalActionProps?: Record<string, any>;
}

export interface ActionServices {
  updateRecordValues: (databaseId: string, recordIds: string[], values: Record<string, any>) => Promise<boolean>;
  deleteRecords: (databaseId: string, ids: string[]) => Promise<void>;
  setPeekRecord?: (recordId: string, databaseId: string) => void;
  pushPeek?: (recordId: string, databaseId: string) => void;
  confirm: (
    title: string, 
    message: string, 
    onConfirm: () => void, 
    onCancel?: () => void, 
    options?: object, 
    isDangerAction?: boolean,
    confirmButtonText?: string,
    cancelButtonText?: string
  ) => void;
  toast: {
    success: (message: string) => void;
    error: (message: string) => void;
    warning: (message: string) => void;
    info: (message: string) => void;
  };
  router: {
    push: (path: string) => void;
  };
  forceRender: () => void;
  sendMessage: (message: string) => void;
  showInputDialog: (title: string, message: string, callback: (value: string) => void) => void;
  directDeleteRecords?: (databaseId: string, ids: string[]) => Promise<void>;
}


export interface ButtonAction<TInput = object, TOutput = object> 
  extends Omit<Action<any, any>, 'run' | 'test'> {
  visibleIf?: DbCondition[];
  enabledIf?: DbCondition[];
  run: (props: TInput, context: ActionContext, services: ActionServices) => Promise<TOutput>;
}



export enum ButtonState {  
  HIDDEN = 'hidden',
  DISABLED = 'disabled', 
  ENABLED = 'enabled',
  ERROR = 'error'  
}

export interface ButtonActionResult {
  state: ButtonState;
  errors: string[];
  visible: boolean;
  enabled: boolean;
}



export interface SendEmailInput {
    senderId: string; 
    subject: string;
    to: string;
    cc?: string; 
    bcc?: string; 
    body: string;
}

export interface SendEmailOutput {
  success: boolean;
  messageId: string;
}

export interface OpenUrlInput {
  url: string;
}

export interface OpenUrlOutput {
  success: boolean;
  url: string;
}

export interface PeekRecordInput {
  databaseId?: string;  
  recordId?: string; 
}

export interface PeekRecordOutput {
  success: boolean;
  recordId: string;
}

export interface UpdateRecordInput {
  databaseId?: string; 
  recordIds: string; 
  updates: Record<string, any>; 
}

export interface UpdateRecordOutput {
  success: boolean;
  recordId: string;
}

export interface DeleteRecordInput {
  databaseId?: string; 
  recordIds?: string; 
}

export interface DeleteRecordOutput {
  success: boolean;
  recordId: string;
}

export interface ShowConfirmationInput {
  message: string; 
  successLabel: string; 
  cancelLabel: string; 
}

export interface ShowConfirmationOutput {
  success: boolean;
  confirmed: boolean;
}

export interface ShowToastInput {
  message: string;
}

export interface ShowToastOutput {
  success: boolean;
  toastId: string;
}

export interface SendNotificationInput {
  users: string; 
  message: string;
}

export interface SendNotificationOutput {
  success: boolean;
  notificationId: string;
}

export interface CallWorkflowInput {
  workflowId: string; 
  inputs?: Record<string, any>; 
}

export interface CallWorkflowOutput {
  success: boolean;
  workflowExecutionId: string;
  result?: object;
}

export interface ExpandRecordInput {
  databaseId?: string; 
  recordId?: string; 
}

export interface ExpandRecordOutput {
  success: boolean;
  recordId: string;
}

export interface ExecuteIntegrationActionInput {
  integration: string; 
  connection?: string; 
  action: string; 
  props: Record<string, any>; 
}

export interface ExecuteIntegrationActionOutput {
  success: boolean;
  result?: object;
}


export const updateRecord: ButtonAction<UpdateRecordInput, UpdateRecordOutput> = {
  identifier: 'updateRecord',
  displayName: 'Update Record',
  props: {
    databaseId: Property.ShortText({
      displayName: 'Database',
      required: false
    }),
    recordIds: Property.ShortText({
      displayName: 'Record IDs',
      required: false
    }),
    updates: Property.Json({
      displayName: 'Updates',
      required: true
    })
  },
  run: async (props: UpdateRecordInput, context: ActionContext, services: ActionServices) => {
    const updates = (props.updates as any[] || []);
    if (!Array.isArray(updates) || updates.length === 0) {
      const errorMsg = "Update Record action has no updates configured.";
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    const isUpdateValid = updates.every(u => u.columnId && u.value !== undefined && u.value !== '');
    if (!isUpdateValid) {
      const errorMsg = "One or more updates is missing a column or value.";
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    const databaseId = props.databaseId || context.database?.id || context.meta?.databaseId || context.databaseId;
    if (!databaseId) {
      const errorMsg = "Missing database ID";
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    let recordIds: string[] = [];
    if (context.originalActionProps?.recordIds) {
      const resolvedIds = String(props.recordIds || '').split(',').map(id => id.trim()).filter(id => id);
      if (resolvedIds.length === 0) {
        const errorMsg = "Record IDs field was configured but resolved to empty value.";
        services.toast.error(errorMsg);
        throw new Error(errorMsg);
      }
      recordIds = resolvedIds;
    } else if (context.record?.id) {
      recordIds = [context.record.id];
    }

    if (recordIds.length === 0) {
      const errorMsg = "No valid record IDs found to update.";
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    const targetDatabase = context.databaseStore?.[databaseId];
    if (!targetDatabase) {
      const errorMsg = `Target database (${databaseId}) not found.`;
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    const notFoundIds = recordIds.filter(id => !targetDatabase?.recordsIdMap?.[id]);
    if (notFoundIds.length > 0) {
      const dbName = targetDatabase?.database?.name || databaseId;
      const errorMsg = `Records not found in "${dbName}": ${notFoundIds.join(', ')}`;
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    let updatesPayload: Record<string, any> = {};
    if (Array.isArray(props.updates)) {
      props.updates.forEach((item: any) => {
        if (item.columnId && item.value !== undefined) {
          updatesPayload[item.columnId] = item.value;
        }
      });
    } else {
      updatesPayload = props.updates;
    }

    const firstRecordId = recordIds[0];
    const existingRecord = targetDatabase?.recordsIdMap?.[firstRecordId]?.record;
    
    if (existingRecord) {
      const changedUpdates: Record<string, any> = {};
      for (const columnId in updatesPayload) {
        const oldValue = existingRecord.recordValues?.[columnId];
        const newValue = updatesPayload[columnId];
        
        let hasChanged = false;
        if (Array.isArray(oldValue) && Array.isArray(newValue)) {
          if (oldValue.length !== newValue.length) {
            hasChanged = true;
          } else {
            const oldSorted = [...oldValue].sort();
            const newSorted = [...newValue].sort();
            hasChanged = JSON.stringify(oldSorted) !== JSON.stringify(newSorted);
          }
        } else {
          hasChanged = oldValue !== newValue;
        }
        
        if (hasChanged) {
          changedUpdates[columnId] = newValue;
        }
      }
      updatesPayload = changedUpdates;
    }

    if (Object.keys(updatesPayload).length === 0) {
      services.toast.info("Record is already up to date.");
      return { success: true, recordId: context.record.id, updatedFields: [] };
    }


    const result = await services.updateRecordValues(databaseId, recordIds, updatesPayload);

    if (result) {
      const successMsg = `${recordIds.length} record(s) updated successfully`;
      services.toast.success(successMsg);
      services.forceRender();
      return {
        success: true,
        recordId: recordIds[0],
        updatedFields: Object.keys(updatesPayload)
      };
    } else {
      const errorMsg = "Failed to update record(s)";
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }
  }
};

export const sendEmail: ButtonAction<SendEmailInput, SendEmailOutput> = {
    identifier: 'sendEmail',
    displayName: 'Send Email',
    props: {
        senderId: Property.ShortText({
            displayName: 'Sender',
            required: true,
        }),
        to: Property.ShortText({
            displayName: 'To',
            required: true,
        }),
        subject: Property.ShortText({
            displayName: 'Subject',
            required: true,
        }),
        body: Property.LongText({
            displayName: 'Body',
            required: true,
        }),
        cc: Property.ShortText({
            displayName: 'CCs',            
            required: false,
        }),
        bcc: Property.ShortText({
            displayName: 'BCCs',
            required: false,
        })
    },
    run: async (props: SendEmailInput, context: ActionContext, services: ActionServices) => {
        if (!props.senderId || !props.to || !props.subject || !props.body) {
            const errorMsg = 'Email requires: sender, recipient (to), subject, and body';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const workspaceId = context.workspace?.workspace || (context.workspace as any);
        const actualWorkspaceId = workspaceId?.id || '';
        const token = context.token?.token || '';

        if (!actualWorkspaceId) {
            const errorMsg = 'Workspace ID not found';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        if (!token) {
            const errorMsg = 'Authentication token not found';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const recipientEmail = props.to || '';
        if (!recipientEmail.trim()) {
            const errorMsg = 'Recipient email (To) is empty after processing variables.';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const emailSubject = props.subject || '';
        if (!emailSubject.trim()) {
            const errorMsg = 'Email subject is empty after processing variables.';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const emailBody = props.body || '';
        if (!emailBody.trim()) {
            const errorMsg = 'Email body is empty after processing variables.';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const emailData = {
            to: recipientEmail.trim(),
            subject: emailSubject.trim(),
            body: emailBody.trim(),
            cc: props.cc?.trim() || '',
            bcc: props.bcc?.trim() || '',
            senderId: props.senderId
        };

        const result = await sendDirectEmail(token, actualWorkspaceId, emailData);

        if (result.error) {
            const errorMsg = `Failed to send email: ${result.error}`;
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        services.toast.success('Email sent successfully!');
        return {
            success: true,
            messageId: 'email_' + Date.now()
        };
    },
};

export const openUrl: ButtonAction<OpenUrlInput, OpenUrlOutput> = {
    identifier: 'openUrl',
  displayName: 'Open URL',
  props: {
    url: Property.ShortText({
      displayName: 'URL',
      required: true
    })
    // target: Property.StaticDropdown({
    //   displayName: 'Target',
    //   description: 'Where to open the URL',
    //   required: false,
    //   options: [
    //     {label: 'New Tab', value: '_blank'},
    //     {label: 'Same Tab', value: '_self'},
    //     {label: 'New Window', value: '_window'}
    //   ]
    // })
  },

    run: async (props: OpenUrlInput, context: ActionContext, services: ActionServices) => {
        const urlInput = props.url || '';
        
        // Validate that URL is not empty after variable processing (only if field was configured)
        if (context.originalActionProps?.url && !urlInput.trim()) {
            const errorMsg = "URL was configured but resolved to an empty value after processing variables.";
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        if (!urlInput.trim()) {
            services.toast.error("URL is required to open a link.");
            throw new Error("URL is required to open a link.");
        }

        let url = String(urlInput)
            .trim()
            .replace(/\s+/g, '')
            .replace(/%20/g, '')
            .replace(/[\u200B-\u200D\uFEFF]/g, '')
            .replace(/\u00A0/g, '')
            .replace(/\{\{[^}]*\}\}/g, '')
            .replace(/@+/g, '')
            .replace(/\/+$/, '')
            .replace(/([^:])\/\/+/g, '$1/')
            .trim();
        
        if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('mailto:') && !url.startsWith('ftp://')) {
            url = 'https://' + url;
        }
        
        if (!url || url === 'https://' || url.length < 10) {
            services.toast.error("Invalid URL: " + url);
            throw new Error("Invalid URL: " + url);
        }
        
        // Actually open the URL
        window.open(url, '_blank', 'noopener,noreferrer');
        
        return {
            success: true,
            url
        };
    }
};

export const deleteRecord: ButtonAction<DeleteRecordInput, DeleteRecordOutput> = {
    identifier: 'deleteRecord',
    displayName: 'Delete Record',
    props: {
        databaseId: Property.ShortText({
            displayName: 'Database',
            required: false
        }),
        recordIds: Property.ShortText({
            displayName: 'Record IDs',
            required: false
        })
    },
    run: async (props: DeleteRecordInput, context: ActionContext, services: ActionServices) => {
        const databaseId = props.databaseId || context.databaseId;
        if (!databaseId) {
            services.toast.error("Missing database ID");
            throw new Error("Missing database ID");
        }

        let idsToDelete: string[] = [];
        const originalRecordIds = context.originalActionProps?.recordIds;

        if (originalRecordIds) {
            const resolvedIds = String(props.recordIds || '').split(',').map(id => id.trim()).filter(id => id);
            if (resolvedIds.length === 0) {
                const errorMsg = "The 'Record IDs' field was configured but resolved to an empty value. No records were deleted.";
                services.toast.error(errorMsg);
                throw new Error(errorMsg);
            }
            idsToDelete = resolvedIds;
        } else if (context.record?.id) {
            idsToDelete = [context.record.id];
        }

        if (idsToDelete.length === 0) {
            services.toast.error("Could not find any records to delete. If you're using a column reference, please ensure it has a value in this row.");
            throw new Error("No record IDs found to delete.");
        }

        const targetDatabase = context.databaseStore?.[databaseId];
        if (!targetDatabase) {
            const errorMsg = `The target database (${databaseId}) could not be found.`;
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const notFoundIds = idsToDelete.filter(id => !targetDatabase?.recordsIdMap?.[id]);
        if (notFoundIds.length > 0) {
            const dbName = targetDatabase?.database?.name || databaseId;
            services.toast.error(`The following record IDs were not found in "${dbName}": ${notFoundIds.join(', ')}. No records were deleted.`);
            throw new Error(`Records not found for deletion: ${notFoundIds.join(', ')}`);
        }

        const confirmationMessage = `Are you sure you want to delete ${idsToDelete.length} record(s)? This cannot be undone.`;

        return new Promise<DeleteRecordOutput>((resolve, reject) => {
            services.confirm(
                'Delete Record',
                confirmationMessage,
                async () => {
                    try {
                        if (services.directDeleteRecords) {
                            await services.directDeleteRecords(databaseId, idsToDelete);
                            services.toast.success(`${idsToDelete.length} record(s) deleted successfully`);
                            services.forceRender();
                            resolve({
                                success: true,
                                recordId: idsToDelete[0]
                            });
                        } else {
                            services.toast.error("Delete function not available");
                            reject(new Error("Delete function not available"));
                        }
                    } catch (error) {
                        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                        services.toast.error("Failed to delete record: " + errorMessage);
                        reject(new Error("Failed to delete record: " + errorMessage));
                    }
                },
                () => {
                    services.toast.info("Deletion cancelled");
                    reject(new Error("Deletion cancelled"));
                },
                undefined,
                true
            );
        });
    }
};

export const showConfirmation: ButtonAction<ShowConfirmationInput, ShowConfirmationOutput> = {
    identifier: 'showConfirmation',
    displayName: 'Show Confirmation',
    props: {
        message: Property.LongText({
            displayName: 'Confirmation message',
            required: true
        }),
        successLabel: Property.ShortText({
            displayName: 'Success label',
            required: true
        }),
        cancelLabel: Property.ShortText({
            displayName: 'Cancel label',
            required: true
        })
    },
    run: async (props: ShowConfirmationInput, context: ActionContext, services: ActionServices) => {
        const title = 'Confirmation';
        const message = props.message || '';
        const confirmButtonText = props.successLabel || '';
        const cancelButtonText = props.cancelLabel || '';

        if (!message.trim()) {
            const errorMsg = 'Confirmation message is empty after processing variables.';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }
        
        return new Promise<ShowConfirmationOutput>((resolve, reject) => {
            services.confirm(
                title,
                message.trim(),
                () => {
                    services.toast.success(confirmButtonText.trim() || "Action confirmed");
                    resolve({
                        success: true,
                        confirmed: true
                    });
                },
                () => {
                    services.toast.info(cancelButtonText.trim() || "Action cancelled");
                    reject(new Error("Action Cancelled"));
                },
                undefined,
                false,
                confirmButtonText.trim() || 'Confirm',
                cancelButtonText.trim() || 'Cancel'
            );
        });
    }
};

export const showToast: ButtonAction<ShowToastInput, ShowToastOutput> = {
    identifier: 'showToast',
    displayName: 'Show Toast',
    props: {
        message: Property.ShortText({
            displayName: 'Message',
            required: true
        }),
        type: Property.StaticDropdown({
            displayName: 'Type',
            required: true,
            options: [
                {label: 'Success', value: 'success'},
                {label: 'Error', value: 'error'},
                {label: 'Warning', value: 'warning'},
                {label: 'Info', value: 'info'}
            ]
        }),
        duration: Property.Number({
            displayName: 'Duration (ms)',
            required: false
        })
    },
    run: async (props: ShowToastInput, context: ActionContext, services: ActionServices) => {
        const message = props.message || '';
        const type = (props as any).type || 'info';

        if (!message.trim()) {
            const errorMsg = 'Toast message is empty after processing variables.';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        switch (type) {
            case 'success':
                services.toast.success(message.trim());
                break;
            case 'error':
                services.toast.error(message.trim());
                break;
            case 'warning':
                services.toast.warning(message.trim());
                break;
            case 'info':
                services.toast.info(message.trim());
                break;
            default:
                services.toast.info(message.trim());
        }

        return {
            success: true,
            toastId: 'toast_' + Date.now()
        };
    }
};

export const sendNotification: ButtonAction<SendNotificationInput, SendNotificationOutput> = {
    identifier: 'sendNotification',
    displayName: 'Send Notification',
    props: {
        users: Property.ShortText({
            displayName: 'Users',
            required: true
        }),
        message: Property.LongText({
            displayName: 'Message',
            required: true
        })
    },
    run: async (props: SendNotificationInput, context: ActionContext, services: ActionServices) => {
        const { createNotification, getWorkspaceMembers } = await import('@/api/workspace');
        
        if (!props.users || !props.message) {
            services.toast.error("Notification requires users and message");
            throw new Error("Notification requires users and message");
        }

        let targetUserId: string | undefined = undefined;
        const recipientIdentifier = props.users || '';

        if (!recipientIdentifier) {
            services.toast.error("Recipient user is empty after processing variables.");
            throw new Error("Recipient user is empty.");
        }

        if (/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(recipientIdentifier)) {
            targetUserId = recipientIdentifier;
        } else {
            const token = context.token?.token;
            const workspaceData = context.workspace?.workspace || (context.workspace as any);
            const actualWorkspaceId = workspaceData?.id;

            const membersResponse = await getWorkspaceMembers(token || '', actualWorkspaceId || '');

            if (membersResponse.error || !membersResponse.data?.data?.members) {
                services.toast.error("Failed to load workspace members for user resolution");
                throw new Error("Failed to load workspace members for user resolution");
            }

            const members = membersResponse.data.data.members;
            
            let foundUser = members.find((m: any) => m.user?.id === recipientIdentifier) ||
                           members.find((m: any) => m.user?.email?.toLowerCase() === recipientIdentifier.toLowerCase()) ||
                           members.find((m: any) => {
                               const user = m.user;
                               if (!user) return false;
                               
                               const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim().toLowerCase();
                               const firstName = user.firstName?.toLowerCase() || '';
                               const lastName = user.lastName?.toLowerCase() || '';
                               const recipientLower = recipientIdentifier.toLowerCase();
                               
                               return fullName === recipientLower || firstName === recipientLower || lastName === recipientLower;
                           });
            
            if (foundUser) {
                targetUserId = foundUser.user.id;
            }
        }

        if (!targetUserId) {
            services.toast.error(`User "${recipientIdentifier}" not found in this workspace.`);
            throw new Error(`User "${recipientIdentifier}" not found`);
        }
        

        const workspaceData = context.workspace?.workspace || (context.workspace as any);
        const actualWorkspaceId = workspaceData?.id || '';
        const token = context.token?.token || '';

        const databaseId = (context.database as DatabaseData)?.id || 
                           context.meta?.databaseId || 
                           context.databaseId;
        
        let notificationLink = undefined;
        if (workspaceData?.domain) {
            if (databaseId) {
                notificationLink = `/${workspaceData.domain}/databases/${databaseId}`;
            } else {
                notificationLink = `/${workspaceData.domain}`;
            }
        }

        const notificationData = {
            userId: targetUserId || '',
            title: 'Notification',
            message: props.message,
            link: notificationLink
        };
        
        const result = await createNotification(token || '', actualWorkspaceId || '', notificationData);
        
        if (result && !result.error) {
            services.toast.success("Notification sent successfully");
            return {
                success: true,
                notificationId: 'notification_' + Date.now()
            };
        } else {
            services.toast.error("Failed to send notification");
            throw new Error("Failed to send notification");
        }
    }
};

export const callWorkflow: ButtonAction<CallWorkflowInput, CallWorkflowOutput> = {
    identifier: 'callWorkflow',
    displayName: 'Call Workflow',
    props: {
        workflowId: Property.StaticDropdown({
            displayName: 'Workflow',
            required: true,
            options: [] 
        }),
        inputs: Property.Json({
            displayName: 'Inputs',
            required: false
        })
    },
    run: async (props: CallWorkflowInput, context: ActionContext, services: ActionServices) => {
        const { createWorkflowInstance } = await import('@/api/workflow');
        
        if (!props.workflowId) {
            const errorMsg = "Workflow ID is required";
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const workspaceId = context.workspace?.workspace?.id;
        const token = context.token?.token;

        if (!workspaceId || !token) {
            const errorMsg = "Workspace ID or token not found.";
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }
        
        let inputs = {};
        if (props.inputs) {
            try {
                inputs = typeof props.inputs === 'string' ? JSON.parse(props.inputs) : props.inputs;
            } catch (e) {
                const errorMsg = `Invalid JSON in workflow inputs: ${(e as Error).message}`;
                services.toast.error(errorMsg);
                throw new Error(errorMsg);
            }
        }

        const result = await createWorkflowInstance(token, workspaceId, props.workflowId, { data: inputs });

        if (result.error) {
            const errorMsg = `Failed to call workflow: ${result.error}`;
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        services.toast.success(`Workflow "${props.workflowId}" started successfully.`);
        
        return {
            success: true,
            workflowExecutionId: result.data?.data?.instance.id || 'exec_' + Date.now(),
            result: result.data?.data?.instance
        };
    }
};

export const expandRecord: ButtonAction<ExpandRecordInput, ExpandRecordOutput> = {
    identifier: 'expandRecord',
    displayName: 'Expand Record',
    props: {
        databaseId: Property.ShortText({
            displayName: 'Database',
            required: false
        }),
        recordId: Property.ShortText({
            displayName: 'Record ID',
            required: false
        })
    },
    run: async (props: ExpandRecordInput, context: ActionContext, services: ActionServices) => {
        const workspaceData = context.workspace?.workspace || (context.workspace as any);
        if (!workspaceData?.domain) {
            services.toast.error("Missing workspace domain");
            throw new Error("Missing workspace domain");
        }
        
        const isForeignKey = !!props.databaseId && props.databaseId !== context.databaseId;
        const databaseId = props.databaseId || context.databaseId;
        let recordId;

        if (isForeignKey) {
            recordId = props.recordId;
            if (!recordId) {
                services.toast.error("Record ID is required when expanding a record from another database.");
                throw new Error("Record ID is required for foreign key expand.");
            }
        } else {
            const originalRecordId = context.originalActionProps?.recordId;
            
            if (originalRecordId) {
                const resolvedRecordId = String(props.recordId || '').trim();
                if (!resolvedRecordId) {
                    const errorMsg = "The 'Record ID' field was configured but resolved to an empty value after processing variables.";
                    services.toast.error(errorMsg);
                    throw new Error(errorMsg);
                }
                recordId = resolvedRecordId;
            } else {
                recordId = context.record?.id;
            }
        }

        if (!databaseId) {
            services.toast.error("Missing database ID");
            throw new Error("Missing database ID");
        }
        
        if (!recordId) {
            services.toast.error("Could not find a record to expand. If you're using a column reference, please ensure it has a value in this row.");
            throw new Error("No record ID in context.");
        }

        if (recordId.includes(',')) {
            services.toast.error("The 'Expand Record' action only works with a single Record ID.");
            throw new Error("Multiple record IDs not supported for expand action.");
        }

        const targetDatabase = context.databaseStore?.[databaseId];
        if (!targetDatabase?.recordsIdMap?.[recordId]) {
            const dbName = targetDatabase?.database?.name || databaseId;
            services.toast.error(`The record ID "${recordId}" was not found in the database "${dbName}".`);
            throw new Error(`Record not found for expand: ${recordId} in ${dbName}`);
        }
        
        window.open(`/${workspaceData.domain}/databases/${databaseId}/records/${recordId}`, '_blank', 'noopener,noreferrer');
        
        return {
            success: true,
            recordId,
        };
    }
};

export const peekRecord: ButtonAction<PeekRecordInput, PeekRecordOutput> = {
    identifier: 'peekRecord',
    displayName: 'Peek Record',
    props: {
        databaseId: Property.ShortText({
            displayName: 'Database',
            required: false
        }),
        recordId: Property.ShortText({
            displayName: 'Record ID',
            required: false
        })
    },
    run: async (props: PeekRecordInput, context: ActionContext, services: ActionServices) => {
        const isForeignKey = !!props.databaseId && props.databaseId !== context.databaseId;
        const databaseId = props.databaseId || context.databaseId;
        let recordId;

        if (isForeignKey) {
            recordId = props.recordId;
            if (!recordId) {
                services.toast.error("Record ID is required when peeking into another database.");
                throw new Error("Record ID is required for foreign key peek.");
            }
        } else {
            const originalRecordId = context.originalActionProps?.recordId;
            
            if (originalRecordId) {
                const resolvedRecordId = String(props.recordId || '').trim();
                if (!resolvedRecordId) {
                    const errorMsg = "The 'Record ID' field was configured but resolved to an empty value after processing variables.";
                    services.toast.error(errorMsg);
                    throw new Error(errorMsg);
                }
                recordId = resolvedRecordId;
            } else {
                recordId = context.record?.id;
            }
        }

        if (!databaseId) {
            services.toast.error("Could not find a database to peek in the current context.");
            throw new Error("No database ID in context.");
        }

        if (!recordId) {
            services.toast.error("Could not find a record to peek. If you're using a column reference, please ensure it has a value in this row.");
            throw new Error("No record ID found to peek.");
        }

        if (recordId.includes(',')) {
            services.toast.error("The 'Peek Record' action only works with a single Record ID.");
            throw new Error("Multiple record IDs not supported for peek action.");
        }

        const targetDatabase = context.databaseStore?.[databaseId];
        if (!targetDatabase?.recordsIdMap?.[recordId]) {
            const dbName = targetDatabase?.database?.name || databaseId;
            services.toast.error(`The record ID "${recordId}" was not found in the database "${dbName}".`);
            throw new Error(`Record not found for peek: ${recordId} in ${dbName}`);
        }

        if (services.setPeekRecord) {
            services.setPeekRecord(recordId, databaseId);
        } else {
            services.toast.error("Peek view functionality not available.");
            throw new Error("Peek view functionality not available.");
        }

        return {
            success: true,
            recordId,
        };
    }
};

export const executeIntegrationAction: ButtonAction<ExecuteIntegrationActionInput, ExecuteIntegrationActionOutput> = {
    identifier: 'executeIntegrationAction',
    displayName: 'Execute Integration Action',
    props: {
        integration: Property.StaticDropdown({
            displayName: 'Integration',
            required: true,
            options: [] // Options will be populated dynamically in the editor
        }),
        connection: Property.StaticDropdown({
            displayName: 'Connection',
            required: false,
            options: [] // Options will be populated dynamically based on selected integration
        }),
        action: Property.StaticDropdown({
            displayName: 'Action',
            required: true,
            options: [] // Options will be populated dynamically based on selected integration
        }),
        props: Property.Json({
            displayName: 'Props',
            required: true
        })
    },
    run: async (props: ExecuteIntegrationActionInput, context: ActionContext, services: ActionServices) => {
        if (!props.integration || !props.action) {
            services.toast.error("Integration action requires integration and action");
            throw new Error("Integration action requires integration and action");
        }

        const workspaceId = context.workspace?.workspace?.id;
        const token = context.token?.token;

        if (!workspaceId || !token) {
            const errorMsg = "Workspace ID or token not found.";
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        // Validate that integration is not empty after variable processing
        if (context.originalActionProps?.integration && !props.integration.trim()) {
            const errorMsg = "Integration was configured but resolved to an empty value after processing variables.";
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        // Validate that action is not empty after variable processing
        if (context.originalActionProps?.action && !props.action.trim()) {
            const errorMsg = "Action was configured but resolved to an empty value after processing variables.";
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        let propsValue = {};
        if (props.props) {
            try {
                propsValue = typeof props.props === 'string' ? JSON.parse(props.props) : props.props;
            } catch (e) {
                const errorMsg = `Invalid JSON in integration props: ${(e as Error).message}`;
                services.toast.error(errorMsg);
                throw new Error(errorMsg);
            }
        }

        const { executeIntegrationAction } = await import('@/api/workspace');
        
        const integrationData = {
            name: props.action,
            connectionId: props.connection,
            propsValue: propsValue,
            mode: 'run' as const,
            type: 'action' as const
        };

        const result = await executeIntegrationAction(token, workspaceId, props.integration, integrationData);

        if (result.error) {
            const errorMsg = `Failed to execute integration action: ${result.error}`;
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        services.toast.success(`Integration action "${props.action}" executed successfully.`);
        
        return {
            success: true,
            result: result.data?.data?.result || { executed: true }
        };
    }
};

export const buttonActions = {
    updateRecord,
    sendEmail,
    openUrl,
    deleteRecord,
    showConfirmation,
    showToast,
    sendNotification,
    callWorkflow,
    expandRecord,
    peekRecord,
    executeIntegrationAction,
};


export const getButtonAction = (identifier: string): ButtonAction | undefined => {
    const action = buttonActions[identifier as keyof typeof buttonActions] as ButtonAction<any, any> | undefined;
    return action;
};

export const getButtonActionIdentifiers = (): string[] => {
    const identifiers = Object.keys(buttonActions);
    return identifiers;
};
