import {Column, RenderCellProps, RenderGroupCellProps, RenderHeaderCellProps, SELECT_COLUMN_KEY, useRowSelection} from "react-data-grid";
import React from "react";
import {Checkbox} from "@/components/ui/checkbox";
import {DataViewRow} from "@/components/workspace/main/views/table";
import {Button} from "@/components/ui/button";
import {EyeIcon} from "@/components/icons/FontAwesomeRegular";
import {useWorkspace} from "@/providers/workspace";
import {useViews} from "@/providers/views";
import {useStackedPeek} from "@/providers/stackedpeek";

function HeaderRenderer(props: RenderHeaderCellProps<unknown>) {
    const column = props.column
    const [isRowSelected, onRowSelectionChange] = useRowSelection();

    return (
        <>
            <div className="r-row-select text-xs h-full flex items-center justify-center">
                <div className='flex-1 px-2'>
                    {column.editable && <Checkbox
                        checked={isRowSelected}
                        onCheckedChange={(checked) => {
                            onRowSelectionChange({type: 'HEADER', checked: !!checked});
                            // onRowSelectionChange({type: 'ROW', row: props.row, checked: !!checked, isShiftClick: false});
                        }}
                    />}
                </div>
            </div>
        </>
    );
}

function SelectFormatter(props: RenderCellProps<unknown>) {
    const [isRowSelected, onRowSelectionChange] = useRowSelection();
    const {setPeekRecordId} = useViews()
    const {url} = useWorkspace()
    const {openRecord} = useStackedPeek()
    const rowData = props.row as DataViewRow

    const row = rowData.record
    const index = props.rowIdx

    // Check if peek icon should be hidden
    const hidePeekIcon = (props.column as any).__hidePeekIcon__ || false

    let href = url(`/databases/${row.databaseId}/records/${row.id}`)
    return (
        <>
            {rowData.id === AddRowKey ? <>
                <div className="absolute text-xs top-0 left-0 bottom-0">
                    <Button
                        variant='ghost'
                        // disabled={isInProgress}
                        // onClick={doGenerate}
                        className={`rounded-full p-1.5 size-auto`}>
                        Add Row
                    </Button>
                </div>
            </> : <>
                 <div className={`r-row-select text-xs h-full flex items-center gap-1 ${props.column.editable ? 'group !border-r-0' : 'text-center justify-center'}`}>
                     <div className='flex-1 px-2'>
                         {!isRowSelected && <div className='absolute left-0 right-0 px-3 top-1/2 -translate-y-1/2 group-hover:hidden'>
                             <span className='text-xs font-medium'>{index + 1}</span>
                         </div>}
                         <Checkbox
                             checked={isRowSelected}
                             disabled={!props.column.editable}
                             className={`${!isRowSelected && 'invisible opacity-0 group-hover:opacity-100 group-hover:visible'}  `}
                             onCheckedChange={(checked) => {
                                 onRowSelectionChange({type: 'ROW', row: props.row, checked: !!checked, isShiftClick: false});
                             }}
                         />
                     </div>
                     {props.column.editable && !hidePeekIcon && <div className='relative'>
                         <Button variant='outline'
                                 onClick={() => openRecord(row.id, row.databaseId)}
                                 className={`absolute right-0 top-1/2 -translate-y-1/2 h-auto p-1.5 rounded-full z-10 hover:bg-neutral-300 hover:border-neutral-400
                                      hidden group-hover:block pointer hover:text-black`}>
                             {/*<Link href={href}>*/}
                             {/*    <EyeIcon className='size-2.5'/>*/}
                             {/*</Link>*/}
                             <EyeIcon className='size-2.5'/>
                         </Button>
                     </div>}
                 </div>
             </>}

        </>
    );
}

function SelectGroupFormatter(props: RenderGroupCellProps<unknown>) {
    const [isRowSelected, onRowSelectionChange] = useRowSelection();

    return (
        <>
            <div className="r-row-select text-xs h-full flex items-center">
                <Checkbox
                    checked={isRowSelected}
                    disabled={!props.column.editable}
                    onCheckedChange={(checked) => {
                        onRowSelectionChange({type: 'ROW', row: props.row, checked: !!checked, isShiftClick: false});
                    }}
                />
            </div>
        </>
    );
}

export const getSelectColumnProps = (colsCount: number, editable = true, hidePeekIcon = false): Column<any, any> => {
    const column: Column<any, any> = {
        key: SELECT_COLUMN_KEY,
        name: '',
        width: 50,
        // minWidth: 35,
        // maxWidth: 35,
        resizable: false,
        sortable: false,
        editable: editable,
        frozen: true,
        colSpan(args) {
            if (args.type === 'ROW' && args.row.id === AddRowKey) {
                console.log({args, colsCount})

                return colsCount + 1
            }
            // if (args.type === 'ROW') {
            //     if (key === '2' && args.row === 2) return 3;
            //     if (key === '4' && args.row === 4) return 6; // Will not work as colspan includes both frozen and regular columns
            //     if (key === '0' && args.row === 5) return 5;
            //     if (key === '27' && args.row === 8) return 3;
            //     if (key === '6' && args.row < 8) return 2;
            // }
            // if (args.type === 'HEADER' && key === '8') {
            //     return 3;
            // }
            return undefined;
        },
        renderHeaderCell(props) {
            return <HeaderRenderer {...props} />;
        },
        renderCell(props) {
            return <SelectFormatter {...props} />;
        },
        renderGroupCell(props) {
            return <SelectGroupFormatter {...props} />;
        }
    };
    
    // Add custom property to pass hidePeekIcon flag
    (column as any).__hidePeekIcon__ = hidePeekIcon;
    
    return column;
}

export const AddRowKey = 'addRow'

// export const getAddRowProps = (): DataViewRow => {
//     return {
//         record: {
//             id: AddRowKey,
//             recordValues: {},
//             updatedAt: new Date(0).toISOString(),
//         } as Record,
//         updatedAt: "",
//         id: 'addRow',
//     }
// }


