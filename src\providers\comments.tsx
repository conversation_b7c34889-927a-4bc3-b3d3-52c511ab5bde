import { createContext, useContext, useMemo, useState, useEffect, useCallback, useRef } from "react";
import { useAuth } from "@/providers/user";
import { useWorkspace } from "@/providers/workspace";
import { Comment, CommentEnum, CreateThread, Reaction, Thread } from "@/typings/comment";
import { apiUrl } from "@/api/common";
import { useWorkspaceSocket } from "./workspaceSocket";
import { Block, BlockNoteEditor } from "@blocknote/core";
import { queryObjectToString } from "@/api/admin";
import { CommentStoreAuth, CustomCommentStore } from "@/api/comments";
import { setMark } from "@/components/workspace/main/common/comment/setMark";
import * as Y from "yjs";


export enum CommentEvent {
  COMMENT_CREATED = "comment-created",
  COMMENT_UPDATED = "comment-updated",
  COMMENT_DELETED = "comment-deleted",
}

interface CommentSocketEvent {
  comment: Comment,
  databaseId: string,
  recordId: string,
  documentId: string,
  pageId: string
}

interface CommentContextType {
  comments: Comment[];
  threads: Thread[];
  filteredThreads: Thread[];
  editor: BlockNoteEditor<any, any, any> | undefined;
  isLoading: boolean;
  toggleSuggestionMode: () => void;
  suggestionMode: boolean;
  filterComments: (filter: {
    type?: 'all' | 'comments' | 'suggestions';
    status?: 'all' | 'resolved' | 'unresolved';
    authorId?: string;
  }) => void;
  commentFilter: {
    type: 'all' | 'comments' | 'suggestions';
    status: 'all' | 'resolved' | 'unresolved';
    userId?: string;
  };
  refreshComments: () => void;
  commentStore: CustomCommentStore;
}

const CommentContext = createContext<CommentContextType | undefined>(undefined);

interface CommentProviderProps {
  children: React.ReactNode;
  room: string;
  editor?: BlockNoteEditor<any, any, any>; 
   fragment?: Y.XmlFragment,
      doc?:Y.Doc,
}

export function CommentProvider({
  children,
  room,
  editor,
  doc,
  fragment
}: CommentProviderProps) {
  const { user, token } = useAuth();
  const { workspace } = useWorkspace();
  const { socket, isConnected } = useWorkspaceSocket();

  // const [editor, setEditor] = useState<BlockNoteEditor<any, any, any> | undefined>(initialEditor);
  const [suggestionMode, setSuggestionMode] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false); // NEW: Track if we've loaded data before
  const [commentFilter, setCommentFilter] = useState<{
    type: 'all' | 'comments' | 'suggestions';
    status: 'all' | 'resolved' | 'unresolved';
    userId?: string;
  }>({ type: 'all', status: 'unresolved' });

  const [updateTrigger, setUpdateTrigger] = useState(0);
  const forceUpdate = () => setUpdateTrigger(prev => prev + 1);

  const lastCommentsRef = useRef<string>('');
  const isUpdatingRef = useRef(false);

  // Create comment store instance
  const commentStore = useMemo(() => {
    if (!workspace?.workspace.id || !token?.token || !user?.id) return null;

    let databaseId;
    let documentId;
    let recordId;
    let pageId;
    
    if (room.startsWith('rs:')) {
      const name = room.replace("rs:", '');
      const [dbId, rId] = name.split("|");
      databaseId = dbId;
      recordId = rId;
    } else if (room.startsWith('d:')) {
      const name = room.replace("d:", '');
      const [pId, viewId, id] = name.split("|");
      documentId = id;
      pageId = pId;
    } else if (room.startsWith('n:')) {
      const name = room.replace("n:", '');
      const [id, workspaceId] = name.split("|");
      documentId = id;
    }
    
    const queryStr = queryObjectToString({ databaseId, documentId, recordId, pageId });
    const entityParams = { databaseId, documentId, recordId, pageId }
    const auth: CommentStoreAuth = {
      canCreateComment: () => !!user?.id,
      canUpdateComment: (comment: Comment) => comment.userId === user.id || workspace.workspaceMember.role === 'admin' || workspace.workspaceMember.role === 'owner',
      canDeleteComment: (comment: Comment) => comment.userId === user.id || workspace.workspaceMember.role === 'admin' || workspace.workspaceMember.role === 'owner',
      canResolveComment: (comment: Comment) => !!user?.id,
      canUnresolveComment: (comment: Comment) => !!user?.id,
      canAddReaction: (comment: Comment, emoji: string) => !!user?.id,
      canDeleteReaction: (comment: Comment, emoji: string) => !!user?.id,
    };

    return new CustomCommentStore(
      `${apiUrl()}/workspaces/${workspace.workspace.id}/comments`,
      queryStr,
      {
        Authorization: `Bearer ${token.token}`,
      },
      auth,
      user.id,
      {
        documentId,
        databaseId,
        recordId,
        pageId
      }
    );
  }, [workspace?.workspace.id, token?.token, user?.id, workspace?.workspaceMember.role, room]);

  const buildThreads = (comments: Comment[]): Thread[] => {
    const threadsMap = new Map<string, Thread>();
    
    const sortedComments = [...comments].sort((a, b) => 
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
    
    for (const comment of sortedComments) {
      if (!comment.parentId) {
        threadsMap.set(comment.id, {
          id: comment.id,
          isResolved: comment.isResolved,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
          comments: [comment],
        });
        if (editor && fragment && doc && comment.position.yjs) {
           setMark(editor, fragment, doc, comment.position.yjs, "comment", {
          orphan: false,
        threadId: comment.id,})
        }
       
      } else {
        const thread = threadsMap.get(comment.parentId);
        if (thread && !thread.comments.some(c => c.id === comment.id)) {
          thread.comments.push(comment);
        }
      }
    }

    return Array.from(threadsMap.values());
  };

  const threads = useMemo(() => {
    return buildThreads(comments);
  }, [comments, updateTrigger]);

  const filteredThreads = useMemo(() => {
    if (comments.length === 0) {
      return [];
    }
    
    let filtered = [...threads];

    if (commentFilter.status === 'resolved') {
      filtered = filtered.filter(thread => thread.isResolved);
    } else if (commentFilter.status === 'unresolved') {
      filtered = filtered.filter(thread => !thread.isResolved);
    }

    // if (commentFilter.type === 'comments') {
    //   filtered = filtered.filter(thread => thread.type === CommentEnum.COMMENT);
    // } else if (commentFilter.type === 'suggestions') {
    //   filtered = filtered.filter(thread => thread.type === CommentEnum.SUGGESTION);
    // }

    if (commentFilter.userId) {
      filtered = filtered.filter(thread => 
        thread.comments.some(comment => comment.userId === commentFilter.userId)
      );
    }

    return filtered;
  }, [threads, commentFilter, updateTrigger, comments.length]);

  const getAllComments = useCallback(async (): Promise<Comment[]> => {
    if (!commentStore) return [];
    
    try {
      setIsLoading(true);
      
      const allComments = await commentStore.getComments();
      return allComments;
    } catch (error) {
      console.error('❌ Error fetching comments:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [commentStore, hasLoadedOnce]);

  const updateComments = useCallback(async () => {
    if (isUpdatingRef.current) {
      return;
    }
    
    isUpdatingRef.current = true;
    
    try {
      const allComments = await getAllComments();
      setComments(allComments);
      forceUpdate();
    } finally {
      isUpdatingRef.current = false;
    }
  }, [getAllComments]);

  // Socket event handlers
  useEffect(() => {
    if (!socket || !isConnected) return;

    console.log('🔌 Setting up comment socket listeners');

    const handleCommentCreated = (data: CommentSocketEvent) => {
      console.log('✅ Comment created:', data.comment.id);
      if (!data?.comment?.id) return;
      
      setComments(prev => {
        const exists = prev.find(c => c.id === data.comment.id);
        if (exists) return prev;
        const newComments = [...prev, data.comment];
        console.log(' Added comment, total:', newComments.length);
        return newComments;
      });
    };

    const handleCommentUpdated = (data: CommentSocketEvent) => {
      console.log('🔄 Comment updated:');
      if (!data?.comment?.id) return;
      
      setComments(prev => {
        const updated = prev.map(c => 
          c.id === data.comment.id ? data.comment : c
        );
        console.log('✏️ Updated comment, total:', updated.length);
        return updated;
      });
    };

    const handleCommentDeleted = (data: CommentSocketEvent) => {
      console.log('🗑️ Comment deleted:', data.comment.id);
      if (!data?.comment?.id) return;
      
      setComments(prev => {
        const updated = prev.filter(c => c.id !== data.comment.id);
        console.log('➖ Removed comment, total:', updated.length);
        return updated;
      });
      forceUpdate();
    };

    socket.on(CommentEvent.COMMENT_CREATED, (data) => handleCommentCreated(data.comment));
    socket.on(CommentEvent.COMMENT_UPDATED, (data) => handleCommentUpdated(data.comment));
    socket.on(CommentEvent.COMMENT_DELETED, (data) => handleCommentDeleted(data.comment));

    return () => {
      console.log('🧹 Cleaning up comment socket listeners');
      socket.off(CommentEvent.COMMENT_CREATED, handleCommentCreated);
      socket.off(CommentEvent.COMMENT_UPDATED, handleCommentUpdated);
      socket.off(CommentEvent.COMMENT_DELETED, handleCommentDeleted);
    };
  }, [socket, isConnected]);

  // Initial load - only load if we haven't loaded before
  useEffect(() => {
    if (commentStore) {
      if (!hasLoadedOnce) {
        updateComments();
      }
    }
  }, [commentStore]); // Simplified dependencies

  const refreshComments = useCallback(() => {
 
    updateComments();
  }, [updateComments]);

  const toggleSuggestionMode = useCallback(() => {
    setSuggestionMode(prev => !prev);
  }, []);

  const filterComments = useCallback((filter: {
    type?: 'all' | 'comments' | 'suggestions';
    status?: 'all' | 'resolved' | 'unresolved';
    authorId?: string;
  }) => {
    
    setCommentFilter(prev => ({
      type: filter.type ?? prev.type,
      status: filter.status ?? prev.status,
      userId: filter.authorId ?? prev.userId,
    }));
    forceUpdate();
  }, []);

  const value: CommentContextType = {
    comments,
    threads,
    filteredThreads,
    editor,
    isLoading,
    toggleSuggestionMode,
    suggestionMode,
    filterComments,
    commentFilter,
    refreshComments,
    commentStore: commentStore!,
  };

 
  return <CommentContext.Provider value={value}>{children}</CommentContext.Provider>;
}

export function useComments(): CommentContextType {
  const context = useContext(CommentContext);
  if (!context) {
    throw new Error("useComments must be used within a CommentProvider");
  }
  return context;
}