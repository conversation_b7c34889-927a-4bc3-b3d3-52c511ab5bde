
"use client";

import {<PERSON><PERSON>} from "@/components/ui/button";
import {Bars3CenterLeftIcon} from "@heroicons/react/24/outline";
import React, { useState } from "react";
import {useWorkspace} from "@/providers/workspace";
import WorkspaceSwitcher from "@/components/workspace/main/common/workspaceSwitcher";
import Link from "next/link";
import {DatabaseLinks} from "@/components/workspace/main/database/navigation";
import {PageLinks} from "@/components/workspace/main/pages/navigation";
import {NotificationView} from "@/components/workspace/main/common/notificationView";
import {ScrollArea} from "@/components/ui/scroll-area";
import {SidebarProvider} from "@/providers/sidebar";
import {isProd} from "@/utils/environment";
import { CodeMergeIcon, EnvelopeIcon, GearComplexIcon, NoteIcon, ShapesIcon, WreathIcon } from "@/components/icons/FontAwesomeRegular";
import SearchModal from "@/components/workspace/main/common/search";
export interface SidebarProps {
    collapse: boolean
    setCollapse: (val: boolean) => void
}

export const Sidebar = ({setCollapse, collapse}: SidebarProps) => {
    return <>
        <SidebarProvider>
            {collapse && <div className='fixed'>
                <div className="h-12 flex items-center p-2">
                    <SidebarCollapse {...{collapse, setCollapse}}/>
                </div>
            </div>}
            {!collapse && <SidebarNav  {...{collapse, setCollapse}}/>}
        </SidebarProvider>
    </>
}

export interface PathContext {
    context: "page" | 'database' | string
    contextId: string
    viewId: string
}

const SidebarNav = ({setCollapse, collapse}: SidebarProps) => {
    const {url} = useWorkspace()
    return <>
        <nav
            className='border-0 border-neutral-300 shadow-lg lg:border-r lg:shadow-none h-full relative group/sidebar flex flex-col'>
            <div className="h-12 flex items-center border-b border-neutral-300 p-2 gap-0.5">
                <div className='flex-1 overflow-hidden'>
                    <WorkspaceSwitcher/>
                </div>
                <SidebarCollapse {...{collapse, setCollapse}}/>
            </div>

            
            <div className="flex flex-col gap-1 py-2 px-1">
             <SearchModal/>
                <Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">
                    <Link href={url("/welcome")}>
                        <WreathIcon className="mr-2 size-3"/>
                        Welcome
                    </Link>
                </Button>
                {/*<Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">*/}
                {/*    <Link href={url("/search")}>*/}
                {/*        <MagnifyingGlassCircleIcon className="mr-2 h-4 w-4"/>*/}
                {/*        Search*/}
                {/*    </Link>*/}
                {/*</Button>*/}
                {/*<Button asChild variant="ghost" className="h-auto text-xs p-2 py-1.5 rounded-none justify-start">*/}
                {/*    <Link href="#">*/}
                {/*        <BellIcon className="mr-2 h-4 w-4"/>*/}
                {/*        Notifications*/}
                {/*    </Link>*/}
                {/*</Button>*/}
                <NotificationView/>

                <Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">
                    <Link href={url("/notes")}>
                        <NoteIcon className="mr-2 size-3" />
                        Notes
                    </Link>
                </Button>
                <Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">
                    <Link href={url("/emails")}>
                        <EnvelopeIcon className="mr-2 size-3"/>
                        Emails
                    </Link>
                </Button>
                <Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">
                    <Link href={url("/workflows")}>
                        <CodeMergeIcon className="mr-2 size-3"/>
                        Workflows
                    </Link>
                </Button>
                <Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">
                    <Link href={url("/templates")}>
                        <ShapesIcon className="mr-2 size-3"/>
                        Templates
                    </Link>
                </Button>
                {/*<Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">*/}
                {/*    <Link href={url("/automations")}>*/}
                {/*        /!*<PuzzlePieceIcon className="mr-2 h-4 w-4"/>*!/*/}
                {/*        /!*<RectangleGroupIcon className="mr-2 h-4 w-4"/>*!/*/}
                {/*        <SquaresPlusIcon className="mr-2 h-4 w-4"/>*/}
                {/*        Automations*/}
                {/*    </Link>*/}
                {/*</Button>*/}
                {/*<Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">*/}
                {/*    <Link href="#">*/}
                {/*        /!*<PuzzlePieceIcon className="mr-2 h-4 w-4"/>*!/*/}
                {/*        /!*<RectangleGroupIcon className="mr-2 h-4 w-4"/>*!/*/}
                {/*        <ArrowUpCircleIcon className="mr-2 h-4 w-4"/>*/}
                {/*        Export*/}
                {/*    </Link>*/}
                {/*</Button>*/}
                <Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start mr-2">
                    <Link href={url("/settings")}>
                        <GearComplexIcon className="mr-2 size-3"/>
                        Settings
                    </Link>
                </Button>

            </div>
            <div className="flex-1 overflow-hidden border-t border-neutral-300">
                <ScrollArea className="size-full scrollBlockChild">

                    <div className="h-full flex flex-col gap-2 py-2 px-1">

                        <DatabaseLinks/>

                        <PageLinks/>

                        {/*<Separator orientation="horizontal" className="border-neutral-300"/>*/}
                        {/*<Button asChild variant="ghost" className="h-auto text-sm p-2 py-1.5 rounded-none justify-start">*/}
                        {/*    <Link href="/">*/}
                        {/*        /!*<PuzzlePieceIcon className="mr-2 h-4 w-4"/>*!/*/}
                        {/*        /!*<RectangleGroupIcon className="mr-2 h-4 w-4"/>*!/*/}
                        {/*        <ArrowUpCircleIcon className="mr-2 h-4 w-4"/>*/}
                        {/*        Export*/}
                        {/*    </Link>*/}
                        {/*</Button>*/}


                    </div>
                </ScrollArea>
            </div>
        </nav>
    </>
}
export const SidebarCollapse = ({collapse, setCollapse}: SidebarProps) => {
    return <>
        <Button variant="ghost" className="p-2 rounded-full h-auto relative"
                onClick={() => setCollapse(!collapse)}>
            <Bars3CenterLeftIcon className="w-4 h-4"/>
        </Button>
    </>
}
