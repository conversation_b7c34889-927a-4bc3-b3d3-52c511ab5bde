import { CalendarEvent } from '@/typings/page';
import { EventSegment } from './multiDay';
import { isSameDay } from 'date-fns';

const GUTTER_WIDTH_PERCENT = 10; // Space on the right for creating new events
const CASCADE_STAGGER_PERCENT = 15; // How much to offset each cascading event
const MIN_EVENT_WIDTH_PERCENT = 50; // Minimum width for events to remain readable

export interface SegmentLayout {
  segment: EventSegment;
  left: number;
  width: number;
  zIndex: number;
  hasOverlap: boolean; // New property to indicate if this event overlaps with others
}

export interface FinalLayout {
  segmentLayouts: SegmentLayout[];
}

/**
 * Checks if two event segments overlap in time on the same day.
 */
const segmentsOverlap = (segment1: EventSegment, segment2: EventSegment): boolean => {
  // Must be on the same day
  if (!isSameDay(segment1.date, segment2.date)) {
    return false;
  }
  return segment1.startTime < segment2.endTime && segment2.startTime < segment1.endTime;
};

export const calculateLayout = (segments: EventSegment[]): FinalLayout => {
  const finalLayout: FinalLayout = {
    segmentLayouts: [],
  };

  if (!segments.length) {
    return finalLayout;
  }

  // Sort all segments by start time, then by duration (longer events first to establish columns)
  const sortedSegments = [...segments].sort((a, b) => {
    const startDiff = a.startTime.getTime() - b.startTime.getTime();
    if (startDiff !== 0) return startDiff;
    const durationB = b.endTime.getTime() - b.startTime.getTime();
    const durationA = a.endTime.getTime() - a.startTime.getTime();
    return durationB - durationA;
  });

  const processedSegments = new Set<string>();

  for (const segment of sortedSegments) {
    if (processedSegments.has(segment.id)) {
      continue;
    }

    // Find all overlapping segments for the current segment
    const overlappingGroup = sortedSegments.filter(s => segmentsOverlap(segment, s));
    
    // This will hold the columns of segments for the current overlapping group
    const columns: EventSegment[][] = [];
    
    overlappingGroup.forEach(groupSegment => {
      let placed = false;
      // Find the first column where this segment can fit
      for (const column of columns) {
        if (column.every(s => !segmentsOverlap(groupSegment, s))) {
          column.push(groupSegment);
          placed = true;
          break;
        }
      }
      // If it doesn't fit in any existing column, create a new one
      if (!placed) {
        columns.push([groupSegment]);
      }
    });

    const maxColumns = columns.length;
    const availableWidth = 100 - GUTTER_WIDTH_PERCENT;

    // --- SMART CASCADING LOGIC ---
    const totalCascadeSpace = (maxColumns - 1) * CASCADE_STAGGER_PERCENT;
    let eventWidth = availableWidth - totalCascadeSpace;
    let stagger = CASCADE_STAGGER_PERCENT;

    if (eventWidth < MIN_EVENT_WIDTH_PERCENT && maxColumns > 1) {
      eventWidth = MIN_EVENT_WIDTH_PERCENT;
      const remainingSpace = availableWidth - eventWidth;
      stagger = remainingSpace / (maxColumns - 1);
    }

    columns.forEach((column, colIndex) => {
      column.forEach(seg => {
        if (processedSegments.has(seg.id)) return;

        const leftPosition = colIndex * stagger;
        
        finalLayout.segmentLayouts.push({
          segment: seg,
          left: leftPosition,
          width: eventWidth,
          zIndex: 10 + colIndex,
          hasOverlap: maxColumns > 1,
        });

        processedSegments.add(seg.id);
      });
    });
     overlappingGroup.forEach(s => processedSegments.add(s.id));
  }
  
  return finalLayout;
};


export const eventsOverlap = (event1: CalendarEvent, event2: CalendarEvent): boolean => {
  const start1 = new Date(event1.start);
  const end1 = new Date(event1.end);
  const start2 = new Date(event2.start);
  const end2 = new Date(event2.end);
  
  return start1 < end2 && start2 < end1;
};


export const calculateAllDayLayout = (
  segments: EventSegment[],
  maxVisible: number = 3,
): { visibleSegments: EventSegment[]; moreCount: number } => {
  if (segments.length <= maxVisible) {
    return { visibleSegments: segments, moreCount: 0 };
  }

  // Sort by start time, then duration.
  const sorted = [...segments].sort((a, b) => {
    const startDiff = a.startTime.getTime() - b.startTime.getTime();
    if (startDiff !== 0) return startDiff;
    return (b.endTime.getTime() - b.startTime.getTime()) - (a.endTime.getTime() - a.startTime.getTime());
  });

  const visibleSegments = sorted.slice(0, maxVisible - 1);
  const moreCount = segments.length - visibleSegments.length;

  return { visibleSegments, moreCount };
};
