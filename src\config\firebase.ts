import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage, Messaging, MessagePayload } from "firebase/messaging";

interface FCMInitOptions {
    onMessage: (payload: MessagePayload) => void;
}

const firebaseConfig = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

const firebaseCloudMessaging = {
    init: async function (callback: FCMInitOptions): Promise<string | null> {
        try {
            // Initialize Firebase only if not already initialized
            let app = initializeApp(firebaseConfig);
            const messaging = getMessaging(app);

            // Set up foreground message handler
            onMessage(messaging, callback.onMessage);

            // Request notification permission and get token
            const permission = await Notification.requestPermission();
            if (permission !== "granted") {
                throw new Error("Notification permission not granted");
            }

            const token = await getToken(messaging, {
                vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
            });
            console.log({ token }, "fire base token***")
            if (token) {
                localStorage.setItem("fcm_token", token);
                return token;
            } else {
                throw new Error("No registration token available");
            }
        } catch (error) {
            console.error("FCM initialization error:", error);
            return null;
        }
    },

    getToken: async (shouldRequest: boolean = false): Promise<string | null> => {
        const tokenInLocalStorage = localStorage.getItem("fcm_token");
        if (tokenInLocalStorage) {
            return tokenInLocalStorage;
        }

        try {
            let permission = Notification.permission;
            if (permission === "default" && shouldRequest) {
                permission = await Notification.requestPermission();
            }
            if (permission !== "granted") {
                throw new Error("Notification permission not granted");
            }

            const messaging = getMessaging();
            const token = await getToken(messaging, {
                vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
            });

            if (token) {
                localStorage.setItem("fcm_token", token);
                return token;
            }
            return null;
        } catch (error) {
            console.error("Error retrieving FCM token:", error);
            return null;
        }
    },
};

export { firebaseCloudMessaging };