import { Page, PagePermission, PermissiblePage, UserNotificationSettings } from "@/typings/page";
import {DatabaseColumn, DatabaseDefinition, DatabaseFieldDataType, RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {KeyValueStore} from "@/typings/workspace";
import {View} from "@/typings/page";

export interface DatabaseMetadata {
    recordViewsMap?: {[key: string]: View};
    recordViewsOrder?: string[];
    [key: string]: any;
}

export interface Database {
    id: string
    name: string
    definition: DatabaseDefinition
    workspaceId: string
    srcPackageName: string
    srcVersionNumber: number
    srcVersionName: string
    packageName: string
    isMessagingEnabled: boolean
    versionNumber: number
    versionName: string
    createdById: string
    createdAt: string
    updatedAt: string
    deletedAt: string
    meta?: DatabaseMetadata
}

export interface PermissibleDatabase extends PermissiblePage {
    database: Database
}

export interface PermissibleDatabaseWithPermissions extends PermissibleDatabase {
    permissions: PagePermission[]
}

export interface Record {
    id: string
    databaseId: string
    createdById: string
    updatedById: string
    recordValues: RecordValues
    uniqueValue: string;
    summaryJSON: object 
    summaryText: string
    userNotificationSettings: UserNotificationSettings

    title: string
    coverImage?: string
    profileImage?: string
    createdAt: string
    updatedAt: string
    deletedAt: string
    meta?: KeyValueStore
}

export const RecordMetaKey = (type: "column", id: string) => {
    return `${type}:${id}`
}

interface RecordIdMap {
    [id: string]: {
        record: Record
        // processedRecord?: ProcessedDbRecord
    }
}

export interface AdjacentDatabases {
    [key: string]: {
        database?: Database,
        page?: Page,
        recordsMap?: RecordIdMap,
        error?: string
    }
}

export enum ActivityObjectType {
    Database = "database",
    Record = "record",
    Page = "page"
}

export enum ActivityType {
    ItemCreated = "item_created",
    ItemUpdated = "item_updated",
    ItemPublished = "item_published",
    ItemDeleted = "item_deleted",
    DatabaseColumnCreated = "db_column_created",
    DatabaseColumnUpdated = "db_column_updated",
    DatabaseColumnDeleted = "db_column_deleted",
    DatabaseColumnMadeUnique = "db_column_made_unique",
    DatabaseColumnRemovedUnique = "db_column_removed_unique",
    DatabaseColumnHidden = "db_column_hidden",
    DatabaseColumnShown = "db_column_shown",
    DatabaseColumnsReOrdered = "db_columns_reordered",
    SelectOptionsReOrdered = "select_options_reordered",
    CommentAdded = "comment_added",
    ValuesUpdated = "values_updated",
}

export enum ChangeType {
    NoChange = "no_change",
    FullProps = "full_props",
    ListOrder = "list_order",
    RecordValues = "record_values",
    CommentBody = "comment_body",
}

export interface DatabaseColumnChangeValue {
    element: DatabaseColumn,
    afterId: string
    beforeId: string
}

export interface ChangeData {
    changeType: ChangeType
    oldValue: boolean | number | string | object | DatabaseColumnChangeValue | Partial<DatabaseColumnChangeValue>
    newValue: boolean | number | string | object | DatabaseColumnChangeValue | Partial<DatabaseColumnChangeValue>
    meta?: object
}

export interface RecordValuesChangeData extends ChangeData {
    changeType: ChangeType.RecordValues
    oldValue: RecordValues
    newValue: RecordValues
    meta: {
        fields: {
            [key: string]: DatabaseFieldDataType
        }
    }
}


export interface Activity {
    id: number;
    workspaceId: string
    databaseId: string
    pageId: string
    recordId: string
    objectType: ActivityObjectType
    objectId: string
    createdById: string
    parentId: string
    activityType: ActivityType
    changeData: ChangeData
    isResolved: boolean
    createdAt: string
    updatedAt: string
    deletedAt: string
}

export interface RecordMatchMap  {
    [value: string]: {
        recordValue: RecordValues
        record?: Record
        compareValue: string
    }
}

export enum OnDuplicateAction {
    Ignore = "ignore",
    Update = "update",
    Reject = "reject",
}
