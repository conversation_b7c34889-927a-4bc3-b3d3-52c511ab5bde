import {RenderHeaderCellProps} from "react-data-grid";
import {EllipsisHorizontalIcon} from "@heroicons/react/24/outline";
import {Button} from "@/components/ui/button";
import {DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import React, {useRef, useState} from "react";
import {useWorkspace} from "@/providers/workspace";
import {RGDMeta} from "@/components/workspace/main/views/table";
import {DatabaseFieldTypeIcon} from "@/components/workspace/main/database/databaseFieldTypeIcon";
import {HeaderEditDropDownOptionsProps, NumberHeaderDropDownMore, TextHeaderDropDownMore} from "@/components/workspace/main/views/table/renderer/fields/text";
import {AIColumn, ButtonAction, ButtonGroupColumn, DatabaseColumn, DatabaseFieldDataType, ScannableCodeColumn, TextColumn, ActionButton} from "opendb-app-db-utils/lib/typings/db";
import {InputWithEnter} from "@/components/custom-ui/inputWithEnter";
import {MultiSelectHeaderDropDownMore} from "@/components/workspace/main/views/table/renderer/fields/select";
import {DateHeaderDropDownMore} from "@/components/workspace/main/views/table/renderer/fields/date";
import {Dialog, DialogContent, DialogHeader, DialogTitle} from "@/components/ui/dialog";
import {Label} from "@/components/ui/label";
import {Input} from "@/components/ui/input";
import "./../../../../../../custom-ui/taggableInput.css"
// import { TagOptionsMap} from "@/components/custom-ui/tagInput";
import {AIHeaderDropDownMore} from "@/components/workspace/main/views/table/renderer/fields/ai";
import {DerivedDropDownMore, DerivedFormulaEditor} from "@/components/workspace/main/views/table/renderer/fields/derived";
import {useViews} from "@/providers/views";
import {LinkedHeaderDropDownMore} from "@/components/workspace/main/views/table/renderer/fields/linked";
import {CircleExclamationIcon, PencilIcon, TrashIcon, PlusIcon} from "@/components/icons/FontAwesomeRegular";
import {getCompanyDbDefinition, getContactDbDefinition, getCustomerDbDefinition, getDatabasePackageName} from "opendb-app-db-utils/lib/utils/onboarding";
import {Switch} from "@/components/ui/switch";
import {useMaybeShared} from "@/providers/shared";
import {FileHeaderDropDownMore} from "@/components/workspace/main/views/table/renderer/fields/files";
import {SummarizeHeaderDropDownMore} from "@/components/workspace/main/views/table/renderer/fields/summarize";
import {DatabaseColumnSelect} from "@/components/workspace/main/common/databaseColumnSelect";
import useForceRender from "@/components/custom-ui/forceRender";
import {TaggableInput} from "@/components/custom-ui/taggableInput";
import {TagOptionsMap} from "@/components/custom-ui/tagInputHelpers";
import { getActionIcon } from "@/components/workspace/main/views/table/renderer/fields/buttonGroup";
import { ButtonEditor } from "@/components/workspace/main/views/table/renderer/fields/buttonGroup/buttoneditor";
import {ScannableCodeHeaderDropDownMore} from "@/components/workspace/main/views/table/renderer/fields/scannableCode";
import { Cross2Icon } from "@radix-ui/react-icons";
import { generateUUID } from "opendb-app-db-utils/lib/methods/string";
import { cn } from "@/lib/utils";
import { DNDSortable, SortItem } from "@/components/custom-ui/dndSortable";
import { useAlert } from "@/providers/alert";


const companyDef = getCompanyDbDefinition()
const contactsDef = getCustomerDbDefinition('')
const contactableDef = getContactDbDefinition()

const companiesColIds = companyDef.definition.columnIds
const contactsColIds = [...contactsDef.definition.columnIds, ...contactableDef.definition.columnIds]


const companySrcPackageName = getDatabasePackageName(companyDef)
const contactsSrcPackageName = getDatabasePackageName(contactsDef)


export const HeaderRenderer = <R, SR>({
                                          column,
                                          sortDirection,
                                          priority
                                      }: RenderHeaderCellProps<R, SR>) => {
    const {databaseStore, databaseErrorStore} = useWorkspace()
    const {updateDatabaseColumn, cache, deleteDatabaseColumn, updateDatabaseTitleColumn, makeDatabaseColumnUnique} = useViews()


    // @ts-ignore
    const meta: RGDMeta = column['__meta__']

    const dbColumn = meta.column

    // console.log("Header", {column, meta})
    // const fieldType = column.fieldType as DatabaseFieldDataType
    // const [isUpdating, setIsUpdating] = useState(false)
    const shared = useMaybeShared()

    const [open, setOpen] = useState(meta.triggerEdit || false)
    const [aiPromptOpen, setAIPromptOpen] = useState(false)
    const [scanContentOpen, setScanContentOpen] = useState(false)
    const [formulaEditorOpen, setFormulaEditorOpen] = useState(false)
    const [buttonGroupEditorOpen, setButtonGroupEditorOpen] = useState(false);


    const onUpdate = async (column: Partial<DatabaseColumn>) => {
        // setOpen(false)
        await updateDatabaseColumn(meta.databaseId, dbColumn.id, dbColumn.type, column)
    }

    const headerLocked = !!meta.headerLocked
    let isNameLocked = false
    let isDeleteLocked = false
    const currDb = databaseStore[meta.databaseId]
    if (currDb.database.srcPackageName === companySrcPackageName && companiesColIds.includes(dbColumn.id)) {
        isDeleteLocked = isNameLocked = true
    } else if ((currDb.database.isMessagingEnabled || currDb.database.srcPackageName === contactsSrcPackageName) && contactsColIds.includes(dbColumn.id)) {
        isDeleteLocked = isNameLocked = true
    }

    const isUnique = currDb.database.definition.uniqueColumnId === dbColumn.id
    const onDelete = () => {
        deleteDatabaseColumn(meta.databaseId, dbColumn)
    }

    let error = ''
    if (dbColumn.type === DatabaseFieldDataType.Linked) {
        if (!dbColumn.databaseId) error = 'Linked database not defined'
        else if (databaseErrorStore[dbColumn.databaseId]) error = databaseErrorStore[dbColumn.databaseId].error || ''
    }

    const setAsTitle = (setTitle: boolean) => {
        if (setTitle) updateDatabaseTitleColumn(currDb.database.id, dbColumn.id).then()
        else updateDatabaseTitleColumn(currDb.database.id, '').then()
    }
    const isTitleCol = currDb.database.definition.titleColumnId === dbColumn.id

    const firstRenderTriggerEditRef = useRef(false)

    const triggerEdit = meta.triggerEdit || false


    const {forceRender} = useForceRender()
    const openModal = (!firstRenderTriggerEditRef.current && triggerEdit) || open

    // console.log({triggerEdit, open, openModal})

    const onOpenChange = (o: boolean) => {
        firstRenderTriggerEditRef.current = true
        setOpen(o)
        if (o === open) forceRender()
    }

    // useEffect(() => {
    //     if (triggerEdit) {
    //         setTimeout(() => {
    //             firstRenderTriggerEditRef.current = true
    //         }, 500)
    //     }
    // }, [triggerEdit])

    return (
        <div className="r-header text-xs h-full flex items-center font-semibold gap-2 relative group"
             onContextMenu={e => {
                 e.preventDefault()
                 e.stopPropagation()
                 if (!headerLocked) {
                     onOpenChange(true)
                 }
             }}>
            <DatabaseFieldTypeIcon type={dbColumn.type}/>
            <div className="flex-1 truncate">{dbColumn.title}</div>
            {error && <span className="relative text-red-600 -right-8 group-hover:right-0" title={error}>
                <CircleExclamationIcon className="size-3"/>
            </span>}
            {!shared && !headerLocked && <>
                <DropdownMenu
                    // defaultOpen={open || meta.triggerEdit || false}
                    open={openModal}
                    onOpenChange={onOpenChange}>
                    <DropdownMenuTrigger asChild>
                        <Button variant='ghost' className="rounded-full h-auto p-1 -mr-1.5">
                            <EllipsisHorizontalIcon className='size-4'/>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56  rounded-none text-neutral-800 font-semibold" align="end">
                        <DropdownMenuGroup className="p-1 flex flex-col gap-2">
                            <InputWithEnter
                                value={dbColumn.title}
                                placeHolder="Field name"
                                disabled={isNameLocked}
                                onChange={v => {
                                    onUpdate({title: v.trim()}).then()
                                }}
                                wrapperClassname='h-8 p-1'
                                shortEnter/>

                            {dbColumn.type === DatabaseFieldDataType.Text ? <TextHeaderDropDownMore
                                                                              onUpdate={onUpdate}
                                                                              column={dbColumn}/>
                                                                          : <></>}

                            {dbColumn.type === DatabaseFieldDataType.Number ? <NumberHeaderDropDownMore
                                                                                onUpdate={onUpdate}
                                                                                column={dbColumn}/>
                                                                            : <></>}

                            {(dbColumn.type === DatabaseFieldDataType.Linked) ?
                             <LinkedHeaderDropDownMore
                                 onUpdate={onUpdate}
                                 column={dbColumn}/> : <></>}

                            {(dbColumn.type === DatabaseFieldDataType.Summarize) ?
                             <SummarizeHeaderDropDownMore
                                 database={currDb.database}
                                 onUpdate={onUpdate}
                                 column={dbColumn}/> : <></>}

                            {(dbColumn.type === DatabaseFieldDataType.Select
                                || dbColumn.type === DatabaseFieldDataType.Linked
                                || dbColumn.type === DatabaseFieldDataType.Person) ?
                             <MultiSelectHeaderDropDownMore
                                 onUpdate={onUpdate}
                                 column={dbColumn}/>
                                                                                   : <></>}

                            {dbColumn.type === DatabaseFieldDataType.Files ? <FileHeaderDropDownMore
                                                                               onUpdate={onUpdate}
                                                                               column={dbColumn}/>
                                                                           : <></>}

                            {dbColumn.type === DatabaseFieldDataType.AI ? <>
                                <AIHeaderDropDownMore
                                    editPrompt={() => setAIPromptOpen(true)}
                                    onUpdate={onUpdate}
                                    column={dbColumn}/>
                            </> : <></>}
                            {dbColumn.type === DatabaseFieldDataType.ScannableCode ? <>
                                <ScannableCodeHeaderDropDownMore
                                    editContent={() => setScanContentOpen(true)}
                                    onUpdate={onUpdate}
                                    column={dbColumn}/>
                            </> : <></>}
                            {dbColumn.type === DatabaseFieldDataType.Derived ? <>
                                <DerivedDropDownMore
                                    edit={() => setFormulaEditorOpen(true)}
                                    onUpdate={onUpdate}
                                    column={dbColumn}/>
                            </> : <></>}
                            {dbColumn.type === DatabaseFieldDataType.ButtonGroup ? <>
    <ButtonGroupHeaderDropDownMore
        editButtons={() => setButtonGroupEditorOpen(true)}
        onUpdate={onUpdate}
        column={dbColumn}/>
</> : <></>}


                            {(dbColumn.type === DatabaseFieldDataType.Date
                                || dbColumn.type === DatabaseFieldDataType.CreatedAt
                                || dbColumn.type === DatabaseFieldDataType.UpdatedAt) ? <DateHeaderDropDownMore
                                 onUpdate={onUpdate}
                                 column={dbColumn}/> : <></>}

                        </DropdownMenuGroup>
                        {/*<DropdownMenuGroup className="p-1">*/}
                        {/*    <DropdownMenuItem className="text-xs rounded-none p-2">*/}
                        {/*        Hide*/}
                        {/*    </DropdownMenuItem>*/}
                        {/*    <DropdownMenuItem className="text-xs rounded-none p-2">*/}
                        {/*        Filter*/}
                        {/*    </DropdownMenuItem>*/}
                        {/*    <DropdownMenuItem className="text-xs rounded-none p-2">*/}
                        {/*        Sort 0 - 9*/}
                        {/*    </DropdownMenuItem>*/}
                        {/*    <DropdownMenuItem className="text-xs rounded-none p-2">*/}
                        {/*        Sort 9 - 0*/}
                        {/*    </DropdownMenuItem>*/}
                        {/*</DropdownMenuGroup>*/}
                        {/*<DropdownMenuSeparator className=""/>*/}

                        {[DatabaseFieldDataType.Text, DatabaseFieldDataType.UUID].includes(dbColumn.type) && <>
                            <DropdownMenuGroup className="p-1">
                                <Label className='p-2 pl-1 text-xs w-full text-left justify-start items-center font-semibold rounded-none gap-2 hover:border-neutral-200 flex'>
                                    <Switch className="h-4 w-8" thumbClassName="!size-3" onCheckedChange={setAsTitle} checked={isTitleCol}/>
                                    <span>Set as title</span>
                                </Label>
                            </DropdownMenuGroup>
                            <DropdownMenuSeparator/>
                            <DropdownMenuGroup className="p-1">
                                <Label className='p-2 pl-1 text-xs w-full text-left justify-start items-center font-semibold rounded-none gap-2 hover:border-neutral-200 flex'>
                                    <Switch className="h-4 w-8" thumbClassName="!size-3"
                                            onCheckedChange={unique => makeDatabaseColumnUnique(currDb.database.id, dbColumn.id, unique)}
                                            checked={isUnique}/>
                                    <span>Enforce Uniqueness</span>
                                </Label>
                            </DropdownMenuGroup>
                        </>}
                        {[DatabaseFieldDataType.Text].includes(dbColumn.type) && <>
                            <DropdownMenuSeparator/>
                            <DropdownMenuGroup className="p-1">
                                <Label className='p-2 pl-1 text-xs w-full text-left justify-start items-center font-semibold rounded-none gap-2 hover:border-neutral-200 flex'>
                                    <Switch className="h-4 w-8" thumbClassName="!size-3"
                                            onCheckedChange={c => onUpdate({isLong: c})}
                                            checked={(dbColumn as TextColumn).isLong}/>
                                    <span>Enable long text</span>
                                </Label>
                            </DropdownMenuGroup>

                        </>}

                        {!isDeleteLocked && <>
                            <DropdownMenuSeparator/>
                            <DropdownMenuGroup className="p-1">
                                <DropdownMenuItem className="text-xs rounded-none p-2" onClick={onDelete}>
                                    Delete
                                </DropdownMenuItem>
                            </DropdownMenuGroup>
                        </>}
                    </DropdownMenuContent>
                </DropdownMenu>
            </>}

            {dbColumn.type === DatabaseFieldDataType.AI && aiPromptOpen && <>
                <AIPromptEditor
                    column={dbColumn}
                    databaseId={meta.databaseId}
                    close={() => setAIPromptOpen(false)}
                    onUpdate={onUpdate}
                />
            </>}
            {dbColumn.type === DatabaseFieldDataType.ScannableCode && scanContentOpen && <>
                <ScannableContentEditor
                    column={dbColumn}
                    databaseId={meta.databaseId}
                    close={() => setScanContentOpen(false)}
                    onUpdate={onUpdate}
                />
            </>}
            {(dbColumn.type === DatabaseFieldDataType.Derived && formulaEditorOpen) && <>
                <DerivedFormulaEditor
                    database={currDb}
                    column={dbColumn}
                    doUpdate={onUpdate}
                    close={() => setFormulaEditorOpen(false)}
                />

            </>}
            {buttonGroupEditorOpen && <ButtonGroupConfigEditor
                column={dbColumn}
                databaseId={meta.databaseId}
                close={() => setButtonGroupEditorOpen(false)}
                onUpdate={onUpdate}
            />}
        </div>
    );
};


const AIPromptEditor = (props: HeaderEditDropDownOptionsProps & {
    close: () => void,
    databaseId: string
}) => {
    const dbColumn = props.column as AIColumn

    const {databaseStore} = useWorkspace()
    const [title, setTitle] = useState(dbColumn.title)
    const [maxWordOutput, setMaxWordOutput] = useState(dbColumn.maxWordOutput || 1000)
    const [attachmentColumnIds, setAttachmentColumnIds] = useState(dbColumn.attachmentColumnIds || [])
    // const [prompt, setPrompt] = useState(dbColumn.prompt || '')

    const promptRef = useRef(dbColumn.prompt || '')

    const columnsMap = databaseStore[props.databaseId]?.database?.definition?.columnsMap
    const tagOptions = (): TagOptionsMap => {
        if (!columnsMap) return {}
        const tagOptions: TagOptionsMap = {}

        for (const col of Object.values(columnsMap)) {
            if (col.id === dbColumn.id) continue
            // if ([DatabaseFieldDataType.Files].includes(dbColumn.type)) continue

            tagOptions[col.id] = {
                tag: `{{${col.id}}}`,
                description: '',
                label: col.title
            }
        }
        return tagOptions
    }
    const tagOpts = tagOptions()
    return (
        <>
            <Dialog open={true}>
                <DialogContent className="max-w-[600px] !rounded-none p-4" hideCloseBtn>
                    <DialogHeader>
                        <DialogTitle className="font-bold text-sm">AI Prompt</DialogTitle>
                    </DialogHeader>
                    <div className="flex flex-col gap-2 py-2">
                        <div className="flex flex-col flex-1 gap-1">
                            <Label className="block text-xs font-medium leading-6 text-gray-900">Name</Label>
                            <Input
                                placeholder="Field name"
                                value={title}
                                onChange={e => setTitle(e.target.value)}
                                autoCapitalize="none"
                                autoComplete="off"
                                spellCheck="false"
                                autoCorrect="off"
                                className="rounded-none text-xs"
                            />
                        </div>
                        <div className="flex flex-col flex-1 gap-1">
                            <Label className="block text-xs font-medium leading-6 text-gray-900">Write your prompt,
                                (mention @columns as needed)</Label>
                            <TaggableInput
                                id="aiPromptTagInput"
                                tagOptionsMap={tagOpts}
                                value={dbColumn.prompt || ''}
                                onChange={v => {
                                    console.log("On change:", v)
                                    promptRef.current = v
                                }}
                                stripTagsInOutput
                                placeholder="Write your prompt, mention @columns as needed"
                                showVariables
                            />
                        </div>
                        <div className="flex flex-col flex-1 gap-1">
                            <Label className="block text-xs font-medium leading-6 text-gray-900">Max Output Words (in 1000s, rounded up)</Label>
                            <Input
                                placeholder="Max output words"
                                value={maxWordOutput}
                                onChange={e => {
                                    const num = Number(e.target.value)
                                    setMaxWordOutput(num)
                                }}
                                autoCapitalize="none"
                                autoComplete="off"
                                spellCheck="false"
                                autoCorrect="off"
                                type="number"
                                step={1000}
                                min={1000}
                                className="rounded-none text-xs"
                            />
                            <span className="font-medium text-muted-foreground text-xs text-[11px]">
                                Max tokens sent to the AI, rounded up to the next 1000.&nbsp;
                                {Math.max(maxWordOutput, 1000)} words = {Math.ceil(Math.max(maxWordOutput, 1000) / 1000)} content unit.
                            </span>
                        </div>
                        <div className="flex flex-col flex-1 gap-1">
                            <Label className="block text-xs font-medium leading-6 text-gray-900">Attachments</Label>
                            <DatabaseColumnSelect
                                databaseId={props.databaseId}
                                isMultiple
                                selected={attachmentColumnIds}
                                filterFor={DatabaseFieldDataType.Files}
                                onChange={setAttachmentColumnIds}/>
                            <span className="font-medium text-muted-foreground text-xs text-[11px]">
                                Text from attachments is extracted and sent to the AI. PDFs and images use OCR. Large files may increase AI content unit usage.
                            </span>
                        </div>
                    </div>
                    <div className="flex flex-row-reverse">
                        <Button
                            onClick={() => {
                                props.onUpdate({prompt: promptRef.current, title: title.trim() || dbColumn.title, attachmentColumnIds, maxWordOutput: Math.max(maxWordOutput, 1000)})
                                props.close()
                            }}
                            className="mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                            Save
                        </Button>
                        <Button
                            onClick={props.close}
                            variant='ghost'
                            className="mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                            Cancel
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

        </>
    )
}


const ScannableContentEditor = (props: HeaderEditDropDownOptionsProps & {
    close: () => void,
    databaseId: string
}) => {
    const dbColumn = props.column as ScannableCodeColumn

    const {databaseStore} = useWorkspace()

    const contentRef = useRef(dbColumn.derivation || '')

    const columnsMap = databaseStore[props.databaseId]?.database?.definition?.columnsMap
    const tagOptions = (): TagOptionsMap => {
        if (!columnsMap) return {}
        const tagOptions: TagOptionsMap = {}

        for (const col of Object.values(columnsMap)) {
            if (col.id === dbColumn.id || col.type === DatabaseFieldDataType.ScannableCode) continue
            tagOptions[col.id] = {
                tag: `{{${col.id}}}`,
                description: '',
                label: col.title
            }
        }
        return tagOptions
    }
    const tagOpts = tagOptions()
    return (
        <>
            <Dialog open={true}>
                <DialogContent className="max-w-[600px] !rounded-none p-4" hideCloseBtn>
                    <DialogHeader>
                        <DialogTitle className="font-bold text-sm">Scannable Code Content</DialogTitle>
                    </DialogHeader>
                    <div className="flex flex-col gap-2 py-2">
                        <div className="flex flex-col flex-1 gap-1">
                            <Label className="block text-xs font-medium leading-6 text-gray-900">Write your prompt,
                                (mention @columns as needed)</Label>
                            <TaggableInput
                                id="scanCodePromptTag"
                                tagOptionsMap={tagOpts}
                                value={dbColumn.derivation || ''}
                                onChange={v => {
                                    console.log("On change:", v)
                                    contentRef.current = v
                                }}
                                stripTagsInOutput
                                placeholder="Enter the content of the code, mention @columns as needed"
                                showVariables
                                debounceTimeoutMS={0}
                            />
                        </div>
                    </div>
                    <div className="flex flex-row-reverse">
                        <Button
                            onClick={() => {
                                props.onUpdate({derivation: contentRef.current})
                                props.close()
                            }}
                            className="mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                            Save
                        </Button>
                        <Button
                            onClick={props.close}
                            variant='ghost'
                            className="mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                            Cancel
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

        </>
    )
}


const ButtonGroupHeaderDropDownMore = (props: HeaderEditDropDownOptionsProps & {
    editButtons: () => void
}) => {
    return (
        <>
            <DropdownMenuItem
                onClick={props.editButtons}
                className="text-xs rounded-none p-2"
            >
                <span className="flex-1 rounded-full capitalize">Edit Buttons</span>
            </DropdownMenuItem>
        </>
    );
};


const ButtonGroupConfigEditor = (props: HeaderEditDropDownOptionsProps & {
    close: () => void,
    databaseId: string
}) => {
    const dbColumn = props.column as ButtonGroupColumn;
    const [saveTriggered, setSaveTriggered] = useState(false);

  

    const [buttons, setButtons] = useState<any[]>(
        Array.isArray(dbColumn.buttons) ?
            dbColumn.buttons.map((button, index) => {
              

                if ('actions' in button && Array.isArray(button.actions)) {
                    return button as ActionButton;
                }
                else if ('actionType' in button) {
                    const newButton = {
                        id: button.id,
                        label: button.label,
                        isReady: button.isReady,
                        actions: [button as any]
                    };
                    return newButton;
                }
                else {
                    return {
                        id: button.id,
                        label: button.label,
                        isReady: button.isReady,
                        actions: []
                    };
                }
            }) : []
    );
    const [selectedButtonIndex, setSelectedButtonIndex] = useState<number | null>(null);
    const { confirm } = useAlert();

    const addButton = () => {
        const newButton: ActionButton = {
            id: generateUUID(),
            label: "New Button",
            isReady: true,
            actions: []
        };
        setButtons([...buttons, newButton]);
        setSelectedButtonIndex(buttons.length);
    };

    const updateButton = (index: number, updatedButton: any) => {
        const newButtons = [...buttons];
        newButtons[index] = updatedButton;
        setButtons(newButtons);
    };

    const deleteButton = (index: number) => {
        confirm(
            'Delete Button',
            'Are you sure you want to delete this button?',
            () => {
                const newButtons = [...buttons];
                newButtons.splice(index, 1);
                setButtons(newButtons);
            },
            undefined,
            undefined,
            true
        );
    };

    const reorderButtons = (items: SortItem<ActionButton>[]) => {
        setButtons(items.map(item => item.data));
    };

    const saveChanges = () => {
        props.onUpdate({ buttons: buttons });
        props.close();
    };

    const handleSaveButtonEditor = () => {
        setSaveTriggered(true);
        // Reset the trigger after a short delay
        setTimeout(() => setSaveTriggered(false), 100);
    };

    if (selectedButtonIndex !== null) {
        return (
            <Dialog open={true} onOpenChange={() => {}}>
                <DialogContent className="max-w-2xl max-h-[90vh] !rounded-none p-0" hideCloseBtn>
                    <DialogHeader className="p-4 border-b flex flex-row items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedButtonIndex(null)}
                                className="p-1 h-6 w-6 hover:bg-gray-100"
                            >
                                <Cross2Icon className="h-4 w-4" />
                            </Button>
                            <DialogTitle className="text-sm font-semibold">Button</DialogTitle>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                className="text-xs rounded-full"
                                onClick={() => setSelectedButtonIndex(null)}
                            >
                                Revert
                            </Button>
                            <Button
                                size="sm"
                                className="text-xs rounded-full bg-black text-white hover:bg-gray-800"
                                onClick={handleSaveButtonEditor}
                            >
                                Save
                            </Button>
                        </div>
                    </DialogHeader>
                    <div className="max-h-[calc(90vh-120px)] overflow-auto mention-input-container">
                        <ButtonEditor
                            button={buttons[selectedButtonIndex]}
                            databaseId={props.databaseId}
                            contextIsRecord={true}
                            saveTriggered={saveTriggered}
                            onSave={(updatedButton: any) => {
                                updateButton(selectedButtonIndex, updatedButton);
                                setSelectedButtonIndex(null);
                            }}
                            onCancel={() => setSelectedButtonIndex(null)}
                        />
                    </div>
                </DialogContent>
            </Dialog>
        );
    }

    return (
        <Dialog open={true} onOpenChange={() => {}}>
            <DialogContent className="max-w-2xl max-h-[90vh] !rounded-none p-0" hideCloseBtn>
                <DialogHeader className="p-4 border-b flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={props.close}
                            className="p-1 h-6 w-6 hover:bg-gray-100"
                        >
                            <Cross2Icon className="h-4 w-4" />
                        </Button>
                        <DialogTitle className="text-sm font-semibold">Edit buttons</DialogTitle>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            className="text-xs rounded-full"
                            onClick={props.close}
                        >
                            Revert
                        </Button>
                        <Button
                            onClick={saveChanges}
                            size="sm"
                            className="text-xs rounded-full bg-black text-white hover:bg-gray-800"
                        >
                            Save
                        </Button>
                    </div>
                </DialogHeader>
                <div className="p-4 sm:p-4 max-h-[calc(90vh-120px)] overflow-auto">
                    <div className="flex flex-col gap-4">
                        <div className="flex flex-col gap-2">
                            <h3 className="text-sm font-medium">Buttons</h3>
                            <div className="border rounded-md overflow-hidden">
                                {buttons.length === 0 ? (
                                    <div className="text-gray-400 text-center py-4">
                                        No buttons configured
                                    </div>
                                ) : (
                                    <DNDSortable
                                        items={buttons.map(button => ({ id: button.id, data: button }))}
                                        itemRenderer={(index, item, isDragging) => {
                                            const button = item.data;
                                            return (
                                                <div
                                                    className={cn(
                                                        "flex items-center justify-between p-3 hover:bg-gray-50 bg-white",
                                                        index !== buttons.length - 1 && "border-b",
                                                        isDragging && "shadow-lg"
                                                    )}
                                                >
                                                    <div className="flex items-center gap-2">
                                                        {button.actions.length > 0 && getActionIcon(button.actions[0].actionType)}
                                                        <span className="text-sm">{button.label}</span>
                                                        {button.actions.length > 1 && (
                                                            <span className="text-xs text-gray-500">
                                                                ({button.actions.length} actions)
                                                            </span>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <Button
                                                            variant="ghost"
                                                            className="p-1 h-6 w-6 text-black"
                                                            onClick={() => {
                                                                const buttonIndex = buttons.findIndex(b => b.id === button.id);
                                                                setSelectedButtonIndex(buttonIndex);
                                                            }}
                                                        >
                                                            <PencilIcon className="h-3 w-3" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            className="p-1 h-6 w-6 text-black"
                                                            onClick={() => {
                                                                const buttonIndex = buttons.findIndex(b => b.id === button.id);
                                                                deleteButton(buttonIndex);
                                                            }}
                                                        >
                                                            <TrashIcon className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            );
                                        }}
                                        onChange={reorderButtons}
                                        useDragHandle={true}
                                        handlePosition="center"
                                        useDragOverlay={true}
                                    />
                                )}
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-xs mt-2 w-full text-gray-600 hover:text-gray-800"
                                onClick={addButton}
                            >
                                <PlusIcon className="h-4 w-4 mr-1" />
                                <span>Add Button</span>
                            </Button>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};


