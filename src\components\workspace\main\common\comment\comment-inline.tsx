import React from 'react'


// should be clickable
// mark as yellow
const CommentInline = createReactInlineContentSpec(
    {
        type: "comment",
        propSchema: {
            text: {
                default: "Unknown",
            },
        },
        content: "none",
    } as const,
    {
        render: (props) => (
            <span className="bn-comment" onClick={() => {
                props.inlineContent.content
            }}>
                <span className="bg-yellow-200">
                    {props.inlineContent.props.text}</span>
            </span>
        ),
    }
);


import { createReactInlineContentSpec, DefaultReactSuggestionItem } from "@blocknote/react";



export const Mention = createReactInlineContentSpec(
    {
        type: "mention",
        propSchema: {
            user: {
                default: "Unknown",
            },
            id: {
                default: "unknown-id",
            },
        },
        content: "none",
    } as const,
    {
        render: (props) => (
            <span className="bn-mention">
                <span className="text-muted-foreground">
                    <span className="text-muted-foreground">@</span>
                    {props.inlineContent.props.user}</span>
            </span>
        ),
    }
);


export default CommentInline