"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Smile } from "lucide-react"
import { EmojiPicker } from "@/components/custom-ui/emojiPicker"
import { Reaction } from "@/typings/comment"

interface ReactionBarProps {
  reactions: Reaction[]
  onAddReaction: (emoji: string) => void
  onReactionClick?: (emoji:string) => void
  currentUserId: string
}

export function ReactionBar({ reactions, onAddReaction, onReactionClick, currentUserId }: ReactionBarProps) {
  const handleEmojiSelect = (emoji: string) => {
    onAddReaction(emoji)
  }

  const handleReactionClick = (reaction: Reaction) => {
    if (onReactionClick) {
      onReactionClick(reaction.emoji)
    }
  }

  const isUserReacted = (reaction: Reaction) => {
    return reaction.userIds.includes(currentUserId)
  }

  return (
    <div className="flex flex-wrap items-center gap-1  pt-2 ">
      {reactions.map((reaction, index) => (
        <Button
          key={`${reaction.emoji}-${index}`}
          variant="ghost"
          size="sm"
          className={`h-7 px-2 py-1 text-xs rounded-full border transition-all hover:scale-105 ${
            isUserReacted(reaction)
              ? "bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
              : "bg-gray-50 border-gray-200 hover:bg-gray-100"
          }`}
          onClick={() => handleReactionClick(reaction)}
        >
          <span className="mr-1">{reaction.emoji}</span>
          <span className="font-medium">{reaction.userIds.length}</span>
        </Button>
      ))}

      {/* Emoji Picker - Always last item */}
      <EmojiPicker emoji=""  onChange={(emoji) => handleEmojiSelect(emoji)} triggerClassName="relative shadow-none size-4" trigger={
                            <Button
          variant="ghost"
          size="sm"
          className="h-7 w-7 p-0 rounded-full border  border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all"
        >
          <Smile className="h-3.5 w-3.5 text-gray-500" />
        </Button>
                          } />
    </div>
  )
}
