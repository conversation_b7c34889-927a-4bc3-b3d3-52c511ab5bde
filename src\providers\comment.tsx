import { createContext, useContext, useMemo, useState, useEffect, useCallback, useRef } from "react";
import * as Y from "yjs";
import { useAuth } from "@/providers/user";
import { useWorkspace } from "@/providers/workspace";
import { Comment, CommentEnum, Thread } from "@/typings/comment";
import { ItemDataLoad } from "@/api/common";
import { useWorkspaceSocket } from "./workspaceSocket";
import { BlockNoteEditor } from "@blocknote/core";
import { ThreadData } from "@blocknote/core/comments";
import { filter } from "lodash";

export enum CommentEvent {
  COMMENT = "comment",
  NEW_COMMENT = "new-comment",
  DELETED_COMMENT = "deleted-comment",
  NEW_REACTION = "new-reaction",
  UPDATED_COMMENT = "updated-comment",
  UPDATE_SUGGESTION = "updated-suggestion",
  NEW_SUGGESTION = "new-suggestion",
  DELETED_SUGGESTION = "deleted-suggestion",
}

interface CommentContextType {
  threads: ThreadData[];
  setYMap: (yMap: Y.Map<any> | null) => void;
  filteredThreads: ThreadData[];
  editor: BlockNoteEditor<any, any, any> | undefined;
  setEditor: (val: BlockNoteEditor<any, any, any> | undefined) => void;
  isLoading: boolean;
  toggleSuggestionMode: () => void;
  suggestionMode: boolean;
  filterComments: (filter: {
    type?: 'all' | 'comments' | 'suggestions';
    status?: 'all' | 'resolved' | 'unresolved';
    authorId?: string;
  }) => void;
  commentFilter: {
    type: 'all' | 'comments' | 'suggestions';
    status: 'all' | 'resolved' | 'unresolved';
    userId?: string;
  };
  refreshThreads: () => void;
}


const CommentContext = createContext<CommentContextType | undefined>(undefined);

interface CommentProviderProps {
  children: React.ReactNode;
  recordId?: string | null;
  databaseId?: string | null;
  documentId?: string | null;
}

export function CommentProvider({
  children,
  documentId,
  databaseId,
  recordId,
}: CommentProviderProps) {
  const { user, token } = useAuth();
  const { workspace } = useWorkspace();
  const { socket, isConnected } = useWorkspaceSocket();

  const [editor, setEditorState] = useState<BlockNoteEditor<any, any, any> | undefined>(undefined);
  const [yMap, setYMap] = useState< Y.Map<any> | null>(null);
  const [suggestionMode, setSuggestionMode] = useState(false);
  const [threads, setThreads] = useState<ThreadData[]>([]);
  const [commentFilter, setCommentFilter] = useState<{
    type: 'all' | 'comments' | 'suggestions';
    status: 'all' | 'resolved' | 'unresolved';
    userId?: string;
  }>({ type: 'all', status: 'all' });

  // Keep track of last threads snapshot to detect changes
  const lastThreadsRef = useRef<string>('');
  // Track if we're currently updating to prevent recursive calls
  const isUpdatingRef = useRef(false);
  
  // Wrapper for setEditor - REMOVE dependency on editor to prevent loop
  const setEditor = useCallback((newEditor: BlockNoteEditor<any, any, any> | undefined) => {
    console.log('setEditor called:', {
      from: !!editor,
      to: !!newEditor,
      hasComments: !!newEditor?.comments,
      hasThreadStore: !!newEditor?.comments?.threadStore
    });
    setEditorState(newEditor);
  }, []); // Remove [editor] dependency

  // Function to get all threads from editor - STABLE reference
  const getAllThreads = useCallback((): ThreadData[] => {
    console.log('getAllThreads called:', {
      editor: !!editor,
      comments: !!editor?.comments,
      threadStore: !!editor?.comments?.threadStore
    });
    
    if (!editor?.comments?.threadStore) {
      console.log('No threadStore available');
      return [];
    }
    
    try {
      const threadsMap = editor.comments.threadStore.getThreads();
      console.log('threadsMap from store:', threadsMap);
      
      if (!threadsMap) {
        console.log('threadsMap is null/undefined');
        return [];
      }
      
      const threads = Array.from(threadsMap?.values())
      console.log('Converted threads:', threads);
      return threads;
    } catch (error) {
      console.warn('Error getting threads from threadStore:', error);
      return [];
    }
  }, [editor]); // Keep editor dependency but make updateThreads stable

  // Update threads function - PREVENT recursive calls
  const updateThreads = useCallback(() => {
    // Prevent recursive calls
    if (isUpdatingRef.current) {
      console.log('updateThreads: Already updating, skipping');
      return;
    }
    
    isUpdatingRef.current = true;
    
    try {
      const allThreads = getAllThreads();
      const threadsSnapshot = JSON.stringify(allThreads.map(t => ({ id: t.id, resolved: t.resolved, type: t.type })));
      
      console.log('updateThreads called:', {
        editor: !!editor,
        threadStore: !!editor?.comments?.threadStore,
        threadsCount: allThreads.length,
        allThreads: allThreads.map(t => ({ id: t.id, resolved: t.resolved, type: t.type }))
      });
      
      // Only update if threads actually changed
      // if (threadsSnapshot !== lastThreadsRef.current) {
        lastThreadsRef.current = threadsSnapshot;
      console.log('Setting threads:', allThreads);
        setThreads(allThreads);
      // }
    } finally {
      isUpdatingRef.current = false;
    }
  }, [getAllThreads]); // Stable dependency

  // Function to apply filters to threads - STABLE reference
  const getFilteredThreads = useCallback((allThreads: ThreadData[]): ThreadData[] => {
    let filtered = [...allThreads];

    // Filter by status
    if (commentFilter.type === 'all') {
      filtered = filtered.filter(thread => thread);
    }

    if (commentFilter.status === 'resolved') {
      filtered = filtered.filter(thread => thread.resolved);
    } else if (commentFilter.status === 'unresolved') {
      filtered = filtered.filter(thread => !thread.resolved);
    }
    else if (commentFilter.status === 'all') {
      filtered = filtered.filter(thread => thread);
    }



    // Filter by type
    // if (commentFilter.type === 'comments') {
    //   filtered = filtered.filter(thread => thread.type === CommentEnum.COMMENT);
    // } else if (commentFilter.type === 'suggestions') {
    //   filtered = filtered.filter(thread => thread.type === CommentEnum.SUGGESTION);
    // }

    // Filter by userId
    if (commentFilter.userId) {
      filtered = filtered.filter(thread => thread.metadata.userId === commentFilter.userId);
    }

    return filtered;
  }, [commentFilter]);

  // Public method to manually refresh threads
  const refreshThreads = useCallback(() => {
    updateThreads();
  }, [updateThreads]);

  // Set up reactive updates when ThreadStore changes
  useEffect(() => {
    if (!editor?.comments?.threadStore) return;

    console.log('Setting up thread store listeners');

    // Initial load
    updateThreads();

    const threadStore = editor.comments.threadStore;
   
    
    // Check if this is a YjsThreadStore by looking for the yMap property
    // if ('yMap' in threadStore) {
    //   yMap = (threadStore as any).yMap;
    // }

    let unsubscribe: (() => void) | null = null;

    if (yMap) {
      // Listen to Yjs Map changes with debouncing
      let timeoutId: NodeJS.Timeout | null = null;
      
      const handleYjsChange = () => {
        // Clear existing timeout
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        // Debounce the update
        timeoutId = setTimeout(() => {
          console.log('Yjs change detected, updating threads');
          updateThreads();
        }, 100); // 100ms debounce
      };

      yMap.observeDeep(handleYjsChange);
      unsubscribe = () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        yMap!.unobserveDeep(handleYjsChange);
      };
    // } else {
    //   // Fallback: Use polling for non-Yjs thread stores (with longer interval)
    //   const interval = setInterval(() => {
    //     console.log('Polling for thread updates');
    //     updateThreads();
    //   }, 5000); // Increased to 5 seconds to reduce load
    //   unsubscribe = () => clearInterval(interval);
    // }

    // return () => {
    //   console.log('Cleaning up thread store listeners');
    //   if (unsubscribe) {
    //     unsubscribe();
    //   }
      // };
    }
  }, [editor, updateThreads]);

  // REMOVE this effect - it was causing loops
  // useEffect(() => {
  //   if (editor?.comments?.threadStore) {
  //     updateThreads();
  //   }
  // }, [commentFilter, updateThreads]);

  // Calculate filtered threads
  const filteredThreads = useMemo(() => {
    const filtered = getFilteredThreads(threads);
    console.log('filteredThreads calculation:', {
      totalThreads: threads.length,
      filteredCount: filtered.length,
      filter: commentFilter
    });
    return filtered;
  }, [threads, getFilteredThreads, commentFilter]);

  const toggleSuggestionMode = useCallback(() => {
    setSuggestionMode(prev => !prev);
  }, []);

  const filterComments = useCallback((filter: {
    type?: 'all' | 'comments' | 'suggestions';
    status?: 'all' | 'resolved' | 'unresolved';
    authorId?: string;
  }) => {
    setCommentFilter(prev => ({
      type: filter.type ?? prev.type,
      status: filter.status ?? prev.status,
      userId: filter.authorId ?? prev.userId,
    }));
  }, []);

  const value: CommentContextType = {
    threads,
    setYMap,
    filteredThreads,
    editor,
    setEditor,
    isLoading: false,
    toggleSuggestionMode,
    suggestionMode,
    filterComments,
    commentFilter,
    refreshThreads,
  };

  return <CommentContext.Provider value={value}>{children}</CommentContext.Provider>;
}

export function useComments(): CommentContextType {
  const context = useContext(CommentContext);
  if (!context) {
    throw new Error("useComments must be used within a CommentProvider");
  }
  return context;
}