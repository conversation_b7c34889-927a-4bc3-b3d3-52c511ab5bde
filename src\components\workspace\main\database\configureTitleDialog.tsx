"use client";
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Cross2Icon } from "@radix-ui/react-icons";
import { Database } from "@/typings/database";
import { useAlert } from "@/providers/alert";
import { useAuth } from "@/providers/user";
import { useWorkspace } from "@/providers/workspace";
import { useViews } from "@/providers/views";
import { setTitleFormat as setTitleFormatAPI } from "@/api/database";
import { useEffect, useCallback, useMemo } from "react";
import { MentionInput, MentionInfo } from "@/components/custom-ui/mentionInput";

interface ConfigureTitleDialogProps {
    database: Database;
    close: () => void;
}

export const ConfigureTitleDialog: React.FC<ConfigureTitleDialogProps> = ({database,close}) => {
    const { toast } = useAlert();
    const { token } = useAuth();
    const { workspace } = useWorkspace();
    const { refreshDatabase } = useViews();

    const getInitialTitleFormat = useCallback((): string => {
        if (database?.definition?.titleFormat && typeof database.definition.titleFormat === 'string') {
            return database.definition.titleFormat;
        }
        if (database?.definition?.titleColumnId && typeof database.definition.titleColumnId === 'string') {
            return `{{${database.definition.titleColumnId}}}`;
        }
        return '';
    }, [database?.definition?.titleFormat, database?.definition?.titleColumnId]);

    const [titleFormat, setTitleFormat] = useState<string>(getInitialTitleFormat());
    useEffect(() => {
        setTitleFormat(getInitialTitleFormat());
    }, [getInitialTitleFormat]);

    const keyMap = useMemo((): Record<string, MentionInfo> => {
        const map: Record<string, MentionInfo> = {};
        Object.values(database.definition.columnsMap).forEach(column => {
            map[column.id] = {
                label: column.title,
                tag: `{{${column.id}}}`
            };
        });
        return map;
    }, [database.definition.columnsMap]);

    const handleSave = async () => {
        try {
            if (!titleFormat || typeof titleFormat !== 'string' || !titleFormat.trim()) {
                toast.error("Please configure a title format");
                return;
            }

            if (!token) {
                toast.error("Authentication required");
                return;
            }
            const initialTitleFormat = getInitialTitleFormat();
            if (titleFormat.trim() === initialTitleFormat.trim()) {
                toast.info("Title format is already up to date");
                return;
            }
            const response = await setTitleFormatAPI(token.token,workspace.workspace.id, database.id, titleFormat);
            if (response.isSuccess && !response.error) {
                await refreshDatabase(database.id);
                toast.success("Title format updated successfully");
                close();
            } else {
                throw new Error(response.error || 'Failed to update title format');
            }
        } catch (error) {
            console.error("Error saving title format:", error);
            toast.error("Failed to update title format");
        }
    };




    return (
        <Dialog open={true} onOpenChange={() => {}}>
            <DialogContent className="max-w-[95vw] sm:max-w-[600px] max-h-[90vh] overflow-y-auto !rounded-none p-0" hideCloseBtn>
                <DialogHeader className="p-4 border-b flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={close}
                            className="p-1 h-6 w-6 hover:bg-gray-100"
                        >
                            <Cross2Icon className="h-4 w-4" />
                        </Button>
                        <DialogTitle className="text-sm font-semibold">Configure Record Title</DialogTitle>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            onClick={close}
                            variant="outline"
                            size="sm"
                            className="text-xs rounded-full"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSave}
                            size="sm"
                            className="text-xs rounded-full bg-black text-white hover:bg-gray-800"
                        >
                            Save
                        </Button>
                    </div>
                </DialogHeader>
                <div className="p-4 sm:p-4">
                    <div className="flex flex-col gap-4">
                        <div className="flex flex-col gap-2">
                            <label className="block text-xs font-medium leading-6 text-gray-900">
                                Title Format
                            </label>
                            <MentionInput
                                keyMap={keyMap}
                                value={titleFormat}
                                onChange={setTitleFormat}
                                placeholder="Type @ to mention columns... e.g., @firstName @lastName"
                                className="w-full min-h-12 text-xs border rounded-none p-3"
                            />
                            <p className="text-xs text-gray-500">
                                Example: @firstName @lastName - text
                            </p>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};