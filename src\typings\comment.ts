// import { Block } from "@blocknote/core";

export type Reaction = {
    emoji: string; 
    userIds: string[];
    createdAt: Date;
};
export enum CommentEnum {
    COMMENT = "comment",
    SUGGESTION = "suggestion"
}

export interface Thread {
    userId: string;
    isResolved: boolean;
    type: CommentEnum,
    comments: Comment[];
    id: string;
}



export interface Comment {
    id: string;
    workspaceId: string;
    parentId: string;
    userId: string;
    type: CommentEnum;
    content: object[]
    databaseId: string;
    recordId: string;
    commentedText: string;
    documentId: string;
    isResolved: boolean
    resolvedById: string;
    resolvedAt: Date;
    reactions: Reaction[];
    updatedById: string;
    meta: object;
    createdAt: Date
    updatedAt: Date
    deletedAt: Date
}

export interface CommentReply {
    id: string;
    text: string;
    author: string;
    timestamp: Date
}

export type SuggestionInterface = {
    id: string;
    type: 'addition' | 'deletion' | 'replacement';
    position: number; // Start index in block content
    length?: number; // Length of affected content (for deletion/replacement)
    suggestedContent: object[]; // Rich text content for additions/replacements
    originalOriginal: object[]; // Original rich text content for deletions/replacements
    authorId: string;
    createdById: string;
    updatedById: string | null;
    workspaceId: string;
    documentId: string | null;
    recordId: string | null;
    databaseId: string | null;
    isResolved: boolean;
    resolvedById: string | null;
    resolvedAt: string | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    meta: { reaction?: { emoji: string; userId: string; createdAt: string }[] } | null;
    room: string;
};