import React, {useState} from "react";
import {Button} from "@/components/ui/button";
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover"
import {cn} from "@/lib/utils";
import "./emojiPicker.css"
import EP from 'emoji-picker-react';
// import dynamic from 'next/dynamic';

// const EP = dynamic(
//     () => {
//         return import('emoji-picker-react');
//     },
//     {ssr: false}
// );

export interface EmojiPickerProps {
    emoji: string;
    onChange: (emoji: string) => void;
    triggerClassName?: string;
    trigger?: React.ReactNode;
}

export const EmojiPicker = (props: EmojiPickerProps) => {
    const [open, setOpen] = useState(false)
    return (<>
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                {props.trigger ? props.trigger :
                    <Button variant='outline' className={cn('size-9 rounded-full text-base', props.triggerClassName)}>
                        props.emoji.trim()
                    </Button>
                }
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0 border-none">
                <div className="emoji-picker">
                    <EP onEmojiClick={(e) => {
                        props.onChange(e.emoji)
                        setOpen(false)
                    }}
                        lazyLoadEmojis
                        previewConfig={{showPreview: false}}
                        className='!shadow-none !rounded-none'
                        width="100%"
                        height="400px"
                    />
                </div>
            </PopoverContent>
        </Popover>

    </>)
}