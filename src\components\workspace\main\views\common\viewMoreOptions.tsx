import {DropdownMenu, DropdownMenuContent, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {Button} from "@/components/ui/button";
import {EllipsisHorizontalIcon} from "@heroicons/react/24/outline";
import React, {useEffect, useState} from "react";
import {Switch} from "@/components/ui/switch";
import {Label} from "@/components/ui/label";
import {View} from "@/typings/page";
import {DashboardViewDefinition, DocumentViewDefinition, FormViewDefinition, TableViewDefinition, ViewColumnCustomization, ViewType} from "opendb-app-db-utils/lib/typings/view";
import {ViewFilter} from "@/components/workspace/main/views/common/viewFilter";
import { ArrowDownToArcIcon, ArrowUpWideShortIcon, FilterListIcon, LockIcon, RectangleHistoryIcon, MessageLinesIcon } from "@/components/icons/FontAwesomeRegular";
import {ViewSort} from "@/components/workspace/main/views/common/viewSort";
import {Database} from "@/typings/database";
import {DatabaseFieldDataType, DbRecordFilter, DbRecordSort, FileItem, Match} from "opendb-app-db-utils/lib/typings/db";
import {useViews} from "@/providers/views";
import {DatabaseRecordStore} from "@/typings/utilities";
import {MyWorkspaceMember} from "@/typings/workspace";
import {DataViewRow, filterAndSortRecords, searchFilteredRecords} from "@/components/workspace/main/views/table";
import {apiUrl} from "@/api/common";
import {recordValueToText} from "opendb-app-db-utils/lib/utils/db";
import crypto from "crypto";
import Papa from 'papaparse';
import {useWorkspace} from "@/providers/workspace";
import {useAlert} from "@/providers/alert";
import {DocumentClientList} from "@/components/workspace/main/views/document/documentClientList";
import {DocConfig} from "@/components/workspace/main/views/document/documentContent";
import {useSearchParams} from "next/navigation";
import { DocumentHistoryModal } from "@/components/workspace/main/common/documentHistory";
import { RiMessageLine } from "react-icons/ri";

interface ViewMoreOptionsProps {
    view: View
    onDefinitionUpdate: (update: Partial<TableViewDefinition | FormViewDefinition>) => void
    disabled?: boolean
    database: Database
    currentRecordId?: string
    currentRecordDatabaseId?: string
}

export const ViewMoreOptions = (props: ViewMoreOptionsProps & {
    selectedIds: string[]
    search: string
    filter: DbRecordFilter
    sorts: DbRecordSort[]
}) => {
    const {view, disabled, database, onDefinitionUpdate, currentRecordId, currentRecordDatabaseId} = props

    const {toast} = useAlert()
    const {databaseStore, members, workspace} = useWorkspace()
    const definition = view.definition as TableViewDefinition

    definition.filter = definition.filter || {conditions: [], match: Match.All}
    definition.sorts = definition.sorts || []

    const viewType = view.type

    const {selectedIds, filter, sorts, search} = props

    const [isExporting, setIsExporting] = useState(false)

    const exportCSV = async () => {
        const {columnPropsMap, columnsOrder} = definition
        await exportToCSV({
            sort: definition.sorts || sorts || [],
            databaseStore,
            fileNameNoExtension: view.name,
            members,
            userId: workspace.workspaceMember.userId,
            databaseId: props.database.id,
            columnsOrder,
            columnPropsMap,
            search,
            selectedIds,
            filter,
            filter2: definition.filter,
            onFinish: () => toast('View exported to CSV'),
            onError: (error) => toast.error(`Error exporting CSV: ${error}`)
        })

    }

    return <>
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost"
                        disabled={disabled}
                        className="mr-2 hover:bg-neutral-300 p-1 size-6 rounded-full">
                    <EllipsisHorizontalIcon className="size-4"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 rounded-none py-2 flex flex-col gap-1" align="end">
                {viewType !== ViewType.SummaryTable && <Label
                    className="text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer">
                    <LockIcon className='size-3'/>
                    <span className="flex-1 capitalize">Lock content</span>
                    <Switch className="h-4 w-8"
                            checked={!!definition.lockContent}
                            onCheckedChange={lockContent => onDefinitionUpdate({lockContent})}
                            thumbClassName="!size-3"/>
                </Label>}
                <ViewFilter
                    database={database}
                    trigger={
                        <Button variant="ghost"
                                className="text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                            <FilterListIcon className="size-3"/>
                            Default Filters
                            {definition.filter.conditions.length > 0 && `(${definition.filter.conditions.length})`}
                        </Button>
                    }
                    filter={definition.filter}
                    onChange={filter => onDefinitionUpdate({filter})}
                    currentRecordId={currentRecordId}
                    currentRecordDatabaseId={currentRecordDatabaseId}
                />
                {(viewType === ViewType.Table || viewType === ViewType.SummaryTable || viewType === ViewType.ListView) && <>
                    <ViewSort
                        database={database}
                        sorts={definition.sorts}
                        onChange={sorts => onDefinitionUpdate({sorts})}
                        trigger={
                            <Button variant="ghost"
                                    className="text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                                <ArrowUpWideShortIcon className="size-3"/>
                                Default Sorts
                                {definition.sorts.length > 0 && `(${definition.sorts.length})`}
                            </Button>
                        }
                    />
                </>}

                {(viewType === ViewType.Table || viewType === ViewType.Board || viewType === ViewType.ListView) && <>
                    <Button variant="ghost"
                            onClick={exportCSV}
                            className="text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                        <ArrowDownToArcIcon className="size-3"/>
                        Export as CSV
                    </Button>
                </>}


                {/*<DropdownMenuSeparator/>*/}
                {/*<DropdownMenuGroup>*/}
                {/*   */}
                {/*</DropdownMenuGroup>*/}
            </DropdownMenuContent>
        </DropdownMenu>

    </>
}

export const FormViewMoreOptions = (props: ViewMoreOptionsProps) => {
    const {cache} = useViews()
    const {view, disabled, database, onDefinitionUpdate} = props
    const definition = view.definition as FormViewDefinition

    const submitProps = definition.submitProps || {}

    const isEditing = !!cache.getCache(ViewIsEditingKey, false)


    useEffect(() => {
        () => cache.clearCache(ViewIsEditingKey)
    }, []);

    return <>
        {!definition.lockContent &&
            <Button variant="outline"
                    onClick={() => {
                        if (isEditing) cache.clearCache(ViewIsEditingKey)
                        else cache.setCache(ViewIsEditingKey, true)
                    }}
                    className="text-xs p-2 px-3 h-7 w-auto rounded-full font-semibold gap-1">
                {isEditing ? 'Done Editing' : 'Edit'}
            </Button>}
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost"
                        disabled={disabled}
                        className="mr-2 hover:bg-neutral-300 p-1 size-6 rounded-full">
                    <EllipsisHorizontalIcon className="size-4"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 rounded-none py-2 flex flex-col gap-1" align="end">
                <Label
                    className="text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer">
                    <LockIcon className='size-3'/>
                    <span className="flex-1 capitalize">Lock edits</span>
                    <Switch className="h-4 w-8"
                            checked={!!definition.lockContent}
                            onCheckedChange={lockContent => {
                                onDefinitionUpdate({lockContent})
                                if (!lockContent) cache.clearCache(ViewIsEditingKey)
                            }}
                            thumbClassName="!size-3"/>
                </Label>
                <Label
                    className="text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer">
                    <LockIcon className='size-3'/>
                    <span className="flex-1 capitalize">Accept responses</span>
                    <Switch className="h-4 w-8"
                            checked={!!definition.submitProps?.acceptNewResponse}
                            onCheckedChange={acceptNewResponse => onDefinitionUpdate({submitProps: {...submitProps, acceptNewResponse}})}
                            thumbClassName="!size-3"/>
                </Label>


                {/*<DropdownMenuSeparator/>*/}
                {/*<DropdownMenuGroup>*/}
                {/*   */}
                {/*</DropdownMenuGroup>*/}
            </DropdownMenuContent>
        </DropdownMenu>

    </>
}

export const DocViewMoreOptions = (props: Omit<ViewMoreOptionsProps, 'database'>) => {
    const {cache} = useViews()
    const {workspace, membersMap} = useWorkspace()
    const {view, disabled, onDefinitionUpdate} = props
    const definition = view.definition as DocumentViewDefinition

    const searchParams = useSearchParams()

    let documentId = searchParams.get('id') || ''

    const broadcastKey = `${props.view.pageId}:${props.view.id}:${documentId}`

    return <>
        <DocumentClientList
            documentId={`${DocConfig.namespace}:${documentId}`}
            namespace={DocConfig.namespace}
            broadcastKey={broadcastKey}/>

        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost"
                        disabled={disabled}
                        className="mr-2 hover:bg-neutral-300 p-1 size-6 rounded-full">
                    <EllipsisHorizontalIcon className="size-4"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 rounded-none py-2 flex flex-col gap-1" align="end">
                <Label
                    className="text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer">
                    <LockIcon className='size-3'/>
                    <span className="flex-1 capitalize">Lock edits</span>
                    <Switch className="h-4 w-8"
                            checked={!!definition.lockContent}
                            onCheckedChange={lockContent => {
                                onDefinitionUpdate({lockContent})
                                if (!lockContent) cache.clearCache(ViewIsEditingKey)
                            }}
                            thumbClassName="!size-3"/>
                </Label>


                {!!documentId && <DocumentHistoryModal
                    documentId={documentId}
                    membersMap={membersMap}
                    workspace={workspace.workspace}>
                    <Button variant="ghost" className="text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                        <RectangleHistoryIcon className="size-3"/>
                        Version History
                    </Button>
                </DocumentHistoryModal>}


                {/*<DropdownMenuSeparator/>*/}
                {/*<DropdownMenuGroup>*/}
                {/*   */}
                {/*</DropdownMenuGroup>*/}
            </DropdownMenuContent>
        </DropdownMenu>

    </>
}

export const ViewIsEditingKey = "viewIsEditing"

export const DashboardViewMoreOptions = (props: Omit<ViewMoreOptionsProps, 'database'>) => {
    const {cache} = useViews()
    const {view, disabled, onDefinitionUpdate} = props
    const definition = view.definition as DashboardViewDefinition

    const isEditing = !!cache.getCache(ViewIsEditingKey, false)


    useEffect(() => {
        () => cache.clearCache(ViewIsEditingKey)
    }, [cache]);

    return <>
        {!definition.lockContent &&
            <Button variant="outline"
                    onClick={() => {
                        if (isEditing) cache.clearCache(ViewIsEditingKey)
                        else cache.setCache(ViewIsEditingKey, true)
                    }}
                    className="text-xs p-2 px-3 h-7 w-auto rounded-full font-semibold gap-1">
                {isEditing ? 'Done Editing' : 'Edit'}
            </Button>}
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost"
                        disabled={disabled}
                        className="mr-2 hover:bg-neutral-300 p-1 size-6 rounded-full">
                    <EllipsisHorizontalIcon className="size-4"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 rounded-none py-2 flex flex-col gap-1" align="end">
                <Label
                    className="text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer">
                    <LockIcon className='size-3'/>
                    <span className="flex-1 capitalize">Lock edits</span>
                    <Switch className="h-4 w-8"
                            checked={!!definition.lockContent}
                            onCheckedChange={lockContent => {
                                onDefinitionUpdate({lockContent})
                                if (!lockContent) cache.clearCache(ViewIsEditingKey)
                            }}
                            thumbClassName="!size-3"/>
                </Label>
            </DropdownMenuContent>
        </DropdownMenu>

    </>
}

export const exportToCSV = async (opts: {
    databaseId: string,
    databaseStore: DatabaseRecordStore,
    members: MyWorkspaceMember[],
    columnsOrder?: string[]
    columnPropsMap?: {
        [key: string]: ViewColumnCustomization
    }
    search: string,
    filter: DbRecordFilter,
    filter2: DbRecordFilter,
    sort: DbRecordSort[],
    selectedIds: string[],
    onFinish?: () => void
    onError?: (error: string) => void,
    userId: string,
    fileNameNoExtension: string
}) => {
    let {
        databaseId, databaseStore,
        members, search,
        filter, filter2,
        selectedIds,
        onFinish, onError,
        sort, columnPropsMap,
        columnsOrder, userId, fileNameNoExtension
    } = opts

    filter = filter || {conditions: [], match: Match.All}
    filter2 = filter2 || {conditions: [], match: Match.All}
    columnsOrder = columnsOrder || []
    sort = sort || []

    const databaseStoreItem = databaseStore[databaseId]

    if (!databaseStoreItem) {
        onError?.("Database not found");
        return
    }
    const colDef = getColDefs(databaseStoreItem.database, columnsOrder, columnPropsMap)

    const fromSelection = (selectedIds && Array.isArray(selectedIds) && selectedIds.length > 0)
    let filteredRows: DataViewRow[] = (fromSelection ? filterAndSortRecords(
        databaseStoreItem,
        members,
        databaseStore,
        {conditions: [], match: Match.All},
        {conditions: [], match: Match.All},
        [],
        userId
    ) : filterAndSortRecords(
        databaseStoreItem,
        members,
        databaseStore,
        filter,
        filter2,
        sort,
        userId
    )).rows
    const rows = searchFilteredRecords(search, filteredRows)

    const dataViewRowMap: { [id: string]: DataViewRow } = {}

    for (let filteredRow of filteredRows) {
        dataViewRowMap[filteredRow.id] = filteredRow
    }

    if (!selectedIds || !Array.isArray(selectedIds) || selectedIds.length === 0) {
        selectedIds = rows.map(r => r.id)
    }
    const csvRows = buildCSVRows(databaseStoreItem.database, colDef.columns, dataViewRowMap, selectedIds)
    let headers = colDef.columns.map(c => c.title);

    const csv = Papa.unparse([headers, ...csvRows]);

    console.log({
        csvRows,
        headers,
        filteredRows
    })

    const blob = new Blob([csv], {type: "text/csv"});
    const downloadLink = document.createElement("a");
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = `${fileNameNoExtension}.csv`;

    downloadLink.click();

    onFinish?.()
}

interface ColDef {
    id: string,
    title: string,
    type: DatabaseFieldDataType
}

const getColDefs = (
    database: Database,
    columnsOrder: string[],
    columnPropsMap?: {
        [key: string]: ViewColumnCustomization
    }): {
    columns: ColDef[]
} => {
    const columns: ColDef[] = []

    const dbDefinition = database.definition
    if (!dbDefinition) return {columns}

    columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : []
    columnPropsMap = columnPropsMap || {}

    for (const key of dbDefinition.columnIds) {
        if (!columnsOrder.includes(key)) columnsOrder.push(key)
        if (!columnPropsMap[key]) columnPropsMap[key] = {}
    }
    for (const colId of columnsOrder) {
        const dbCol = dbDefinition.columnsMap[colId]
        if (!dbCol) continue
        if (columnPropsMap[colId].isHidden) continue

        const {id, title, type} = dbCol

        const column: ColDef = {
            id,
            title,
            type
        }
        columns.push(column)
    }
    return {columns}
}

const attachmentTempUrl = (workspaceId: string, file: FileItem) => {
    const connectTs = new Date().getTime()
    const hash = crypto.createHash('sha256').update(`${workspaceId}|${file.id}|${connectTs}`).digest('hex')
    return apiUrl() + `/attachments/${workspaceId}/${file.id}/${connectTs}/${encodeURIComponent(hash)}`
}

const buildCSVRows = (
    database: Database,
    columns: ColDef[],
    dataViewRowMap: { [id: string]: DataViewRow },
    rowIds: string[]
) => {
    const result: string[][] = []

    for (let id of rowIds) {
        const row = dataViewRowMap[id]
        if (!row) continue

        const rowData: string[] = []
        for (let column of columns) {
            rowData.push(getCellText(database, column, row))
        }
        result.push(rowData)
    }
    return result
}

const getCellText = (database: Database, column: ColDef, row: DataViewRow) => {
    let text = ''

    const rawCellValue = row.record.recordValues[column.id]
    const processedCellValue = row.processedRecord.processedRecordValues[column.id]

    if (column.type === DatabaseFieldDataType.Files) {
        const files: FileItem[] = rawCellValue
                                  && Array.isArray(rawCellValue)
                                  && rawCellValue.length > 0
                                  && typeof (rawCellValue[0]) === 'object'
                                  && rawCellValue[0].link
                                  && rawCellValue[0].name ? rawCellValue as FileItem[] : []

        text = files.map(f => `${f.name}(${attachmentTempUrl(database.workspaceId, f)})`).join(',')
    } else {
        text = String(recordValueToText(processedCellValue))
    }

    return text
}
