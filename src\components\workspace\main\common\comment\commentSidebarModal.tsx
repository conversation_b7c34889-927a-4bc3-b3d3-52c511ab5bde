"use client"

import { useState, useRef, useEffect, ReactNode } from "react"
import { <PERSON>bar, SidebarContent, SidebarHeader, SidebarProvider } from "@/components/ui/sidebar"
import { CommentSidebar } from "./commentSidebar"
import { MessageSquare, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { IconType } from "react-icons"
import { ScrollArea } from "@/components/ui/scroll-area"

interface CommentSidebarProps {
  databaseId?: string
  documentId?: string
  recordId?: string
  pageId?: string
  // Allow parent to control the modal state
  open?: boolean
  onOpenChange?: (open: boolean) => void
  // Simple trigger customization
  trigger: ReactNode
  triggerClassName?: string
  icon?: IconType
  className?: string;
  // Container awareness
  containerSelector?: string // CSS selector for the container to respect
  offsetRight?: number // Additional right offset
  offsetTop?: number // Additional top offset
}

const CommentSidebarModal = (props: CommentSidebarProps) => {
  const [internalOpen, setInternalOpen] = useState(false)
  const [sidebarStyle, setSidebarStyle] = useState<React.CSSProperties>({})
  const triggerRef = useRef<HTMLButtonElement>(null)

  // Use controlled state if provided by parent, otherwise use internal state
  const isControlled = props.open !== undefined
  const open = isControlled ? props.open! : internalOpen
  const setOpen = isControlled ? props.onOpenChange! : setInternalOpen

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen && triggerRef.current) {
      calculateSidebarPosition()
    }
    setOpen(newOpen)
  }

  const calculateSidebarPosition = () => {
    if (!triggerRef.current) return

    const triggerRect = triggerRef.current.getBoundingClientRect()
    const container = props.containerSelector
      ? document.querySelector(props.containerSelector)
      : triggerRef.current.closest('[role="dialog"]') || triggerRef.current.closest('.dialog-content') || document.body

    if (container) {
      const containerRect = container.getBoundingClientRect()

      // Position sidebar to the right of the container, not overlapping
      const sidebarWidth = 280 // 80 * 4 (w-80)
      const rightOffset = props.offsetRight || 8
      const topOffset = props.offsetTop || 0

      const left = containerRect.right + rightOffset
      const top = containerRect.top + topOffset
      const maxHeight = window.innerHeight - top - 20 // 20px bottom margin

      const shouldPositionLeft = left + sidebarWidth > window.innerWidth

      setSidebarStyle({
        // position: 'fixed',
        // left: shouldPositionLeft ? containerRect.left - sidebarWidth - rightOffset : left,
        // top: top,
        // maxHeight: `${maxHeight}px`,
        zIndex: 60, // Higher than dialog z-index
        width: '280px'
      })
    }
  }

  // Recalculate position on window resize
  useEffect(() => {
    if (open) {
      const handleResize = () => calculateSidebarPosition()
      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [open])

  return (
    <>
      <Button
        ref={triggerRef}
        variant={"ghost"}
        onClick={() => handleOpenChange(!open)}
        className={`flex items-center gap-2 ${props.triggerClassName || ""}`}
        title={"Comments"}
        asChild={props.trigger !== undefined}
      >
        {props.trigger || <MessageSquare className="h-4 w-4" />}
        {/* {props.triggerSize !== "icon" && (props.triggerText || "Comments")} */}
      </Button>

      {open && (
        <div

          className={cn("absolute  z-50 right-0 ", props.className)}
          onClick={(e) => {
            // Close if clicking outside the sidebar
            if (e.target === e.currentTarget) {
              handleOpenChange(false)
            }
          }}
        >
          <div
            className="bg-white border border-gray-200 shadow-sm rounded-lg "
            style={sidebarStyle}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}


            {/* Content */}
            <div className="flex-1 min-h-0 ">
              <ScrollArea className="h-full">
                <div className="p-2">
                  <CommentSidebar 
                    databaseId={props.databaseId}
                    documentId={props.documentId}
                    recordId={props.recordId}
                  />
                </div>
              </ScrollArea>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export const SidebarContainer = ({ databaseId, documentId, recordId, isOpen, onToggle, className }: {
  databaseId?: string
  documentId?: string
  recordId?: string
  pageId?: string,
  isOpen: boolean,
  onToggle: () => void,
  className: string
}) => {
  // Use internal state if no external control is provided
  const [internalOpen, setInternalOpen] = useState(false);
  const isSidebarOpen = isOpen !== undefined ? isOpen : internalOpen;
  const toggleSidebar = onToggle || (() => setInternalOpen(!internalOpen));

  return (


    <div
      className={cn(`
          absolute top-0 right-0 h-full w-80 bg-white border-l border-gray-200 shadow-lg z-10
          transform transition-transform duration-300 ease-in-out
          ${isSidebarOpen ? 'translate-x-0' : 'translate-x-full'}
        `, className)}
    >
      <CommentSidebar
        databaseId={databaseId}
        documentId={documentId}
        recordId={recordId}
      />
    </div>
  );
};
export default CommentSidebarModal