"use client"

import { useState, useRef, useEffect, ReactNode } from "react"
import { Sidebar, SidebarContent, SidebarHeader, SidebarProvider } from "@/components/ui/sidebar"
import { CommentSidebar } from "./commentSidebar"
import { MessageSquare, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { IconType } from "react-icons"
import { ScrollArea } from "@/components/ui/scroll-area"





export const SidebarContainer = ({ roomName, isOpen, onToggle, className, sidebarClassName }: {
  isOpen: boolean,
  onToggle?: () => void,
  className: string
  sidebarClassName: string,
  roomName: string
}) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const isSidebarOpen = isOpen !== undefined ? isOpen : internalOpen;
  const toggleSidebar = onToggle || (() => setInternalOpen(!internalOpen));

  let databaseId
  let documentId
  let recordId
  if (roomName.startsWith('rs:')) {
    // Expected format: `rs:${databaseId}|${id}`
    const name = roomName.replace("rs:", '')
    const [dbId, rId] = name.split("|")
    databaseId = dbId;
    recordId = rId;

  } else if (roomName.startsWith('d:')) {
    // Expected format: `d:${props.view.pageId}|${props.view.id}|${props.activeId}`
    const name = roomName.replace("d:", '')
    const [pageId, viewId, id] = name.split("|")

    documentId = id;
  } else if (roomName.startsWith('n:')) {
    // Expected format: `n:${documentId}|${workspaceId}`
    const name = roomName.replace("n:", '')
    const [id, workspaceId] = name.split("|")
    documentId = id;
  }
  return (


    <div
      className={cn(`
          h-full w-80 bg-white border-l border-gray-200 shadow-lg z-10
          transform transition-transform duration-300 ease-in-out
          ${isSidebarOpen ? 'translate-x-0' : 'translate-x-full'}
        `, className)}
    >
      <CommentSidebar
        databaseId={databaseId}
        documentId={documentId}
        recordId={recordId}
        className={sidebarClassName}
      />
    </div>
  );
};
