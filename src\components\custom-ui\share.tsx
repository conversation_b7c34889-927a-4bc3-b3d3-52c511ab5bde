import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { useState } from "react";

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { AccessLevel } from '@/typings/page';
import { AccessLevelSelect, VisibilitySelect } from "../workspace/main/pages/pagePermission";
import { BuildingsIcon, CheckIcon, LinkIcon, LockIcon } from "@/components/icons/FontAwesomeRegular";
import { copyToClipboard } from "@/utils/clipboard";
import { toast } from "sonner";



interface ShareWrapperProps {
    onShare?: (emails: string, accessLevel: AccessLevel) => Promise<void>;
    onCancel?: () => void;
    showFooter?: boolean,
    children?: React.ReactNode;
    trigger: React.ReactNode;
}
const ShareWrapper: React.FC<ShareWrapperProps> = ({ onShare, trigger, showFooter = false, children }) => {
    const [emails, setEmails] = useState("")
    const [newAccessLevel, setNewAccessLevel] = useState(AccessLevel.Full)

    const handleSubmit = () => {
        if (onShare) {
            // const emailList = emails.split(",").map(email => email.trim()).filter(email => email);
            onShare(emails, newAccessLevel);
            setEmails("");
        }
    }

    const copyLink = async () => {
        const url = window.location.href;
        copyToClipboard(url);
        toast.success("Link copied to clipboard");
    }
    return (
        <Popover>
            <PopoverTrigger asChild>
                {trigger}
            </PopoverTrigger>
            <PopoverContent className="max-w-full w-[450px] p-0 rounded-none" align="end">
                <div className="p-3">
                    <div className="flex gap-2">
                        <Input placeholder="Enter emails separate by comma"
                            value={emails}
                            onChange={e => setEmails(e.target.value)}
                            className="w-full rounded-none h-8 text-xs " />

                        <AccessLevelSelect
                            accessLevel={newAccessLevel}
                            onChange={setNewAccessLevel}
                        // disabled={shareFor == 'note'}
                        />
                        <Button
                            // disabled={isSending}
                            onClick={handleSubmit}
                            className="text-xs p-2 px-4 h-8 w-auto rounded-full font-semibold gap-1">
                            Invite
                        </Button>
                    </div>
                </div>
                {children}

                {showFooter && <div className="p-3 border-t flex">
                    {/* <VisibilitySelect */}
                    {/* //   visibility={visibility}  */}
                    {/* //   onChange={(visibility) => Props.onUpdate({visibility})} */}
                    {/* /> */}
                    <div className="flex-1"></div>
                    <Button
                        onClick={copyLink}
                        variant="ghost"
                        className="text-xs p-2 px-4 h-8 w-auto rounded-full font-semibold gap-1">
                        <LinkIcon className="size-3" />
                        Copy link
                    </Button>
                </div>
                }
            </PopoverContent>
        </Popover>
    )
}

export default ShareWrapper