import {<PERSON><PERSON>} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import React, {useState} from "react";

import {Popover, PopoverContent, PopoverTrigger} from "@/components/ui/popover"
import {DropdownMenu, DropdownMenuContent, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {ChevronDownIcon} from "@heroicons/react/24/outline";
import {BuildingsIcon, CheckIcon, LinkIcon, LockIcon} from "@/components/icons/FontAwesomeRegular";
import {AccessLevel, Page, Visibility} from "@/typings/page";
import {useWorkspace} from "@/providers/workspace";
import {cn} from "@/lib/utils";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import {Badge} from "@/components/ui/badge";
import {useWorkspaceSocket} from "@/providers/workspaceSocket";
import {defaultAPIMessage} from "@/api/common";
import {Socket<PERSON>allbackData, WorkspaceHandlerEvent} from "@/typings/socket";
import {useAlert} from "@/providers/alert";
import {usePage} from "@/providers/page";
import {WorkspaceMemberRole} from "@/typings/workspace";
import {validateEmail} from "@/utils/validate";
import {useAuth} from "@/providers/user";
import {inviteToPage, revokePagePermission, updatePagePermission} from "@/api/workspace";
import {arrayDeDuplicate} from "opendb-app-db-utils/lib";
import {getPageUrl} from "@/api/page";
import {copyToClipboard} from "@/utils/clipboard";
import ShareWrapper from "@/components/custom-ui/share";

export interface ShareProps {
    shareFor?: "page" | "note"
    accessLevel?: AccessLevel
    permissions?: {
        userId: string,
        accessLevel: AccessLevel
    }[]

}
export const PagePermission: React.FC<ShareProps> = ({ shareFor = 'page', ...Props }) => {
// const [emails, setEmails] = useState("")
// const [newAccessLevel, setNewAccessLevel] = useState(AccessLevel.Full)
    const {socket, isConnected} = useWorkspaceSocket()
    const {toast, confirm, promptUpgrade} = useAlert()
    const {page, permissions, accessLevel} = usePage()
    const {token} = useAuth()
    const [isSending, setIsSending] = useState(false)
    const visibility = page.visibility

    const updatePage = (change: Partial<Page>) => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return
        }
        socket.emit(WorkspaceHandlerEvent.UpdatePage, {
            update: {
                id: page.id,
                name: page.name,
                icon: page.icon,
                visibility: page.visibility,
                accessLevel: page.accessLevel,
                ...change
            }
        }, (res: SocketCallbackData<{}>) => {
            console.log('UpdatePage with response', res);
        })
    }

    const sendInvitation = async (emails: string, accessLevel: AccessLevel) => {
        if (!token) return
        const rawList = emails.split(",")
        const emailList: string[] = [];

        for (let email of rawList) {
            email = email.trim()
            if (!email) continue
            if (!validateEmail(email)) {
                toast.error(`Invalid email ${email} detected, unable to proceed`)
                return
            }
            emailList.push(email)
        }
        if (emailList.length === 0) return
        setIsSending(true)
        const res = await inviteToPage(token.token, workspace.workspace.id, page.id, { emails: arrayDeDuplicate(emailList), accessLevel })
        setIsSending(false)
        if (res.error) {
            const error = res.error || defaultAPIMessage()
            if (error.includes('upgrade')) {
                promptUpgrade(error, workspace.workspace.domain)
                return
            }
            toast.error(error)
            return
        }
        // setEmails("")
        toast.success(`Invitation sent to ${emailList.length} email(s)`)
    }

    const revokePermission = async (userId: string) => {
        if (!token) return
        const member = membersMap[userId]
        if (!member) return
        const name = `${member.user.firstName} ${member.user.lastName}`.trim()

        const cb = async () => {
            const res = await revokePagePermission(token.token, workspace.workspace.id, page.id, {userIds: [userId]})
            if (res.error) {
                const error = res.error || defaultAPIMessage()
                toast.error(error)
                return
            }
        }
        let title = `Are you sure you want to remove ${name || member.user.email}'s access?`
        let message = `This will remove ${name || member.user.email}'s access, they'll no longer have access.`
        confirm(title, message, cb, undefined, undefined, true)

    }

    const updatePermission = async (userId: string, accessLevel: AccessLevel) => {
        if (!token) return
        const res = await updatePagePermission(token.token, workspace.workspace.id, page.id, {userId, accessLevel})
        if (res.error) {
            const error = res.error || defaultAPIMessage()
            toast.error(error)
            return
        }
    }

    const copyLink = () => {
        const link = getPageUrl(workspace.workspace.domain, page)
        copyToClipboard(link)
        toast.success("Link copied to clipboard")
    }

    const {workspace, membersMap} = useWorkspace()

    if (accessLevel !== AccessLevel.Full) return;
    return (    
        <ShareWrapper
            onShare={sendInvitation}
            trigger={
                <Button variant="outline"
                    className="text-xs p-2 px-3 h-7 w-auto rounded-full font-semibold gap-1">
                    Share
                </Button>
            }
        >
            {/* <Popover> */}

            {/* <PopoverContent className="max-w-full w-[450px] p-0 rounded-none" align="end">
                    <div className="p-3">
                        <div className="flex gap-2">
                            <Input placeholder="Enter emails separate by comma"
                                   value={emails}
                                   onChange={e => setEmails(e.target.value)}
                                   className="w-full rounded-none h-8 text-xs "/>

                            <AccessLevelSelect
                                accessLevel={newAccessLevel}
                                onChange={setNewAccessLevel}
                                disabled={shareFor == 'note'}
                            />
                            <Button
                                disabled={isSending}
                                onClick={sendInvitation}
                                className="text-xs p-2 px-4 h-8 w-auto rounded-full font-semibold gap-1">
                                Invite
                            </Button>
                        </div>
                    </div> */}
                    {(permissions.length > 0 || visibility === Visibility.Open) &&
                        <div className="p-3 overflow-y-auto h-auto max-h-96 border-t">
                            <div className="flex flex-col gap-2">
                                {visibility === Visibility.Open && <div className="flex gap-3 items-center select-none">
                                    <Avatar
                                        className="size-7 items-center justify-center group-hover:hidden rounded-sm">
                                        <AvatarImage src={workspace.workspace.logo}/>
                                        <AvatarFallback>{workspace.workspace.name[0]}</AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1 overflow-hidden">
                                        <div className="w-full overflow-hidden">
                                            <div className="w-full max-w-full truncate font-medium text-xs">
                                                Everyone at {workspace.workspace.name}
                                            </div>
                                        </div>
                                    </div>
                                    <AccessLevelSelect
                                        accessLevel={page.accessLevel}
                                        onChange={(accessLevel) => updatePage({accessLevel})}
                                        revocable
                                        onRevoke={() => updatePage({visibility: Visibility.Private})}
                                    />
                                </div>}

                                {permissions.filter(p => !!membersMap[p.userId]).map((p, i) => {
                                    const member = membersMap[p.userId]
                                    const name = `${member.user.firstName} ${member.user.lastName}`.trim()
                                    return (
                                        <div key={i} className="flex gap-3 items-center select-none">
                                            <Avatar className="size-7 items-center justify-center group-hover:hidden">
                                                <AvatarImage src={member.user.profilePhoto}/>
                                                <AvatarFallback>{name[0] || member.user.email[0]}</AvatarFallback>
                                            </Avatar>
                                            <div className="flex-1 overflow-hidden">
                                                <div className="w-full overflow-hidden">
                                                    <div className="w-full max-w-full truncate font-medium text-sm">
                                                        {name}
                                                        {member.user.id === workspace.workspaceMember.userId &&
                                                            <Badge variant='secondary'
                                                                   className="text-[10px] bg-neutral-200 leading-tight p-0.5 px-1 ml-1 rounded-full">You</Badge>}
                                                    </div>
                                                    <div
                                                        className="w-full max-w-full font-medium truncate text-xs text-[11px] text-muted-foreground">
                                                        {member.user.email}
                                                        {member.workspaceMember.role === WorkspaceMemberRole.Collaborator && <> • <span
                                                            className='capitalize'>Guest</span></>}
                                                    </div>
                                                </div>
                                            </div>

                                            <AccessLevelSelect
                                                accessLevel={p.accessLevel}
                                                revocable
                                                onChange={(accessLevel) => updatePermission(member.workspaceMember.userId, accessLevel)}
                                                onRevoke={() => revokePermission(member.workspaceMember.userId)}
                                            />
                                        </div>
                                    )
                                })}
                            </div>

                        </div>}
            <div className="p-3 border-t flex">
                <VisibilitySelect
                    visibility={visibility}
                    onChange={(visibility) => updatePage({ visibility })}
                />
                <div className="flex-1"></div>
                <Button
                    onClick={copyLink}
                    variant="ghost"
                    className="text-xs p-2 px-4 h-8 w-auto rounded-full font-semibold gap-1">
                    <LinkIcon className="size-3" />
                    Copy link
                </Button>
            </div>

            {/* </PopoverContent> */}
            {/* </Popover> */}
        </ShareWrapper>
    )
}

interface AccessLevelSelectProp {
    accessLevel: AccessLevel
    onChange: (accessLevel: AccessLevel) => void
    onRevoke?: () => void
    levels?: AccessLevel[]
    disabled?: boolean
    revocable?: boolean
}

export const AccessLevelSelect = (props: AccessLevelSelectProp) => {
    const levels = props.levels || [AccessLevel.Full, AccessLevel.Edit, AccessLevel.View]
    const [open, setOpen] = useState(false)
    return (
        <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
                <Button variant="outline"
                        disabled={props.disabled}
                        className="text-xs rounded-full p-1.5 !px-2 gap-1 w-24 h-auto overflow-hidden">
                    <div className="truncate flex-1">
                        <span className="font-medium capitalize">{props.accessLevel}</span>
                    </div>
                    <ChevronDownIcon className="ml-auto size-4 shrink-0"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-36 p-0 rounded-none" align="end">
                <div className="flex flex-col h-auto">
                    <div className="p-1 border-b flex-1">
                        {levels.map(r => {
                            const isSelected = r === props.accessLevel
                            return (<Button variant="ghost"
                                            key={r}
                                            onClick={() => {
                                                if (r !== props.accessLevel) props.onChange(r)
                                                setOpen(false)
                                            }}
                                            className={`text-xs rounded-none p-2 h-auto gap-0.5 text-left w-full justify-start overflow-hidden`}>
                                <div
                                    className={`truncate w-full capitalize ${isSelected ? 'font-semibold' : 'font-medium'}`}>
                                    {r}
                                </div>
                                {isSelected && <CheckIcon className="size-3"/>}
                            </Button>)
                        })}
                        {props.revocable && <Button variant="ghost"
                                                    onClick={() => {
                                                        setOpen(false)
                                                        props.onRevoke?.()
                                                    }}
                                                    className={`text-xs rounded-none p-2 h-auto gap-0.5 text-left w-full justify-start overflow-hidden`}>
                            <div className={`truncate w-full capitalize font-medium`}>
                                None
                            </div>
                        </Button>}
                    </div>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

interface VisibilitySelectProps {
    visibility: Visibility
    onChange: (visibility: Visibility) => void
    disabled?: boolean
}

export const VisibilitySelect = (props: VisibilitySelectProps) => {
    const [open, setOpen] = useState(false)
    return (
        <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
                <Button variant="outline"
                        disabled={props.disabled}
                        className="text-xs rounded-full p-1.5 !px-3 gap-1 h-auto overflow-hidden capitalize">
                    <VisibilityIcon visibility={props.visibility} className="size-3"/>
                    <div className="truncate flex-1">
                        <span className="font-medium">{props.visibility}</span>
                    </div>
                    <ChevronDownIcon className="ml-auto size-4 shrink-0"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-72 p-0 rounded-none" align="start">
                <div className="flex flex-col h-auto">
                    <div className="p-1 border-b flex-1">
                        {[Visibility.Open, Visibility.Private].map(r => {
                            const isSelected = r === props.visibility
                            return (<Button variant="ghost"
                                            key={r}
                                            onClick={() => {
                                                if (r !== props.visibility) props.onChange(r)
                                                setOpen(false)
                                            }}
                                            className={`text-xs rounded-none p-3 pt-2 h-auto gap-3 text-left w-full items-center overflow-hidden`}>

                                {r === Visibility.Open && <>
                                    <BuildingsIcon className="size-4"/>
                                    <div
                                        className={`truncate flex-1 overflow-hidden flex flex-col gap-0 font-semibold`}>
                                        <div className='capitalize'>{r}</div>
                                        <div className="text-muted-foreground text-[11px] font-medium">
                                            All workspace members can access it
                                        </div>
                                    </div>
                                </>}
                                {r === Visibility.Private && <>
                                    <LockIcon className="size-4"/>
                                    <div
                                        className={`truncate flex-1 overflow-hidden flex flex-col gap-0 font-semibold`}>
                                        <div className='capitalize'>{r}</div>
                                        <div className="text-muted-foreground text-[11px] font-medium">
                                            Only you and people invited can access it
                                        </div>
                                    </div>
                                </>}
                                {isSelected && <CheckIcon className="size-3"/>}
                            </Button>)
                        })}
                    </div>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

export const VisibilityIcon = (props: {
    visibility: Visibility,
    className: string
}) => {
    return (<>
        {props.visibility === Visibility.Private ? <>
                <LockIcon className={cn("size-4", props.className)}/>
            </> :
            props.visibility === Visibility.Open ? <>
                <BuildingsIcon className={cn("size-4", props.className)}/>
            </> : null}

    </>)
}
