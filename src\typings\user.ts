export interface User {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    profilePhoto: string;
    isEmailVerified: boolean;
    isSetupCompleted: boolean;
    isSupportAccount: boolean;
    activeWorkspaceId: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string;
}

export interface Token {
    name: string
    token: string;
    type: string;
    createdAt: string;
    expiresAt: string;
    id: number;
    clientName?: string;
    lastActiveAt?: string;
    meta?: { client?: object } | object
}

export interface NotificationChannelSetting {
    isActive: boolean
    notifyOn: {
        comment: boolean
        replies: boolean,
        assignedRecord: boolean
        dueTask: boolean
    }
}

export enum SettingsType {
    Notification = "notification",
}

export interface NotificationSettings {
    email: NotificationChannelSetting

    [key: string]: NotificationChannelSetting
}

export interface Settings {
    id: number;
    userId: string
    notification: NotificationSettings
    createdAt: string;
    updatedAt: string;

}

export interface UpdateSettingsData {
    type: SettingsType
    settings: NotificationSettings
}