.editorWrapper {
  padding:0.5px;
  /* border: 1px solid #ccc; */
}

.editorWrapper :global(.bn-editor)  {
  /* background-color: #f8fafc;*/
  border-radius: 4px; /* Optional: for rounded corners */
  /* color: #9b9a97; */
  height: 100%;
  max-width: 700px;
  overflow: auto;
  width: 100%;
  padding-inline: 1px !important;
  overflow: hidden;
}

.editorWrapper :global(.bn-block-outer)  {
 margin-bottom:0 !important;
 /* line-height: 0.5; */
}

.bn-is-empty .bn-inline-content:before, .bn-is-filter .bn-inline-content:before {
    @apply text-sm font-normal;
    font-style: normal;
}

.bn-block-group{

}