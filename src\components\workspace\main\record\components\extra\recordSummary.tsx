import {useRecord} from "@/providers/record";
import {usePage} from "@/providers/page";
import {AccessLevel} from "@/typings/page";
import {YJSDoc} from "@/components/workspace/main/common/YJSDoc";
import React, {useState} from "react";
import {ViewWarning, WarningMessage} from "@/components/workspace/main/views/common/contentLocked";
import {ScrollArea} from "@/components/ui/scroll-area";
import {Button} from "@/components/ui/button";
import { RectangleHistoryIcon, MessageLinesIcon } from "@/components/icons/FontAwesomeRegular";
import {DocumentHistoryModal} from "@/components/workspace/main/common/documentHistory";
import {useWorkspace} from "@/providers/workspace";
import { CommentProvider } from "@/providers/comment";
import CommentSidebarModal from "../../../common/comment/commentSidebarModal";
import { RiMessageLine } from "react-icons/ri";
import { FaRegMessage } from "react-icons/fa6";

export const RecordSummary = () => {
    const {accessLevel} = usePage()
    const {recordInfo} = useRecord()
    const {membersMap, workspace} = useWorkspace()
    const {id, databaseId, summaryJSON} = recordInfo.record
    const roomName = `rs:${databaseId}|${id}`
    const [isConnected, setConnected] = useState(false)

    const initialContent = Array.isArray(summaryJSON) ? summaryJSON as any : []
    const readonly = !accessLevel || ![AccessLevel.Full, AccessLevel.Edit].includes(accessLevel)

    return <div className='size-full flex flex-col overflow-hidden relative'>
        {!isConnected && <ViewWarning message={WarningMessage.ConnectionLost}/>}

        <DocumentHistoryModal
            recordId={id}
            documentId={''}
            membersMap={membersMap}
            workspace={workspace.workspace}>
            <Button variant="ghost" title='Version History' className='size-6 p-1.5 rounded-full mr-2 absolute top-4 right-4 z-50'>
                <RectangleHistoryIcon className='size-full'/>
            </Button>
        </DocumentHistoryModal>

        <CommentSidebarModal
            // documentId={note.document.id}
            databaseId={databaseId}
            recordId={recordInfo.record.id}
            trigger={
                <Button variant="ghost" title='Comments' className='size-6 p-1.5 rounded-full mr-2 absolute top-4 right-9 z-50'>
                    <FaRegMessage className='w-5 h-5' />
                </Button>
            }
            triggerClassName="size-6 p-1.5 rounded-full  relative -top-1"
            className="top-9 shadow-none"

        />
        <ScrollArea className='scrollBlockChild'>
            <div className='pb-10'>

                <YJSDoc
                    documentId={'record:' + recordInfo.record.id}
                    roomName={roomName}
                    initialContent={initialContent}
                    readonly={readonly}
                    onConnectionStatusChanged={setConnected}
                    collaborationEnabled/>
            </div>
        </ScrollArea>

    </div>
}