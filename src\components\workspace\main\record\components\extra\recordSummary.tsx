import {useRecord} from "@/providers/record";
import {usePage} from "@/providers/page";
import {AccessLevel} from "@/typings/page";
import {YJSDoc} from "@/components/workspace/main/common/YJSDoc";
import React, {useState} from "react";
import {ViewWarning, WarningMessage} from "@/components/workspace/main/views/common/contentLocked";
import {ScrollArea} from "@/components/ui/scroll-area";
import {Button} from "@/components/ui/button";
import { RectangleHistoryIcon, MessageLinesIcon, MessageIcon } from "@/components/icons/FontAwesomeRegular";
import {DocumentHistoryModal} from "@/components/workspace/main/common/documentHistory";
import { useWorkspace } from "@/providers/workspace";

export const RecordSummary = () => {
    const {accessLevel} = usePage()
    const {recordInfo} = useRecord()
    const {membersMap, workspace} = useWorkspace()
    const {id, databaseId, summaryJSON} = recordInfo.record
    const roomName = `rs:${databaseId}|${id}`
    const [isConnected, setConnected] = useState(false)
    const [openSidebar, setOpenSidebar] = useState(false)

    const initialContent = Array.isArray(summaryJSON) ? summaryJSON as any : []
    const readonly = !accessLevel || ![AccessLevel.Full, AccessLevel.Edit].includes(accessLevel)

    return <div className='size-full flex flex-col overflow-hidden relative h-full'>
        {!isConnected && <ViewWarning message={WarningMessage.ConnectionLost}/>}

        <div className="flex items-center gap-1  mr-2 absolute top-[14px] right-[22px] z-50">
            <DocumentHistoryModal
            recordId={id}
            documentId={''}
            membersMap={membersMap}
            workspace={workspace.workspace}>
                <Button variant="ghost" title='Version History' className='size-6 p-1.5 rounded-full'>
                <RectangleHistoryIcon className='size-full'/>
            </Button>
        </DocumentHistoryModal>

            <Button variant="ghost"
                title='Comments'
                className='size-6 p-1.5 rounded-full mr-2 '
                onClick={(e) => {
                    e.preventDefault()
                    setOpenSidebar(!openSidebar)
                }}
            >
                <MessageIcon className='size-3' />
            </Button>
        </div>

        <ScrollArea className='scrollBlockChild h-full'>
            <div className='pb-10'>

                <YJSDoc
                    isSidebarOpen={openSidebar}
                    onToggleSidebar={() => setOpenSidebar(!openSidebar)}
                    documentId={'record:' + recordInfo.record.id}
                    roomName={roomName}
                    initialContent={initialContent}
                    readonly={readonly}
                    onConnectionStatusChanged={setConnected}
                    collaborationEnabled/>
            </div>
        </ScrollArea>

    </div>
}