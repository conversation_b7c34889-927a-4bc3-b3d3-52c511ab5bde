import React from 'react';
import { format, isSameDay } from 'date-fns';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CalendarEvent } from '@/typings/page';
import { useScreenSize } from '@/providers/screenSize';

interface CalendarSideCardProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  handleEventClick: (event: CalendarEvent) => void;
}

export const CalendarSideCard: React.FC<CalendarSideCardProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  handleEventClick
}) => {
  const { isMobile } = useScreenSize();

  const dayEvents = events
    .filter(event => isSameDay(new Date(event.start), selectedDate))
    .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());

  return (
    <div 
      data-side-card="true"
      className={cn(
        "border-l border-neutral-300 flex flex-col bg-gradient-to-b from-neutral-50 to-neutral-100",
        isMobile ? "w-full border-t" : "w-96"
      )}
    >
      <div className={cn(
        "border-b border-neutral-300 bg-gradient-to-r from-white to-neutral-50",
        "p-4 relative overflow-hidden"
      )}>
        <div className="absolute top-0 right-0 w-20 h-20 bg-neutral-100 rounded-full -translate-y-10 translate-x-10 opacity-30"></div>
        <div className="relative z-10">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-neutral-400 rounded-full"></div>
            <h2 className="font-semibold text-black text-xs">
              {format(selectedDate, 'MMM d, yyyy')}
            </h2>
          </div>
          <div className="flex items-center space-x-1">
            <svg className="w-3 h-3 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-xs text-neutral-600">
              {dayEvents.length} {dayEvents.length === 1 ? 'event' : 'events'} scheduled
            </p>
          </div>
        </div>
      </div>

      <ScrollArea className="flex-1 p-4">
        {dayEvents.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-center relative">
            <div className="absolute inset-0 bg-gradient-to-br from-neutral-100 to-transparent rounded-lg opacity-50"></div>
            <div className="relative z-10">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-white to-neutral-100 rounded-2xl flex items-center justify-center border border-neutral-200 shadow-sm">
                <svg className="w-7 h-7 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
                </svg>
              </div>
              <p className="text-xs text-neutral-500 font-medium mb-1">
                No events scheduled
              </p>
              <p className="text-xs text-neutral-400">
                Your day is completely free
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {dayEvents.map((event) => (
              <Card
                key={event.id}
                className={cn(
                  "cursor-pointer p-4 relative overflow-hidden",
                  selectedEvent === event.id
                    ? "bg-gradient-to-r from-neutral-200 to-neutral-300 text-black border-neutral-400 shadow-lg"
                    : "bg-gradient-to-r from-white to-neutral-50 hover:from-neutral-50 hover:to-neutral-100 border-neutral-200"
                )}
                onClick={() => {
                  setSelectedEvent(event.id);
                  handleEventClick(event);
                }}
              >
                <div className="absolute top-0 right-0 w-8 h-8 bg-gradient-to-br from-transparent to-neutral-200 opacity-30 rounded-bl-full"></div>
                <div className="relative z-10">
                  <div className="flex items-start justify-between mb-3">
                    <div className={cn(
                      "font-semibold text-xs leading-relaxed",
                      selectedEvent === event.id ? "text-black" : "text-black"
                    )}>
                      {event.title}
                    </div>
                    <div className={cn(
                      "w-2 h-2 rounded-full flex-shrink-0 mt-1",
                      selectedEvent === event.id ? "bg-neutral-600" : "bg-neutral-400"
                    )}></div>
                  </div>
                  <div className={cn(
                    "flex items-center space-x-2 text-xs",
                    selectedEvent === event.id ? "text-neutral-700" : "text-neutral-600"
                  )}>
                    <div className={cn(
                      "w-4 h-4 rounded-full flex items-center justify-center",
                      selectedEvent === event.id ? "bg-neutral-400" : "bg-neutral-200"
                    )}>
                      <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <span className="font-medium">
                      {format(new Date(event.start), 'h:mm a')} - {format(new Date(event.end), 'h:mm a')}
                    </span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
};
