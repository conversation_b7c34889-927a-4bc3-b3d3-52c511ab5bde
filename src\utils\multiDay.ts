import {  addDays, startOfDay, endOfDay, isSameDay, format, differenceInDays, isWithinInterval } from 'date-fns';
import { CalendarEvent } from '@/typings/page';

export interface EventSegment {
  id: string;
  originalEventId: string;
  date: Date;
  startTime: Date;
  endTime: Date;
  isFirstSegment: boolean;
  isLastSegment: boolean;
  isMiddleSegment: boolean;
  segmentIndex: number;
  totalSegments: number;
  originalEvent: CalendarEvent;
  isAllDay: boolean;
  isMultiDay: boolean;
}


export const isMultiDayEvent = (event: CalendarEvent): boolean => {
  const start = new Date(event.start);
  const end = new Date(event.end);
  return !isSameDay(start, end);
};


export const createEventSegments = (event: CalendarEvent): EventSegment[] => {
  const eventStart = new Date(event.start);
  const eventEnd = new Date(event.end);
  const isMultiDay = isMultiDayEvent(event);
  
  // For single day events, return a single segment.
  if (!isMultiDay) {
    return [{
      id: `${event.id}-${format(eventStart, 'yyyy-MM-dd')}`,
      originalEventId: event.id,
      date: startOfDay(eventStart),
      startTime: eventStart,
      endTime: eventEnd,
      isFirstSegment: true,
      isLastSegment: true,
      isMiddleSegment: false,
      segmentIndex: 0,
      totalSegments: 1,
      originalEvent: event,
      isAllDay: false,
      isMultiDay: false
    }];
  }

  const segments: EventSegment[] = [];
  const totalDays = differenceInDays(endOfDay(eventEnd), startOfDay(eventStart)) + 1;
  
  let currentDate = startOfDay(eventStart);
  const finalDate = startOfDay(eventEnd);
  let segmentIndex = 0;

  while (currentDate <= finalDate) {
    const isFirstSegment = segmentIndex === 0;
    const isLastSegment = isSameDay(currentDate, finalDate);
    const isMiddleSegment = !isFirstSegment && !isLastSegment;

    // Calculate segment start and end times.
    const segmentStart = isFirstSegment 
      ? eventStart 
      : startOfDay(currentDate);
      
    const segmentEnd = isLastSegment 
      ? eventEnd 
      : endOfDay(currentDate);

    // Determine if this segment should be treated as all-day
    const segmentDurationHours = (segmentEnd.getTime() - segmentStart.getTime()) / (1000 * 60 * 60);
    const isAllDay = segmentDurationHours >= 23 || (isMiddleSegment && segmentDurationHours >= 22);

    segments.push({
      id: `${event.id}-${format(currentDate, 'yyyy-MM-dd')}`,
      originalEventId: event.id,
      date: currentDate,
      startTime: segmentStart,
      endTime: segmentEnd,
      isFirstSegment,
      isLastSegment,
      isMiddleSegment,
      segmentIndex,
      totalSegments: totalDays,
      originalEvent: event,
      isAllDay,
      isMultiDay: true
    });

    currentDate = addDays(currentDate, 1);
    segmentIndex++;
  }

  return segments;
};


export const getSegmentsForDay = (segments: EventSegment[], date: Date): EventSegment[] => {
  return segments.filter(segment => isSameDay(segment.date, date));
};


export const getSegmentsForWeek = (
  segments: EventSegment[], 
  weekStart: Date, 
  weekEnd: Date
): EventSegment[] => {
  return segments.filter(segment => 
    isWithinInterval(segment.date, { start: weekStart, end: weekEnd })
  );
};


export const eventsToSegments = (events: CalendarEvent[]): EventSegment[] => {
  return events
    .map(event => normalizeEventTimes(event)) 
    .flatMap(event => createEventSegments(event));
};


export const groupSegmentsByDate = (segments: EventSegment[]): Map<string, EventSegment[]> => {
  const grouped = new Map<string, EventSegment[]>();
  
  segments.forEach(segment => {
    const dateKey = format(segment.date, 'yyyy-MM-dd');
    if (!grouped.has(dateKey)) {
      grouped.set(dateKey, []);
    }
    grouped.get(dateKey)!.push(segment);
  });
  
  return grouped;
};


export const getAllDaySegments = (segments: EventSegment[]): EventSegment[] => {
  return segments.filter(segment => segment.isAllDay || segment.isMultiDay);
};


export const getTimeSlotSegments = (segments: EventSegment[]): EventSegment[] => {
  return segments.filter(segment => !segment.isAllDay);
};


export const getSegmentHeight = (segment: EventSegment): number => {
  const startHour = segment.startTime.getHours();
  const startMinutes = segment.startTime.getMinutes();
  const endHour = segment.endTime.getHours();
  const endMinutes = segment.endTime.getMinutes();
  
  // Convert to minutes from start of day.
  const startMinutesFromDayStart = (startHour * 60) + startMinutes;
  const endMinutesFromDayStart = (endHour * 60) + endMinutes;
  
  // Calculate duration in minutes, but cap at end of day (23:59)
  const maxMinutesInDay = 23 * 60 + 59;
  const cappedEndMinutes = Math.min(endMinutesFromDayStart, maxMinutesInDay);
  
  const durationMinutes = cappedEndMinutes - startMinutesFromDayStart;
  
  // Return height in pixels (1 minute = 1 pixel, minimum 30px)
  return Math.max(30, durationMinutes);
};

/**
 * Gets the top offset for positioning a segment in the time grid
 */
export const getSegmentTopOffset = (segment: EventSegment): number => {
  const startHour = segment.startTime.getHours();
  const startMinutes = segment.startTime.getMinutes();
  
  // Each hour is 60px, calculate offset within the hour
  return startMinutes; // This will be added to the hour row's top position
};


export const getSegmentStylingClasses = (segment: EventSegment): {
  roundedCorners: string;
  continuationIndicator: string;
  opacity: string;
} => {
  const baseRounded = "rounded-md";
  
  if (!segment.isMultiDay) {
    return {
      roundedCorners: baseRounded,
      continuationIndicator: "",
      opacity: "opacity-100"
    };
  }

  let roundedCorners = "";
  let continuationIndicator = "";
  
  if (segment.isFirstSegment && segment.isLastSegment) {
    roundedCorners = baseRounded;
  } else if (segment.isFirstSegment) {
    roundedCorners = "rounded-l-md rounded-r-sm";
    continuationIndicator = "pr-4";
  } else if (segment.isLastSegment) {
    roundedCorners = "rounded-r-md rounded-l-sm";
    continuationIndicator = "pl-4";
  } else {
    roundedCorners = "rounded-sm";
    continuationIndicator = "pl-4 pr-4";
  }

  return {
    roundedCorners,
    continuationIndicator,
    opacity: segment.isMiddleSegment ? "opacity-90" : "opacity-100"
  };
};


export const shouldShowTimeInSegment = (segment: EventSegment, view: 'day' | 'week' | 'month'): boolean => {
  if (view === 'month') return !segment.isAllDay;
  if (segment.isAllDay) return false;
  
  // Show time for first and last segments, but not middle all-day segments
  return segment.isFirstSegment || segment.isLastSegment || !segment.isMultiDay;
};


export const getSegmentContinuationText = (segment: EventSegment): string => {
  if (!segment.isMultiDay) return "";
  
  if (segment.isFirstSegment) {
    return `Continues for ${segment.totalSegments - 1} more day${segment.totalSegments > 2 ? 's' : ''}`;
  } else if (segment.isLastSegment) {
    return `Continued from ${segment.segmentIndex} day${segment.segmentIndex > 1 ? 's' : ''} ago`;
  } else {
    return `Day ${segment.segmentIndex + 1} of ${segment.totalSegments}`;
  }
};


export const isOvernightEvent = (event: CalendarEvent): boolean => {
  const start = new Date(event.start);
  const end = new Date(event.end);
  
  // Check if event crosses midnight on the same calendar day
  const startDay = startOfDay(start);
  const endDay = startOfDay(end);
  
  return !isSameDay(startDay, endDay) && differenceInDays(endDay, startDay) === 1;
};


export const normalizeEventTimes = (event: CalendarEvent): CalendarEvent => {
  const start = new Date(event.start);
  const end = new Date(event.end);
  
  // Ensure end time is after start time
  if (end <= start) {
    const normalizedEnd = new Date(start.getTime() + (30 * 60 * 1000)); // Add 30 minutes
    return {
      ...event,
      end: normalizedEnd
    };
  }
  
  return event;
};


export const getEventTotalDuration = (event: CalendarEvent): {
  days: number;
  hours: number;
  minutes: number;
  totalMinutes: number;
} => {
  const start = new Date(event.start);
  const end = new Date(event.end);
  
  const totalMinutes = Math.max(0, (end.getTime() - start.getTime()) / (1000 * 60));
  const days = Math.floor(totalMinutes / (24 * 60));
  const remainingMinutes = totalMinutes % (24 * 60);
  const hours = Math.floor(remainingMinutes / 60);
  const minutes = remainingMinutes % 60;
  
  return { days, hours, minutes, totalMinutes };
};


export const shouldTreatAsFullDay = (event: CalendarEvent): boolean => {
  const duration = getEventTotalDuration(event);
  
  
  if (duration.days === 0 && duration.hours >= 22) {
    return true;
  }
  
  if (duration.days > 0) {
    const start = new Date(event.start);
    const end = new Date(event.end);
    
    const startHour = start.getHours();
    const endHour = end.getHours();
    
    return startHour <= 6 && endHour >= 22;
  }
  
  return false;
};