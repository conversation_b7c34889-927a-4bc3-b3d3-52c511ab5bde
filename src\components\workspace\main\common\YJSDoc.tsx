"use client";

import {useViews} from "@/providers/views";
import {useAuth} from "@/providers/user";
import {useWorkspace} from "@/providers/workspace";
import React, { useEffect, useMemo, useRef, useState } from "react";
import * as Y from "yjs";
import {SocketIOProvider} from "y-socket.io";
import { apiUrl, collabServiceHash, collabServiceUrl, defaultAPIMessage } from "@/api/common";
import crypto from "crypto";
import {BlockNoteView} from "@blocknote/mantine";
import {adjustToDark, adjustToLight, ColorEntries} from "@/utils/color";
import {jakartaSans} from "@/utils/fonts";
import {cn} from "@/lib/utils";
import "@blocknote/mantine/style.css";
import "./YJSDoc.css";
import { Block, BlockNoteEditor, BlockSchema, filterSuggestionItems, InlineContentSchema, StyleSchema } from "@blocknote/core";
import {Socket} from "socket.io-client";
import {PageLoader} from "@/components/custom-ui/loader";
import { IndexeddbPersistence } from 'y-indexeddb'
import {
    AddCommentButton, AddTiptapCommentButton, BlockTypeSelect, DefaultReactSuggestionItem, FloatingComposerController,
    FormattingToolbar, FormattingToolbarController, getDefaultReactSlashMenuItems, SuggestionMenuController,
    BasicTextStyleButton,
    ColorStyleButton,
    CreateLinkButton,

    NestBlockButton,
    TextAlignButton,
    UnnestBlockButton,
    useEditorSelectionChange,
    useBlockNoteEditor,
    createReactInlineContentSpec,
    FileCaptionButton,
    FileReplaceButton,
} from "@blocknote/react";
import { CommentButton, FloatingCommentPopup } from "./comment/commentPopup";
import { getMentionMenuItems, CommentSchema, DocumentSchema } from "./comment/schema";
import { DefaultThreadStoreAuth, YjsThreadStore } from "@blocknote/core/comments";
import { useComments } from "@/providers/comment";
import { CustomThreadStore } from "./comment/customThreadStore";
import { queryObjectToString } from "@/api/admin";
import { CustomRestjsThreadStore } from "./comment/restyJsThreadStore";
import { getRelativeSelection, ySyncPluginKey } from "y-prosemirror";

export interface CollaborativeUser {
    color: string;
    name: string,
    id: string
    image: string
    isActive?: boolean
}

export interface YJSDocProps {
    documentId: string
    roomName: string
    initialContent: object[]
    readonly?: boolean
    collaborationEnabled?: boolean
    className?: string
    editorClassName?: string
    onConnectionStatusChanged?: (connected: boolean) => void
    onClientList?: (users: CollaborativeUser[]) => void
    onEditorReady?: () => void
    onChange?: (text:string) => void
}

const slashExclude = ['Emoji', 'Audio', 'Video', 'File']

export const YJSDoc = (props: YJSDocProps) => {

    const {uploadWorkspaceFile} = useViews()
    const { user, token } = useAuth()
    // const user = possibleUser as User;
    const { workspace, members } = useWorkspace()
    const { setEditor: setCommentEditor, setYMap } = useComments()
    const workspaceId = workspace.workspace.id
    const [isEditorReady, setIsEditorReady] = useState(!props.collaborationEnabled)
    const divRef = useRef<HTMLDivElement | null>(null)

    const {roomName, readonly, editorClassName, className} = props

    const editable = !readonly


    const connectTs = Date.now()

    const yDocRef = useRef<Y.Doc>()
    const providerRef = useRef<SocketIOProvider>()


    const [editor, setEditor] = useState<BlockNoteEditor<any, any, any>>()

    const onEditorReady = () => {
        props.onEditorReady?.()
        setIsEditorReady(true)
    }

    const collaborationEnabled = props.collaborationEnabled


    useEffect(() => {

        let initialContent = props.initialContent
        if (initialContent.length === 0) {
            initialContent = [{
                type: "paragraph",
                content: "",
            }]
        }

        if (!props.collaborationEnabled) {
            const instance = BlockNoteEditor.create({
                initialContent: initialContent,
                // uploadFile: async (file) => {
                //     return new Promise((resolve, reject) => {
                //         uploadWorkspaceFile('document', 'YJSDoc', roomName, file, (res) => {
                //             if (!res || !res.isSuccess) {
                //                 reject(res.error || defaultAPIMessage())
                //                 return
                //             }
                //             const u = res.data.data.upload
                //             resolve(u.finalUrl)
                //             return
                //         })
                //     });
                // },
                animations: false,
            });

            setEditor(instance)
            return
        }

        // if (!collaborationEnabled || !user) return
        const _user: CollaborativeUser = {
            name: `${user?.firstName || ''} ${user?.lastName || ''}`.trim(),
            color: getUserColor(user?.id || '', workspaceId),
            id: user?.id || '',
            image: user?.profilePhoto || ''
        }

        const yDoc = yDocRef.current = new Y.Doc()
        setYMap(yDoc.getMap("threads"))
        const provider = providerRef.current = new SocketIOProvider(
            `${collabServiceUrl()}`,
            roomName,
            yDoc,
            {},
            {
                path: "/dcs/",
                auth: {
                    connectTs,
                    userId: workspace.workspaceMember.userId,
                    room: roomName,
                    hash: collabRoomHash(workspaceId, workspace.workspaceMember.userId, roomName, connectTs),
                    _user,
                    workspaceId
                },
                withCredentials: true,
                transports: ["websocket"],
            },
        )
        // const persistenceProvider = new IndexeddbPersistence(roomName, yDoc)
        new IndexeddbPersistence(roomName, yDoc)
        // provider.awareness.setLocalStateField('user', _user);

        const s = provider.socket as Socket


        provider.on('sync', (isSynced: boolean) => {
            console.log('Collaboration provider synced:', {isSynced, s}); // true
            if (isSynced) onEditorReady()
        })

        // provider.on('awareness-update', (awareness: any) => {
        //     console.log('Collaboration provider aware:', awareness); // true
        //
        //     console.log({provider, s})
        // })


        // client-side
        s.on("connect", () => {
            console.log('Collaboration Socket connected:', providerRef.current?.bcconnected); // true
            props.onConnectionStatusChanged?.(true)
        });

        // s.on("awareness-update", (d) => {
        //     console.log('Collaboration awareness updated:', d.toString());
        //     // props.onConnectionStatusChanged?.(true)
        //     console.log({s})
        // });

        const scrollToClient = (id: string) => {
            const divEle = divRef.current

            console.log("Scroll to client:", {id, divEle})
            if (!divEle) return

            const cursor = divEle.querySelector(`.collaboration-cursor__caret[data-user-id="${id}"]`)

            console.log("Scroll to client:", {id, cursor})
            if (!cursor) return
            cursor.scrollIntoView({behavior: "smooth"})
        }

        const processClients = (d: CollaborativeUser[]) => {
            const divEle = divRef.current
            if (!divEle) return
            // const clients = d.filter(c => c.id !== _user.id).map(c => {
            //     const cursor = divEle.querySelector(`.collaboration-cursor__caret[data-user-id="${c.id}"]`)
            //     c.isActive = !!cursor
            //     return c
            // })
            const clients = [...new Map(d.filter(c => c.id !== _user.id).map(c => [c.id, c])).values()]
            props.onClientList?.(clients)
        }

        s.on("clients-updated", (d) => {
            // console.log('Clients updated:', d.toString());
            // props.onConnectionStatusChanged?.(true)
            // console.log({s, d})
            processClients(d)
        });

        s.on("disconnect", () => {
            console.log('Collaboration Socket disconnected:', providerRef.current?.bcconnected); // false
            props.onConnectionStatusChanged?.(false)
            processClients([])
        });


        const renderCursor = (user: { color: string; name: string, id: string }) => {
            const cursor = document.createElement("span");

            cursor.classList.add("collaboration-cursor__caret");
            cursor.setAttribute('data-user-id', user.id)
            cursor.setAttribute("style", `border-color: ${user.color}`);

            const label = document.createElement("span");

            label.classList.add("collaboration-cursor__label");
            label.setAttribute("style", `background-color: ${user.color}`);

            // @ts-ignore
            label.insertBefore(document.createTextNode(user.name), null);

            const nonbreakingSpace1 = document.createTextNode("\u2060");
            const nonbreakingSpace2 = document.createTextNode("\u2060");
            // @ts-ignore
            cursor.insertBefore(nonbreakingSpace1, null);
            // @ts-ignore
            cursor.insertBefore(label, null);
            // @ts-ignore
            cursor.insertBefore(nonbreakingSpace2, null);

            return cursor;
        };



        async function resolveUsers(userIds: string[]) {
            if (!user || !userIds || userIds.length === 0) return []

            // Map MyWorkspaceMember to User type
            return members
                .filter(m => userIds.includes(m.user?.id))
                .map(m => ({
                    id: m.user?.id || '',
                    username: `${m?.user.firstName || ''} ${m?.user.lastName || ''}`.trim(),
                    color: getUserColor(m.user?.id || '', workspaceId),
                    image: m.user?.profilePhoto || '',
                    avatarUrl: m.user?.profilePhoto || ''
                }));
        }

        const threadStore = () => {
            let databaseId
            let documentId
            let recordId
            if (roomName.startsWith('rs:')) {
                // Expected format: `rs:${databaseId}|${id}`
                const name = roomName.replace("rs:", '')
                const [dbId, rId] = name.split("|")
                databaseId = dbId;
                recordId = rId;

            } else if (roomName.startsWith('d:')) {
                // Expected format: `d:${props.view.pageId}|${props.view.id}|${props.activeId}`
                const name = roomName.replace("d:", '')
                const [pageId, viewId, id] = name.split("|")

                documentId = id;
            } else if (roomName.startsWith('n:')) {
                // Expected format: `n:${documentId}|${workspaceId}`
                const name = roomName.replace("n:", '')
                const [id, workspaceId] = name.split("|")
                documentId = id;
            }
            const queryStr = queryObjectToString({ databaseId, documentId, recordId })
            return new CustomThreadStore(
                `${apiUrl()}/workspaces/${workspaceId}/comments`,
                queryStr,
                {

                    Authorization: `Bearer ${token!.token}`,

                },
                yDoc.getMap("threads"),
                new DefaultThreadStoreAuth(user!.id, editable ? "editor" : "comment"),
                user?.id!
            );
            // return new YjsThreadStore(
            //     user!.id,
            //     yDoc.getMap("threads"),
            //     new DefaultThreadStoreAuth(user!.id, editable ? "editor" : "comment"),
            // );
        }


        const instance = BlockNoteEditor.create({
            schema: DocumentSchema,
            initialContent: props.collaborationEnabled ? undefined : initialContent,
            uploadFile: async (file) => {
                return new Promise((resolve, reject) => {
                    uploadWorkspaceFile('document', 'YJSDoc', roomName, file, (res) => {
                        if (!res || !res.isSuccess) {
                            reject(res.error || defaultAPIMessage())
                            return
                        }
                        const u = res.data.data.upload
                        resolve(u.finalUrl)
                        return
                    })
                });
            },
            animations: false,
            resolveUsers,
            comments: {
                threadStore: threadStore()
            },
            collaboration: props.collaborationEnabled ? {
                // The Yjs Provider responsible for transporting updates:
                provider,
                // Where to store BlockNote data in the Y.Doc:
                // document: doc,
                fragment: yDoc.getXmlFragment("document-store"),
                // Information (name and color) for this user:
                user: _user,
                renderCursor
            } : undefined,
        });

        setEditor(instance)

        setCommentEditor(editor);


        // yDoc.on("update", (update: any, origin: any, doc: Y.Doc, transaction: any) => {
        //     console.log("YDoc Updated")
        // })
        // yDoc.on("load", (...args) => {

        //     console.log("YDoc loaded:", ...args)
        // })


        return () => {
            if (yDoc) {
                yDoc.destroy();
            }
            if (provider) {
                provider.disconnect();
                provider.destroy();
            }
        }
    }, [collaborationEnabled]);

    useEffect(() => {
        setCommentEditor(editor);
        return () => setCommentEditor(undefined);
    }, [editor, setCommentEditor]);

    useEffect(() => {
        const handleClipboardEvent = (event: ClipboardEvent) => {
            // Get the target element that triggered the event
            const targetElement = event.target as HTMLElement;
            const documentElement = document.getElementById(props.documentId);

            // Check if the target is a child of the specified div with the correct ID and class
            if (documentElement && documentElement.contains(targetElement) && documentElement.classList.contains('yDoc')) {
                const selectedText = window.getSelection()?.toString();

                // console.log(`On ${event.type}:`, { event });

                // If there is selected text (for copy or cut operations)
                if (selectedText) {
                    event.preventDefault(); // Prevent the default clipboard behavior (copy, cut, paste)
                    event.clipboardData?.setData('text/plain', selectedText); // Set the selected text to clipboard
                }
            }
        };

        // Add listeners for `copy`, `cut`, and `paste` events
        document.addEventListener('copy', handleClipboardEvent);
        document.addEventListener('cut', handleClipboardEvent);
        // document.addEventListener('paste', handleClipboardEvent);

        return () => {
            // Clean up event listeners on component unmount
            document.removeEventListener('copy', handleClipboardEvent);
            document.removeEventListener('cut', handleClipboardEvent);
            // document.removeEventListener('paste', handleClipboardEvent);
        };
    }, [props.documentId]);




    // useEffect(() => {
    //     const handleCopy = (event: ClipboardEvent) => {
    //         const selectedText = window.getSelection()?.toString();
    //
    //         // copy as html instead
    //         if (selectedText) {
    //             event.clipboardData?.setData("text/plain", selectedText);
    //         }
    //     };
    //
    //     document.addEventListener("copy", handleCopy);
    //     return () => {
    //         document.removeEventListener("copy", handleCopy);
    //     };
    // }, []);

    const getCustomSlashMenuItems = (
        editor: BlockNoteEditor
    ): DefaultReactSuggestionItem[] => {
        const items = [
            ...getDefaultReactSlashMenuItems(editor),
        ];
        return items.filter(i => !slashExclude.includes(i.title))
    };

    const handleAddComment = async (content: Block<any, any, any>[], selectedText?: string) => {
        if (!editor) return;

        try {
            const comments = editor.comments;
            const cleanedContent = content.filter(k => Array.isArray(k.content) && k.content.length > 0)
            if (!comments) return;

            // Store current document state
            const view = editor.prosemirrorView!;
                const pmSelection = view.state.selection;

            const ystate = ySyncPluginKey.getState(view.state); 
            const selection = getRelativeSelection(ystate.binding, view.state)
            const thread = await comments.createThread({
                initialComment: {
                    body: cleanedContent,
                    metadata: {
                        commentedText: editor.getSelectedText(),
                        // userId: user?.id,
                        parentId: null,
                        range: {

                            proseMirror: {
                                head: editor.prosemirrorState.selection.head,
                            anchor: editor.prosemirrorState.selection.anchor
                            },
                            yjs: selection ? selection : undefined

                        }
                    }
                },
                metadata: {
                    commentedText:  editor.getSelectedText(),
                    // userId: user?.id
                }
            });

            console.log('🟢 Comment created:', thread);
            comments.stopPendingComment();


        } catch (error) {
            console.error('🔴 Error in safe comment creation:', error);
        } finally {
            //     // Resume YJS synchronization
            //     if (provider) {
            //         console.log('🟢 Resuming YJS sync');
            //         setTimeout(() => {
            //             provider.connect();
            //         }, 100); // Small delay to ensure comment operation is complete
            // }
        }
    };

    return <>
        {!isEditorReady && <>
            <div className='h-40'>
                <PageLoader size='full'/>
            </div>
        </>}
        <div ref={divRef} id={`${props.documentId}`} className={cn('yDoc', className)}>
            {isEditorReady && editor && <BlockNoteView
                formattingToolbar={false}
                emojiPicker={false}
                editor={editor}
                editable={editable}
                // slashMenu={false}
                onChange={(editor, context) => {
                    console.log("Editor content changed", context.getChanges());
                    const divEle = divRef.current
                    if (!divEle) return
                    const text = divEle.innerText
                    props.onChange?.(text)
                }}

                comments={false}
                className={cn(`pt-4 pb-8 mx-auto ${jakartaSans.className}`, editorClassName || '')}
                theme={"light"}>

                <CustomFormattingToolbar />
                <SuggestionMenuController
                    triggerCharacter={"/"}
                    // Replaces the default Slash Menu items with our custom ones.
                    getItems={async (query) => {
                        // @ts-ignore
                        return filterSuggestionItems(getCustomSlashMenuItems(editor), query);
                    }}
                />

                <SuggestionMenuController
                    triggerCharacter={"@"}
                    getItems={async (query) =>
                        // Gets the mentions menu items
                        filterSuggestionItems(getMentionMenuItems(editor, members), query)
                    }
                />
                {!readonly && <FloatingComposerController
                    floatingComposer={() => <FloatingCommentPopup
                        workspaceMembers={members}
                        onAddComment={(val) => handleAddComment(val)}
                        selectedText={editor.getSelectedText()}
                        currentUser={user!}
                        className="absolute w-80 max-h-[60vh]"
                    />}

                />
                }
            </BlockNoteView>}
        </div>

    </>
}

function htmlToText(html: string) {
    const tempElement = document.createElement('div');
    tempElement.innerHTML = html;
    return tempElement.textContent || tempElement.innerText || '';
}

function getUserColor(userId: string, workspaceId: string, isLight = false) {
    // Generate hash value from user ID
    const hash = crypto.createHash('md5').update(`${userId}|${workspaceId}`).digest('hex');

    // Convert hexadecimal hash value to a number
    const hashNumber = parseInt(hash, 16);

    const colour = ColorEntries[hashNumber % ColorEntries.length];
    let color: string = colour.hex;

    // Function to determine if a color is light
    const isColorLight = (hex: string) => {
        // Convert hex to RGB
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        // Calculate the luminance (using the perceived brightness formula)
        const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
        return luminance > 186; // Adjust this threshold as necessary
    };

    // Adjust the color based on the isLight flag
    if (isLight && !isColorLight(color)) {
        // If it's supposed to be light but is dark, adjust it
        color = adjustToLight(color);
    } else if (!isLight && isColorLight(color)) {
        // If it's supposed to be dark but is light, adjust it
        color = adjustToDark(color);
    }

    return color;
}

const collabRoomHash = (workspaceId:string, userId: string, room: string, connectTs: number) => {
    return crypto.createHash('sha256').update(`${collabServiceHash()}|${workspaceId}|${room}|${userId}|${connectTs}`).digest('hex')
}

export const CustomFormattingToolbar = () => {
    const editor = useBlockNoteEditor<
        BlockSchema,
        InlineContentSchema,
        StyleSchema
    >();
    const [isVisible, setVisible] = useState(false);

    useEditorSelectionChange(() => {
        const selection = editor.getSelection();

        // Get the blocks in the current selection and store on the state. If
        // the selection is empty, store the block containing the text cursor
        // instead.
        let blocks = [];
        if (selection !== undefined) {
            blocks = selection.blocks;
        } else {
            blocks = [editor.getTextCursorPosition().block];
        }
        const isTrue = blocks.some(
            (block) =>
                block.type !== "mermaid" &&
                block.type !== "role" &&
                block.type !== "control" &&
                block.type !== "code"
        );
        setVisible(isTrue);
    }, editor);
    return isVisible ? (
        <FormattingToolbarController
            formattingToolbar={() => (

                <FormattingToolbar>
                    <BlockTypeSelect key={"blockTypeSelect"} />
                    {/* Our personal addition! */}
                    <FileCaptionButton key={"fileCaptionButton"} />
                    <FileReplaceButton key={"replaceFileButton"} />
                    <BasicTextStyleButton basicTextStyle={"bold"} key={"boldStyleButton"} />
                    <BasicTextStyleButton basicTextStyle={"italic"} key={"italicStyleButton"} />
                    <CommentButton key={"comment"} />
                    {/* <SuggesstionButton key={"suggestion"} /> */}

                    <BasicTextStyleButton basicTextStyle={"underline"} key={"underlineStyleButton"} />
                    <BasicTextStyleButton basicTextStyle={"strike"} key={"strikeStyleButton"} />
                    <BasicTextStyleButton key={"codeStyleButton"} basicTextStyle={"code"} />
                    <TextAlignButton textAlignment={"left"} key={"textAlignLeftButton"} />
                    <TextAlignButton textAlignment={"center"} key={"textAlignCenterButton"} />
                    <TextAlignButton textAlignment={"right"} key={"textAlignRightButton"} />
                    <ColorStyleButton key={"colorStyleButton"} />
                    <NestBlockButton key={"nestBlockButton"} />
                    <UnnestBlockButton key={"unnestBlockButton"} />
                    <CreateLinkButton key={"createLinkButton"} />
                </FormattingToolbar>
            )}
        />
    ) : null;
};
