{"name": "opendashboard-frontend", "version": "0.1.0", "private": true, "scripts": {"generate-sw": "node src/config/generate-sw-firebase.js", "dev": "npm run generate-sw && next dev", "build": "npm run generate-sw && next build", "start": "next start", "lint": "next lint", "ts-errors": "tsc --noEmit", "buildFALightIcons": "node buildIcons.js -i ./assets/fontawesome/light -o ./src/components/icons/FontAwesomeLight.tsx", "buildFARegularIcons": "node buildIcons.js -i ./assets/fontawesome/regular -o ./src/components/icons/FontAwesomeRegular.tsx", "buildFASharpRegularIcons": "node buildIcons.js -i ./assets/fontawesome/sharp-light -o ./src/components/icons/FontAwesomeSharpRegular.tsx", "buildFASharpLightIcons": "node buildIcons.js -i ./assets/fontawesome/sharp-regular -o ./src/components/icons/FontAwesomeSharpLight.tsx", "buildIcons": "npm run buildFALightIcons && npm run buildFARegularIcons && npm run buildFASharpRegularIcons && npm run buildFASharpLightIcons", "deploy": "npm run generate-sw && pm2 startOrReload ecosystem.config.js", "stop-deploy": "pm2 stop ecosystem.config.js"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@blocknote/core": "^0.31.1", "@blocknote/mantine": "^0.31.1", "@blocknote/react": "^0.31.1", "@contactlab/ds-tokens": "^3.5.0", "@dagrejs/dagre": "^1.1.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@floating-ui/react-dom": "^2.0.8", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^3.3.4", "@mantine/core": "^7.7.1", "@opendashboard-inc/integration-core": "1.0.15", "@opendashboard-inc/integration-core-ui": "1.0.16", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^8.35.0", "@tanstack/react-table": "^8.12.0", "@tiptap/core": "^2.2.3", "@tiptap/extension-bold": "^2.2.3", "@tiptap/extension-code": "^2.2.3", "@tiptap/extension-document": "^2.2.3", "@tiptap/extension-history": "^2.2.3", "@tiptap/extension-image": "^2.2.3", "@tiptap/extension-italic": "^2.2.3", "@tiptap/extension-link": "^2.2.3", "@tiptap/extension-list-item": "^2.2.3", "@tiptap/extension-mention": "^2.2.3", "@tiptap/extension-ordered-list": "^2.2.3", "@tiptap/extension-paragraph": "^2.2.3", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-strike": "^2.2.3", "@tiptap/extension-text": "^2.2.3", "@tiptap/extension-typography": "^2.2.3", "@tiptap/extension-underline": "^2.2.3", "@tiptap/react": "^2.2.3", "@tiptap/starter-kit": "^2.2.3", "@xyflow/react": "^12.3.0", "@yudiel/react-qr-scanner": "^2.2.1", "ag-grid-react": "^31.0.2", "antd": "^5.23.0", "axios": "^1.6.5", "bowser": "^2.11.0", "chart.js": "^4.4.1", "chartjs-chart-funnel": "^4.1.8", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-funnel": "^1.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^0.2.0", "dagre": "^0.8.5", "dompurify": "^3.1.5", "dotenv": "^16.4.7", "elkjs": "^0.9.3", "email-validator": "^2.0.4", "embla-carousel-autoplay": "^8.2.1", "embla-carousel-react": "^8.2.1", "emoji-mart": "^5.5.2", "emoji-picker-react": "^4.9.2", "firebase": "^11.1.0", "javascript-time-ago": "^2.5.9", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lucide-react": "^0.453.0", "mime-ext": "^0.0.2", "moment": "^2.30.1", "next": "14.2.16", "next-themes": "^0.3.0", "opendb-app-db-utils": "git+https://****************************************:<EMAIL>/Opendashboard-Inc/opendb-app-db-utils.git#49b1f5a2151adb0f0bfc19090dc7860422ce3ae9", "papaparse": "^5.4.1", "react": "^18", "react-barcode": "^1.6.1", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.2.0", "react-confirm-alert": "^3.0.6", "react-data-grid": "7.0.0-beta.41", "react-datepicker": "^4.25.0", "react-dom": "^18", "react-dropzone": "14.2.3", "react-fontawesome": "^1.7.1", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.49.3", "react-icons": "^5.5.0", "react-photo-view": "^1.2.4", "react-qr-code": "^2.0.15", "react-simple-maps": "^3.0.0", "recharts": "^2.13.0", "sass": "^1.83.1", "slate": "^0.102.0", "slate-react": "^0.102.0", "socket.io-client": "^4.7.5", "sonner": "^1.4.32", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "tributejs": "^5.1.3", "typewriter-effect": "^2.21.0", "validator": "^13.12.0", "validator-js": "^0.2.1", "xlsx": "^0.18.5", "y-indexeddb": "^9.0.12", "y-socket.io": "^1.1.3"}, "devDependencies": {"@faker-js/faker": "^8.4.0", "@types/dompurify": "^3.0.5", "@types/node": "^20", "@types/papaparse": "^5.3.14", "@types/react": "^18", "@types/react-datepicker": "^4.19.5", "@types/react-dom": "^18", "@types/react-fontawesome": "^1.6.8", "@types/uuid": "^10.0.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5", "web-worker": "^1.3.0"}}