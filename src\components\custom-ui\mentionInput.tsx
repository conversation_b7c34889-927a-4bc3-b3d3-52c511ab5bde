import React, {useEffect, useRef, useState} from "react";
import {createPortal} from "react-dom";
import "./mentionInput.css"
import {Dialog, DialogContent, DialogHeader, DialogTitle} from "@/components/ui/dialog";
import {Label} from "@/components/ui/label";
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";
import {cn} from "@/lib/utils";


export interface MentionInfo {
    label: string;
    tag: string; // e.g. "{{firstName}}"
}

export interface MentionInputProps {
    keyMap: Record<string, MentionInfo>; // { [key]: { label, value } }
    value?: string; // Controlled usage (final text with placeholders)
    defaultValue?: string; // Uncontrolled usage (initial HTML)
    onChange?: (finalText: string) => void; // final processed text
    placeholder?: string;
    className?: string;
    disabled?: boolean; // Add disabled prop
    keepNewLine?: boolean; // Add keepNewLine prop
    id?: string;
    onBlur?: (finalText: string) => void; // Add onBlur prop
    onDebounceChange?: (finalText: string) => void; // Add onDebounceChange prop
    debounceTimeoutMS?: number; // Make debounce timeout configurable
}

interface MentionModalState {
    isOpen: boolean;
    span: HTMLSpanElement | null;
    mentionKey: string;
    currentDefault: string;
}

export function MentionInput({
                                 keyMap,
                                 value,
                                 defaultValue = "",
                                 onChange,
                                 onBlur,
                                 onDebounceChange,
                                 debounceTimeoutMS = 500, // Default to 500ms
                                 placeholder,
                                 className,
                                 id,
                                 disabled = false,
                                 keepNewLine = false,
                             }: MentionInputProps) {
    const divRef = useRef<HTMLDivElement>(null);

    // Current output value
    const currentOutput = useRef<string>("");

    // For debounced updates
    const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);


    // For the mention popup
    const [mentionMode, setMentionMode] = useState(false); // showing mention popup?
    const [mentionSearch, setMentionSearch] = useState(""); // the user typed in the popup search
    const popupRef = useRef<HTMLDivElement>(null); // popup container ref
    const [showAbove, setShowAbove] = useState(false); // whether to show dropdown above input
    const [popupPosition, setPopupPosition] = useState({ top: 0, left: 0 }); // absolute position for portal
    const [positionCalculated, setPositionCalculated] = useState(false); // ensure position is calculated before showing

    // Store the location of the "@" that triggered the popup.
    const [atRange, setAtRange] = useState<Range | null>(null);
    // Ref for the mention popup search input.
    const mentionInputRef = useRef<HTMLInputElement>(null);

    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)
    const skipInnerHtmlUpdate = useRef(false);

    // Modal state for setting a default value for a mention.
    const [modalState, setModalState] = useState<MentionModalState>({
        isOpen: false,
        span: null,
        mentionKey: "",
        currentDefault: "",
    });
    const [draftDefault, setDraftDefault] = useState("");



    // console.log("MentionInput:", {popupPosition, mentionMode, atRange})

    // Convert final text value to HTML with mention spans.
    function parseTextToHtml(text: string): string {
        text = String(text || '');
        const mentionRegex = /\{\{([^/}]+)(?:\/(.*?))?\}\}/g;
        let lastIndex = 0;
        let resultHtml = "";

        for (const match of text.matchAll(mentionRegex)) {
            const start = match.index ?? 0;
            if (start > lastIndex) {
                let plain = safeHtml(text.slice(lastIndex, start));
                if (keepNewLine) {
                    plain = plain.replace(/\n/g, "<br>");
                }
                resultHtml += plain;
            }

            const mentionKey = match[1];
            const mentionDefault = match[2] || "";

            // Find case-insensitive key match
            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);
            const info = actualKey ? keyMap[actualKey] : null;

            if (info) {
                let label = info.label;
                if (mentionDefault.trim() !== "") {
                    label += ` / ${mentionDefault}`;
                }

                const dataValue =
                    mentionDefault.trim() === ""
                        ? `{{${actualKey}}}`
                        : `{{${actualKey}/${mentionDefault}}}`;

                const spanHtml = `<span contenteditable="false" data-tag="${escapeAttr(dataValue)}">${safeHtml(label)}</span>`;
                resultHtml += spanHtml;
            } else {
                resultHtml += safeHtml(match[0]);
            }

            lastIndex = start + match[0].length;
        }

        if (lastIndex < text.length) {
            let plain = safeHtml(text.slice(lastIndex));
            if (keepNewLine) {
                plain = plain.replace(/\n/g, "<br>");
            }
            resultHtml += plain;
        }

        return resultHtml;
    }

    // Helper function for case-insensitive key lookup
    function findCaseInsensitiveKey(obj: Record<string, any>, key: string): string | null {
        const lowerKey = key.toLowerCase();
        for (const k of Object.keys(obj)) {
            if (k.toLowerCase() === lowerKey) {
                return k;
            }
        }
        return null;
    }

    function safeHtml(str: string) {
        return str
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;");
    }

    function escapeAttr(str: string) {
        return str.replace(/"/g, "&quot;").replace(/'/g, "&#39;");
    }

    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.
    useEffect(() => {
        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {
            const currentHtml = divRef.current.innerHTML;
            const newHtml = parseTextToHtml(defaultValue || '');
            if (currentHtml !== newHtml) {
                divRef.current.innerHTML = newHtml;
            }
        }
    }, [defaultValue, value, keyMap]);

    // For controlled mode: update innerHTML when value changes.
    // We skip this update immediately after a mention insertion if necessary.
    useEffect(() => {
        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {
            const currentHtml = divRef.current.innerHTML;
            const newHtml = parseTextToHtml(value);
            // Only update innerHTML if the div is not focused (to preserve the caret position)
            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {
                divRef.current.innerHTML = newHtml;
            }
        }
    }, [value, keyMap]);

    // Build the processed final text from innerHTML.
    function getFinalOutput(): string {
        if (!divRef.current) return "";
        let result = "";

        function traverse(node: Node) {
            if (node.nodeType === Node.TEXT_NODE) {
                result += node.nodeValue || "";
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                const el = node as HTMLElement;
                if (el.hasAttribute("data-tag")) {
                    result += el.getAttribute("data-tag");
                } else if (el.nodeName === "BR") {
                    if (keepNewLine) result += "\n";
                } else {
                    Array.from(el.childNodes).forEach(child => traverse(child));
                    if (keepNewLine && (el.nodeName === "DIV" || el.nodeName === "P")) {
                        result += "\n";
                    }
                }
            }
        }

        Array.from(divRef.current.childNodes).forEach(child => traverse(child));
        return result;
    }

    // Update value and trigger onChange immediately
    function updateValue() {
        const output = getFinalOutput();
        currentOutput.current = output;
        onChange?.(output);

        // Set up debounced update
        if (onDebounceChange) {
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current);
            }

            debounceTimerRef.current = setTimeout(() => {
                onDebounceChange(output);
                debounceTimerRef.current = null;
            }, debounceTimeoutMS);
        }
    }

    // Handle blur event
    function handleBlur() {
        if (disabled) return;
        if (mentionMode) return;
        onBlur?.(currentOutput.current || getFinalOutput());
    }

    function onInput() {
        if (disabled) return;
        updateValue();
    }

    //////////////////////////////////////////////////////////////////////////
    // Mention popup logic
    //////////////////////////////////////////////////////////////////////////

    // When the user types "@", let it insert normally but store the current Range.
    function onKeyDown(e: React.KeyboardEvent<HTMLDivElement>) {
        if (disabled) return;
        if (e.key === "@") {
            const sel = window.getSelection?.();
            if (sel && sel.rangeCount > 0) {
                // Save a clone of the current range where "@" was inserted.
                const currentRange = sel.getRangeAt(0).cloneRange();
                setAtRange(currentRange);
                
                // Calculate position immediately to prevent flash
                if (divRef.current) {
                    const inputRect = divRef.current.getBoundingClientRect();
                    const spaceBelow = window.innerHeight - inputRect.bottom;
                    const spaceAbove = inputRect.top;
                    const dropdownHeight = 250;
                    const minSpaceRequired = 100;
                    
                    const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;
                    setShowAbove(shouldShowAbove);
                    
                    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
                    const scrollY = window.pageYOffset || document.documentElement.scrollTop;
                    
                    setPopupPosition({
                        left: inputRect.left + scrollX,
                        top: shouldShowAbove 
                            ? inputRect.top + scrollY - dropdownHeight - 2
                            : inputRect.bottom + scrollY + 2
                    });
                    
                    // Mark position as calculated
                    setPositionCalculated(true);
                }
            }
            setMentionMode(true);
            setMentionSearch("");
        }
    }

    // Close the mention popup.
    function closeMentionPopup() {
        setMentionMode(false);
        setMentionSearch("");
        setAtRange(null);
        setPositionCalculated(false); // Reset position calculated state
    }

    // Enhanced fuzzy filtering that checks both keys and labels
    function fuzzyFilter(query: string, items: string[]): string[] {
        if (!query.trim()) return items.slice(0, 30);
        const normalizedQuery = query.replace(/\s+/g, "").toLowerCase();

        type Ranked = { item: string; rank: number };
        const results: Ranked[] = [];

        for (const key of items) {
            const info = keyMap[key];
            const normalizedKey = key.replace(/\s+/g, "").toLowerCase();
            const normalizedLabel = info.label.replace(/\s+/g, "").toLowerCase();

            // Rank priorities (lower is better):
            // 1: Key starts with query
            // 2: Label starts with query
            // 3: Key contains query
            // 4: Label contains query

            if (normalizedKey.startsWith(normalizedQuery)) {
                results.push({item: key, rank: 1});
            } else if (normalizedLabel.startsWith(normalizedQuery)) {
                results.push({item: key, rank: 2});
            } else if (normalizedKey.includes(normalizedQuery)) {
                results.push({item: key, rank: 3});
            } else if (normalizedLabel.includes(normalizedQuery)) {
                results.push({item: key, rank: 4});
            }
        }

        results.sort((a, b) => a.rank - b.rank);
        return results.map((r) => r.item).slice(0, 30);
    }

    const allKeys = Object.keys(keyMap);
    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];

    // When a mention is selected from the popup.
    function onMentionSelect(mentionKey: string) {
        if (!divRef.current) return;
        const sel = window.getSelection();
        if (!sel) {
            closeMentionPopup();
            return;
        }
        // Always use the stored range if available.
        let range: Range | null = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);
        if (!range) {
            closeMentionPopup();
            return;
        }

        // Helper: Search backwards in a text node from a given offset to remove "@".
        function removeAtFromTextNode(textNode: Text, pos: number): Range | null {
            const text = textNode.data;
            const searchStart = Math.max(0, pos - 5);
            const searchEnd = Math.min(text.length, pos + 5);
            for (let i = searchEnd - 1; i >= searchStart; i--) {
                if (text.charAt(i) === "@") {
                    textNode.data = text.substring(0, i) + text.substring(i + 1);
                    const newRange = document.createRange();
                    newRange.setStart(textNode, i);
                    newRange.collapse(true);
                    return newRange;
                }
            }
            return null;
        }

        // Try to remove "@" from the current text node.
        if (range.startContainer.nodeType === Node.TEXT_NODE) {
            const textNode = range.startContainer as Text;
            const pos = range.startOffset;
            const newRng = removeAtFromTextNode(textNode, pos);
            if (newRng) {
                range = newRng;
            }
        } else {
            // If not a text node, check previous sibling (if text) from the current container.
            const container = range.startContainer;
            if (container.childNodes.length > 0 && range.startOffset > 0) {
                const prev = container.childNodes[range.startOffset - 1];
                if (prev && prev.nodeType === Node.TEXT_NODE) {
                    const textNode = prev as Text;
                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);
                    if (newRng) {
                        range = newRng;
                    }
                }
            }
        }

        const info = keyMap[mentionKey];
        if (!info) {
            closeMentionPopup();
            return;
        }

        // Create and insert the mention span.
        const span = document.createElement("span");
        span.contentEditable = "false";
        span.setAttribute("data-tag", info.tag);
        span.textContent = info.label;
        span.ondblclick = () => {
            openDefaultModal(span);
        };

        skipInnerHtmlUpdate.current = true;
        range.insertNode(span);

        // Insert a zero-width space filler node after the mention span.
        const filler = document.createTextNode("\u200B");
        span.parentNode?.insertBefore(filler, span.nextSibling);

        // Position the cursor after the filler node.
        const newRange = document.createRange();
        newRange.setStartAfter(filler);
        newRange.collapse(true);
        sel.removeAllRanges();
        sel.addRange(newRange);

        divRef.current.focus();
        closeMentionPopup();
        updateValue();

        setTimeout(() => {
            skipInnerHtmlUpdate.current = false;
        }, 0);
    }

    // Close mention popup on ESC key or outside click.
    useEffect(() => {
        function onKey(e: KeyboardEvent) {
            if (e.key === "Escape") {
                closeMentionPopup();
            }
        }

        function onClickOutside(e: MouseEvent) {
            const target = e.target as Element;
            
            // Check if click is inside the input field
            if (divRef.current && divRef.current.contains(target)) {
                return; // Don't close if clicking inside input
            }
            
            // Check if click is inside the popup (now rendered as portal)
            if (popupRef.current && popupRef.current.contains(target)) {
                return; // Don't close if clicking inside popup
            }
            
            // Close if click is outside both input and popup
            closeMentionPopup();
        }

        if (mentionMode) {
            document.addEventListener("keydown", onKey);
            // Use click instead of mousedown to allow dropdown interactions to process first
            document.addEventListener("click", onClickOutside);
        }
        return () => {
            document.removeEventListener("keydown", onKey);
            document.removeEventListener("click", onClickOutside);
        };
    }, [mentionMode]);

    // Autofocus the mention popup search input when it opens.
    useEffect(() => {
        if (mentionMode) {
            requestAnimationFrame(() => {
                mentionInputRef.current?.focus();
            });
        }
    }, [mentionMode]);

    // Update position on window resize or scroll
    useEffect(() => {
        if (mentionMode && divRef.current) {
            const updatePosition = () => {
                const inputRect = divRef.current?.getBoundingClientRect();
                if (!inputRect) return;
                
                const spaceBelow = window.innerHeight - inputRect.bottom;
                const spaceAbove = inputRect.top;
                const dropdownHeight = 250;
                const minSpaceRequired = 100;
                
                const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;
                setShowAbove(shouldShowAbove);
                
                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
                const scrollY = window.pageYOffset || document.documentElement.scrollTop;
                
                setPopupPosition({
                    left: inputRect.left + scrollX,
                    top: shouldShowAbove 
                        ? inputRect.top + scrollY - dropdownHeight - 2
                        : inputRect.bottom + scrollY + 2
                });
            };
            
            // Only listen for resize and scroll events, don't run immediately
            window.addEventListener('resize', updatePosition);
            window.addEventListener('scroll', updatePosition, true);
            
            return () => {
                window.removeEventListener('resize', updatePosition);
                window.removeEventListener('scroll', updatePosition, true);
            };
        }
    }, [mentionMode]);



    //////////////////////////////////////////////////////////////////////////
    // Double-click mention => open a modal for editing default.
    //////////////////////////////////////////////////////////////////////////

    function openDefaultModal(span: HTMLSpanElement) {
        if (disabled) return;
        const mentionValue = span.getAttribute("data-tag") || "";
        const pattern = /^\{\{([^/}]+)(?:\/(.*?))?\}\}\s*$/;
        const match = mentionValue.match(pattern);
        if (!match) return;

        const mentionKey = match[1];
        const existingDefault = match[2] || "";
        setModalState({
            isOpen: true,
            span,
            mentionKey,
            currentDefault: existingDefault,
        });
        setDraftDefault(existingDefault);
    }

    function confirmDefault() {
        const {span, mentionKey} = modalState;
        if (!span) {
            closeModal();
            return;
        }
        const info = keyMap[mentionKey];
        if (!info) {
            closeModal();
            return;
        }

        const userDefault = draftDefault.trim();
        let newValue = `{{${mentionKey}}}`;
        let newLabel = info.label;
        if (userDefault !== "") {
            newValue = `{{${mentionKey}/${userDefault}}}`;
            newLabel = `${info.label} / ${userDefault}`;
        }

        span.setAttribute("data-tag", newValue);
        span.textContent = newLabel;
        span.ondblclick = () => {
            openDefaultModal(span);
        };

        updateValue();
        closeModal();
    }

    function closeModal() {
        setModalState({
            isOpen: false,
            span: null,
            mentionKey: "",
            currentDefault: "",
        });
    }

    // Ensure existing mention spans are clickable to open the modal.
    useEffect(() => {
        if (!divRef.current || disabled) return;
        const mentionSpans = divRef.current.querySelectorAll("span[data-tag]");
        mentionSpans.forEach((el) => {
            const span = el as HTMLSpanElement;
            if (!span.ondblclick) {
                span.ondblclick = () => {
                    openDefaultModal(span);
                };
            }
        });
    }, [disabled]);

    //////////////////////////////////////////////////////////////////////////
    // Render
    //////////////////////////////////////////////////////////////////////////

    // console.log("Render:", {keyMap, value, defaultValue})

    return (
        <div className="w-full mI relative">
            <div
                id={id}
                ref={divRef}
                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group
                className={cn(`border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6`, className)}
                contentEditable={!disabled}
                style={{whiteSpace: "pre-wrap"}}
                suppressContentEditableWarning
                data-placeholder={!disabled ? placeholder : ""}
                onInput={onInput}
                onKeyDown={onKeyDown}
                onBlur={handleBlur}
                aria-disabled={disabled}
            />

            {mentionMode && !disabled && positionCalculated && typeof document !== 'undefined' && createPortal(
                <div
                    ref={popupRef}
                    className="fixed z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl"
                    style={{
                        left: `${popupPosition.left}px`,
                        top: `${popupPosition.top}px`,
                        maxHeight: showAbove ? '250px' : '250px',
                        pointerEvents: 'auto'
                    }}>
                    <div className="flex flex-col max-h-60">
                        <div className="flex-none">
                            <input
                                ref={mentionInputRef}
                                className="border-b p-2.5 font-medium w-full outline-none"
                                placeholder="Search for mention..."
                                value={mentionSearch}
                                onChange={(e) => setMentionSearch(e.target.value)}
                            />
                        </div>
                        <div className="overflow-auto flex-1">
                            {mentionMatches.length === 0 ? (
                                <div className="text-neutral-600 font-medium text-sm italic p-2.5">
                                    No results
                                </div>
                            ) : (
                                 mentionMatches.map((mKey) => {
                                     const info = keyMap[mKey];
                                     return (
                                         <div
                                             key={mKey}
                                             className="cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap"
                                             style={{ pointerEvents: 'auto' }}
                                             onClick={(e) => {
                                                 e.preventDefault();
                                                 e.stopPropagation();
                                                 onMentionSelect(mKey);
                                             }}>
                                             {info.label}
                                         </div>
                                     );
                                 })
                             )}
                        </div>
                    </div>
                </div>,
                document.body
            )}

            {modalState.isOpen && !disabled && <>
                <Dialog defaultOpen onOpenChange={closeModal}>
                    <DialogContent className="max-w-[600px] !rounded-none p-4">
                        <DialogHeader>
                            <DialogTitle className="font-bold">Set default for {modalState.span?.innerText || modalState.mentionKey}</DialogTitle>
                        </DialogHeader>
                        <div className="flex flex-col gap-2 py-4 pt-0">
                            <div className="flex flex-col flex-1 gap-1">
                                <Label className="text-xs font-medium leading-6 text-gray-900">
                                    Current default: "{modalState.currentDefault}" (leave blank to remove)
                                </Label>
                                <Input
                                    type="text"
                                    autoCapitalize="none"
                                    autoCorrect="off"
                                    autoComplete={`workflow-input-name`}
                                    value={draftDefault}
                                    placeholder="Type new default..."
                                    onChange={(e) => setDraftDefault(e.target.value)}
                                    className="rounded-none text-xs"/>
                            </div>

                            <div>
                                <Button
                                    onClick={confirmDefault}
                                    className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1">
                                    Confirm
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </>}
        </div>
    );
}