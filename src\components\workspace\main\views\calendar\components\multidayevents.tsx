import React from 'react';
import { cn } from '@/lib/utils';
import { EventSegment } from '@/utils/multiDay';
import { ArrowTurnLeftIcon, ArrowTurnRightIcon, ArrowsLeftRightIcon } from '@/components/icons/FontAwesomeRegular';

interface MultiDayEventBadgeProps {
  segment: EventSegment;
  view: 'day' | 'week' | 'month';
  size?: 'small' | 'medium' | 'large';
  className?: string;
  isEndOfEvent?: boolean;
}

export const MultiDayEventBadge: React.FC<MultiDayEventBadgeProps> = ({
  segment,
  view,
  size = 'medium',
  className = '',
  isEndOfEvent
}) => {
  if (!segment.isMultiDay) return null;

  const baseClasses = cn(
    "inline-flex items-center justify-center font-medium text-xs",
    "transition-colors duration-200",
    className
  );

  const getArrowIcon = () => {
    if (view === 'week') {
      const startsThisWeek = segment.isFirstSegment;
      const endsThisWeek = isEndOfEvent;

      if (startsThisWeek && !endsThisWeek) return <ArrowTurnRightIcon className="h-2 w-2" />;
      if (!startsThisWeek && endsThisWeek) return <ArrowTurnLeftIcon className="h-2 w-2" />;
      if (!startsThisWeek && !endsThisWeek) return <ArrowsLeftRightIcon className="h-2 w-2" />;
      return null;
    }

    if (segment.isFirstSegment && !segment.isLastSegment) return <ArrowTurnRightIcon className="h-2 w-2" />;
    if (!segment.isFirstSegment && segment.isLastSegment) return <ArrowTurnLeftIcon className="h-2 w-2" />;
    if (!segment.isFirstSegment && !segment.isLastSegment) return <ArrowsLeftRightIcon className="h-2 w-2" />;
    
    return null;
  };

  const getDayIndicator = () => {
    if (size === 'small' || view === 'month') {
      return getArrowIcon();
    }
    
    if (segment.isFirstSegment) {
      return `1/${segment.totalSegments}`;
    } else if (segment.isLastSegment) {
      return `${segment.totalSegments}/${segment.totalSegments}`;
    } else {
      return `${segment.segmentIndex + 1}/${segment.totalSegments}`;
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'text-[0.6rem] px-1 py-0.5 min-w-4 h-4';
      case 'large':
        return 'text-xs px-2 py-1 min-w-6 h-6';
      default:
        return 'text-[0.65rem] px-1.5 py-0.5 min-w-5 h-5';
    }
  };

  const getColorClasses = () => {
    return 'bg-gray-100 text-gray-600 border border-gray-200';
  };

  const dayIndicator = getDayIndicator();

  if (!dayIndicator) {
    return null;
  }

  const getTooltipText = () => {
    const eventTitle = segment.originalEvent.title;
    const totalWeeks = Math.ceil(segment.totalSegments / 7);

    if (view === 'week') {
      const startDay = new Date(segment.originalEvent.start);
      const currentDay = segment.date;
      const weekNumber = Math.floor(Math.abs(currentDay.getTime() - startDay.getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;

      if (totalWeeks > 1) {
        return `${eventTitle} (Week ${weekNumber} of ${totalWeeks})`;
      }
    }
    
    return `Day ${segment.segmentIndex + 1} of ${segment.totalSegments} - ${eventTitle}`;
  };

  return (
    <span 
      className={cn(
        baseClasses,
        getSizeClasses(),
        getColorClasses(),
        'rounded-full shadow-sm'
      )}
      title={getTooltipText()}
    >
      {dayIndicator}
    </span>
  );
};

export const ContinuationArrow: React.FC<{
  direction: 'left' | 'right' | 'both';
  className?: string;
}> = ({ direction, className = '' }) => {
  const getArrow = () => {
    switch (direction) {
      case 'left':
        return <ArrowTurnLeftIcon className="h-2 w-2" />;
      case 'right':
        return <ArrowTurnRightIcon className="h-2 w-2" />;
      case 'both':
        return <ArrowsLeftRightIcon className="h-2 w-2" />;
      default:
        return '';
    }
  };

  return (
    <span 
      className={cn(
        "inline-flex items-center justify-center",
        "text-xs opacity-70 font-medium",
        "transition-opacity duration-200",
        className
      )}
    >
      {getArrow()}
    </span>
  );
}; 