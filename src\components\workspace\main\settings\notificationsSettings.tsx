"use client"

import {<PERSON><PERSON><PERSON><PERSON>} from "@/components/ui/scroll-area";
import * as React from "react";
import {useCallback, useEffect, useRef, useState} from "react";
import {BellIcon, EnvelopeIcon} from "@heroicons/react/24/outline";
import {Switch} from "@/components/ui/switch";
import {Checkbox} from "@/components/ui/checkbox";
import {useAuth} from "@/providers/user";
import {DeRegisterFCMToken, getSettings, RegisterFCMToken, updateSettings} from "@/api/account";
import {NotificationChannelSetting, NotificationSettings, SettingsType} from "@/typings/user";
import {defaultAPIMessage} from "@/api/common";
import {PageLoader} from "@/components/custom-ui/loader";
import {toast} from "sonner";
import {Dialog, DialogContent, DialogHeader, DialogTitle} from "@/components/ui/dialog";
import {Button} from "@/components/ui/button";
import {useFCM} from "@/providers/fcm";
import {useAlert} from "@/providers/alert";

export const NotificationsSettings = () => {
    const {token} = useAuth()

    const [isLoading, setLoading] = useState(true)
    const [error, setError] = useState("")
    const [settings, setSettings] = useState<NotificationSettings | null>(null)

    const loadSettings = useCallback(async () => {
        if (!token) return
        setError("")
        setLoading(true)
        const res = await getSettings(token.token)
        setLoading(false)
        if (res.error) {
            setError(res.error || defaultAPIMessage)
            return;
        }
        setSettings(res.data.data.settings.notification)
        console.log("Settings:", res.data.data.settings.notification)
    }, [token])

    const saveChannelSetting = (channel: string, channelSetting: NotificationChannelSetting) => {
        const update = {...settings}
        update[`${channel}`] = channelSetting
        saveSettings(update).then()
    }

    const saveSettings = async (partial: Partial<NotificationSettings>) => {
        if (!token || !settings) return
        const update = {...settings, ...partial} as NotificationSettings
        const res = await updateSettings(token.token, SettingsType.Notification, update)
        if (res.error) {
            toast.error(res.error)
            return
        }
        setSettings(update)
        toast.success("Notification settings updated")
    }

    useEffect(() => {
        loadSettings().then()
    }, [loadSettings]);

    return (
        <>
            {(isLoading || error) && <>
                <PageLoader error={error} size="full" cta={error ? {label: 'Reload', onClick: loadSettings} : null}/>
            </>}
            {settings && <ScrollArea className="size-full">
                <div className="p-4 pb-20">
                    {/*<h2 className="font-semibold mb-2">Notifications</h2>*/}
                    <div className="max-w-[650px]">
                        <div className="flex flex-col gap-3">
                            <div className="flex flex-col flex-1 gap-4">
                                <h4 className="block text-sm font-semibold leading-6 text-gray-900">Notify me on</h4>
                                <div className="flex gap-4 items-center">
                                    <BellIcon className="size-4"/>
                                    <div className="flex-1 flex flex-col gap-1">
                                        <h5 className="font-semibold text-sm">Inbox</h5>
                                        <div className="text-xs">You’ll always receive your notification in your
                                            Opendashboard inbox
                                        </div>
                                    </div>
                                    <Switch
                                        checked
                                        disabled
                                        aria-readonly
                                    />
                                </div>
                                <div className="flex gap-4 items-center">
                                    <EnvelopeIcon className="size-4"/>
                                    <div className="flex-1 flex flex-col gap-1">
                                        <h5 className="font-semibold text-sm">Email</h5>
                                        <div className="text-xs">Receive your notifications by email</div>
                                    </div>
                                    <Switch
                                        checked={settings.email.isActive}
                                        onCheckedChange={e => {
                                            saveChannelSetting('email', {...settings.email, isActive: e})
                                        }}
                                        aria-readonly
                                    />
                                </div>
                                {/*<div className="flex gap-4 items-center">*/}
                                {/*    <BellIcon className="size-4"/>*/}
                                {/*    <div className="flex-1 flex flex-col gap-1">*/}
                                {/*        <h5 className="font-semibold text-sm">Push Notification</h5>*/}
                                {/*        <div className="text-xs">Receive push notifications on this device</div>*/}
                                {/*    </div>*/}
                                {/*    <Switch*/}
                                {/*        checked={isPushEnabled}*/}
                                {/*        onCheckedChange={onPushChange}*/}
                                {/*        aria-readonly*/}
                                {/*    />*/}
                                {/*</div>*/}
                                <NotificationModal showToggle/>
                            </div>
                            {settings.email.isActive && <>
                                <div className="flex flex-col flex-1 gap-4 mt-8 font-medium">
                                    <div className="flex gap-4">
                                        <h4 className="block text-sm font-semibold leading-6 text-gray-900">Notify me
                                            when</h4>
                                        <div className="flex-1"></div>
                                        <h4 className="block text-sm font-semibold leading-6 text-gray-900">Mail</h4>
                                    </div>
                                    <div className="flex gap-4 items-center">
                                        <div className="flex-1 flex flex-col gap-1">
                                            <div className="text-xs">
                                                I’m mentioned in a comment or document
                                            </div>
                                        </div>
                                        <Checkbox
                                            checked={settings.email.notifyOn.mention}
                                            onCheckedChange={e => {
                                                saveChannelSetting('email', {
                                                    ...settings.email, notifyOn: {
                                                        ...settings.email.notifyOn,
                                                        mention: !!e
                                                    }
                                                })
                                            }}
                                            aria-readonly
                                        />
                                    </div>
                                    <div className="flex gap-4 items-center">
                                        <div className="flex-1 flex flex-col gap-1">
                                            <div className="text-xs">
                                                Someone replies to my comment
                                            </div>
                                        </div>
                                        <Checkbox
                                            checked={settings.email.notifyOn.replies}
                                            onCheckedChange={e => {
                                                saveChannelSetting('email', {
                                                    ...settings.email, notifyOn: {
                                                        ...settings.email.notifyOn,
                                                        replies: !!e
                                                    }
                                                })
                                            }}
                                            aria-readonly
                                        />
                                    </div>
                                    <div className="flex gap-4 items-center">
                                        <div className="flex-1 flex flex-col gap-1">
                                            <div className="text-xs">I’m assigned in a record</div>
                                        </div>
                                        <Checkbox
                                            checked={settings.email.notifyOn.assignedRecord}
                                            onCheckedChange={e => {
                                                saveChannelSetting('email', {
                                                    ...settings.email, notifyOn: {
                                                        ...settings.email.notifyOn,
                                                        assignedRecord: !!e
                                                    }
                                                })
                                            }}
                                            aria-readonly
                                        />
                                    </div>
                                    <div className="flex gap-4 items-center">
                                        <div className="flex-1 flex flex-col gap-1">
                                            <div className="text-xs">A reminder is due</div>
                                        </div>
                                        <Checkbox
                                            checked={settings.email.notifyOn.dueTask}
                                            onCheckedChange={e => {
                                                saveChannelSetting('email', {
                                                    ...settings.email, notifyOn: {
                                                        ...settings.email.notifyOn,
                                                        dueTask: !!e
                                                    }
                                                })
                                            }}
                                            aria-readonly
                                        />
                                    </div>
                                </div>
                            </>}
                        </div>
                    </div>
                </div>
            </ScrollArea>}
            {/*{pushModal && <NotificationModal onClose={() => setPushModal(false)}/>}*/}
        </>
    )
}

interface NotificationModalProps {
    // onClose: () => void
    showToggle?: boolean
    src?: 'layout'
}

export const NotificationModal = (props: NotificationModalProps) => {

    const [open, setOpen] = useState(false)

    const {permission, requestPermission, fcmToken, setTokenRegistered, tokenRegistered, disableNotification, notificationDisabled} = useFCM();
    const {token, user} = useAuth()
    const {toast} = useAlert()

    const [isRequesting, setIsRequesting] = useState(false)
    const [isRegistering, setIsRegistering] = useState(false)

    const promptNotification = async () => {
        setIsRequesting(true)
        const token = await requestPermission()
        setIsRequesting(false)
        await registerToken(token || '');
    }

    const registerToken = useCallback(async (fcmToken: string, silent = false) => {
        if (isRegistering || !token || !fcmToken) return
        setIsRegistering(true)

        const res = await RegisterFCMToken(token.token, {token: fcmToken})
        if (res.error) {
            setIsRegistering(false)
            if (!silent) toast.error('Failed to register push token: ' + res.error || defaultAPIMessage())
            return
        }
        setIsRegistering(false)
        setTokenRegistered(true)
        if (!silent) toast.success('Push notification enabled')
        setOpen(false)
    }, [isRegistering, props.src, setTokenRegistered, toast, token])

    const disablePushNotification = async () => {
        disableNotification(true)
        const res = await DeRegisterFCMToken(token?.token || '')
        if (res.error) {
            toast.error("Error de-registering token: " + res.error)
            return
        }
        setTokenRegistered(false)
        toast.error("Push notification disabled")
    }

    const updateOpen = (open: boolean) => {
        setOpen(open)

        if (!open && props.src === 'layout') disableNotification(true)
    }

    const isPushEnabled = permission === 'granted' && !notificationDisabled && tokenRegistered

    const onPushChange = (change: boolean) => {
        if (change) {
            disableNotification(false)
            if (permission === 'granted' && fcmToken) registerToken(fcmToken).then()
            else setOpen(true)
        } else disablePushNotification().then()
    }

    const firstTimeRef = useRef(false)

    const notificationSupported= typeof Notification !== 'undefined' && !!Notification

    useEffect(() => {
        if (props.src !== 'layout' || !user || firstTimeRef.current) {
            return
        }
        if (!notificationSupported) return
        // if (typeof window !== "undefined")
        if (typeof Notification === 'undefined' || !Notification) return
        console.log({fcmToken, permission, tokenRegistered, notificationDisabled})
        firstTimeRef.current = true
        if (permission === 'granted' && fcmToken && !tokenRegistered && !notificationDisabled) {
            registerToken(fcmToken, true).then()
            return
        }
        // if account created over an hour ago and notification request isn’t previously rejected
        const createdDate = new Date(user.createdAt || '0');
        const now = new Date();
        const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const isOverAnHourAgo = createdDate < hourAgo;
        const isNotificationRequestPreviouslyRejected = Notification.permission === 'denied' || notificationDisabled
        if (isOverAnHourAgo && !isNotificationRequestPreviouslyRejected) {
            setOpen(true)
        }
    }, [fcmToken, user])

    console.log({permission})

    if (!notificationSupported) return null
    return <>
        {props.showToggle && <>
            <div className="flex gap-4 items-center">
                <BellIcon className="size-4"/>
                <div className="flex-1 flex flex-col gap-1">
                    <h5 className="font-semibold text-sm">Push Notification</h5>
                    <div className="text-xs">Receive push notifications on this device</div>
                </div>
                <Switch
                    checked={isPushEnabled}
                    onCheckedChange={onPushChange}
                    aria-readonly
                />
            </div>
        </>}
        <Dialog open={open} onOpenChange={updateOpen}>
            <DialogContent className="w-96 max-w-full !rounded-none p-4">
                <DialogHeader>
                    <DialogTitle className="font-bold">Enable push notification</DialogTitle>
                </DialogHeader>
                <div className="flex flex-col gap-2 overflow-hidden">
                    <div className="text-xs font-medium">
                        {permission === 'denied' ?
                         'You have denied the notification permission. Please reset the permission on your device and try again.' :
                         permission === 'default' ? 'Enable to receive push notification on this device as things happen in your workspace' : 'Notification permission granted successfully. You will now receive updates.'}
                    </div>
                </div>
                <div className="flex flex-row-reverse">
                    <Button
                        disabled={isRequesting || isRegistering || permission === "denied"}
                        onClick={promptNotification}
                        className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                        Enable Push Notification
                    </Button>
                    <div className='flex-1'></div>
                    <Button
                        onClick={() => updateOpen(false)}
                        variant={"ghost"}
                        className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                        Cancel
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    </>
}