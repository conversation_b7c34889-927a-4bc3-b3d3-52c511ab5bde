"use client";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ButtonAction } from "opendb-app-db-utils/lib/typings/db";
import { TrashIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { CircleExclamationIcon } from "@/components/icons/FontAwesomeRegular";
import { getActionIcon } from "../buttonGroup";
import { useWorkspace } from "@/providers/workspace";
import {  getButtonActionDefinition,  getAvailableActions,  getDefaultPropsForActionType,isActionConfigReady } from "@/utils/buttonActionHelpers";
import { getRendererForFieldType } from "@/components/workspace/main/common/FieldRenderer";
import { DatabaseColumnValueMapper } from "@/components/workspace/main/common/DatabaseColumnValueMapper";
import { DbCondition } from "opendb-app-db-utils/lib/typings/db";
import { CompareOperator } from "opendb-app-db-utils/lib/methods/compare";
import { CompareOperatorSelect, SingleCompare } from "@/components/custom-ui/compareOperatorSelect";
import { MentionInput } from "@/components/custom-ui/mentionInput";
import { UpdateRecordEditor } from "@/components/workspace/main/common/UpdateRecordEditor";
import { CustomSelect } from "@/components/custom-ui/customSelect";
import { TagItem } from "@/components/workspace/main/views/table/renderer/common/tag";
import { WorkspaceSenderEmail } from "@/typings/workspace";
import { Workflow } from "@/typings/workflow";


// Interface for the ActionConfigEditor props
export interface ActionConfigEditorProps {
    action: ButtonAction;
    onUpdate: (updatedAction: ButtonAction) => void;
    onDelete: () => void;
    databaseId: string;
    workspaceSenders: WorkspaceSenderEmail[];
    workflows: Workflow[];
}

export const ActionConfigEditor = ({ action, onUpdate, onDelete, databaseId, workspaceSenders, workflows }: ActionConfigEditorProps) => {
    const { databaseStore } = useWorkspace();

    // Initialize state with action values
    const [actionType, setActionType] = useState<ButtonAction['actionType']>(action.actionType || "openUrl");
    const [props, setProps] = useState<any>(action.props || getDefaultPropsForActionType(action.actionType || "openUrl"));
    const [isExpanded, setIsExpanded] = useState<boolean>(true);

    const buttonAction = getButtonActionDefinition(actionType);
    const availableActions = getAvailableActions();

    // Check if action is ready (all required props filled)
    const isActionReady = () => {
        return isActionConfigReady({ actionType, props });
    };

    const variableKeyMap: Record<string, { label: string; description: string; tag: string }> = {};
    if (databaseId && databaseStore?.[databaseId]?.database?.definition?.columnsMap) {
        const columnsMap = databaseStore[databaseId].database.definition.columnsMap;
        Object.entries(columnsMap).forEach(([columnId, column]) => {
            variableKeyMap[`column.${columnId}`] = {
                label: `Column: ${column.title}`,
                description: `Column value for ${column.title}`,
                tag: `{{column.${columnId}}}`
            };
        });
    }

    variableKeyMap['person.current'] = { label: 'Current User', description: 'Current user ID', tag: '{{person.current}}' };
    variableKeyMap['person.email'] = { label: 'Current User Email', description: 'Current user email address', tag: '{{person.email}}' };
    variableKeyMap['person.name'] = { label: 'Current User Name', description: 'Current user name', tag: '{{person.name}}' };

    const updateProp = (key: string, value: unknown) => {
        const newProps = { ...props, [key]: value };
        setProps(newProps);
        
        const updatedAction: ButtonAction = {
            ...action,
            actionType: actionType,
            props: newProps
        };
        onUpdate(updatedAction);
    };

    const handleActionTypeChange = (value: string) => {
        const newActionType = value as ButtonAction['actionType'];
        setActionType(newActionType);

        let newProps = getDefaultPropsForActionType(newActionType);
        if (newActionType === 'updateRecord') {
            newProps.updates = [];
        }

        // Update local state
        setProps(newProps);

        const updatedAction = {
            ...action,
            actionType: newActionType,
            props: newProps
        } as unknown as ButtonAction;

        onUpdate(updatedAction);
    };

    if (!buttonAction) {
        return null;
    }

    return (
        <div className="border rounded-md mb-3 bg-white overflow-visible">
            <div
                className={`flex items-center justify-between p-3 ${!isExpanded ? 'cursor-pointer hover:bg-gray-50' : ''}`}
                onClick={!isExpanded ? () => setIsExpanded(true) : undefined}
            >
                <div className="flex items-center gap-2">
                    {!isActionReady() && (
                        <CircleExclamationIcon className="size-3 text-destructive inline-block" />
                    )}
                    {isExpanded ? (
                        <CustomSelect
                            options={availableActions.map(({ value, label }) => ({
                                id: value,
                                value: value,
                                title: label,
                                data: { value, label }
                            } as TagItem<{ value: string; label: string }>))}
                            selectedIds={[actionType]}
                            onChange={(selectedIds) => {
                                if (selectedIds.length > 0) {
                                    handleActionTypeChange(selectedIds[0]);
                                }
                            }}
                            className="h-8 border border-solid rounded-md w-40 text-xs"
                            placeholder="Select action type"
                            hideSearch={false}
                            itemSelectionRender={(_key, _index, item, isSelected, handleSelection) => (
                                <Button
                                    variant='ghost'
                                    onClick={(e) => {
                                        e.preventDefault();
                                        handleSelection(item.id, !isSelected);
                                    }}
                                    className={`text-xs gap-2 rounded-none p-2 mb-1 w-full justify-start ${
                                        isSelected ? 'bg-neutral-100' : 'hover:bg-gray-50'
                                    }`}
                                >
                                    {getActionIcon(item.data.value)}
                                    <span className="truncate flex-1 text-left">{item.data.label}</span>
                                </Button>
                            )}
                            itemRender={(_key, _index, item) => (
                                <div className="flex items-center gap-2 text-xs">
                                    {getActionIcon(item.data.value)}
                                    <span className="truncate">{item.data.label}</span>
                                </div>
                            )}
                        />
                    ) : (
                        <div className="flex items-center gap-2">
                            {getActionIcon(actionType)}
                            <span className="text-sm font-medium">
                                {availableActions.find(a => a.value === actionType)?.label || actionType}
                            </span>
                        </div>
                    )}
                </div>
                <div className="flex items-center gap-2">
                    {isExpanded && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsExpanded(false)}
                            className="h-8 text-xs border border-solid rounded-md"
                        >
                            Done
                        </Button>
                    )}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onDelete}
                        className="p-1 h-6 w-6 hover:bg-gray-50 text-gray-700"
                    >
                        <TrashIcon className="h-3 w-3" />
                    </Button>
                </div>
            </div>

            {isExpanded && (

            <div className="space-y-3 mt-3 p-3">
                {Object.entries(buttonAction.props || {}).map(([key, prop]) => {
                    if (key === 'databaseId') {
                        const dbOptions = Object.entries(databaseStore || {}).map(([dbId, dbData]) => ({
                            id: dbId,
                            value: dbId,
                            title: dbData.database?.name || dbId,
                            data: dbData.database || {},
                        }));
                        return (
                            <div key={key} className="space-y-1">
                                <Label className="text-xs font-medium">{prop.displayName}</Label>
                                <CustomSelect
                                    selectedIds={[props[key] || databaseId]}
                                    options={dbOptions}
                                    onChange={(selected) => updateProp(key, selected[0])}
                                    placeholder="Select database"
                                    className="text-xs"
                                />
                                {prop.description && (
                                    <p className="text-xs text-gray-500">{prop.description}</p>
                                )}
                            </div>
                        );
                    }

                    if (key === 'workflowId') {
                        const workflowOptions = workflows.map((workflow) => ({
                            id: workflow.id.toString(),
                            value: workflow.id.toString(),
                            title: workflow.name || `Workflow ${workflow.id}`,
                            data: workflow,
                        }));
                        return (
                            <div key={key} className="space-y-1">
                                <Label className="text-xs font-medium">{prop.displayName}</Label>
                                <CustomSelect
                                    selectedIds={[props[key]]}
                                    options={workflowOptions}
                                    onChange={(selected) => updateProp(key, selected[0])}
                                    placeholder="Select workflow"
                                    className="text-xs"
                                />
                                {prop.description && (
                                    <p className="text-xs text-gray-500">{prop.description}</p>
                                )}
                            </div>
                        );
                    }

                    if (key === 'senderId') {
                        const senderOptions = workspaceSenders.map((sender) => ({
                            id: sender.id.toString(),
                            value: sender.id.toString(),
                            title: `${sender.name} (${sender.email})${sender.isVerified ? ' ✓' : ' (Unverified)'}`,
                            data: sender,
                        }));
                        return (
                            <div key={key} className="space-y-1">
                                <Label className="text-xs font-medium">{prop.displayName}</Label>
                                <CustomSelect
                                    selectedIds={[props[key]]}
                                    options={senderOptions}
                                    onChange={(selected) => updateProp(key, selected[0])}
                                    placeholder="Select sender"
                                    className="text-xs"
                                />
                                {prop.description && (
                                    <p className="text-xs text-gray-500">{prop.description}</p>
                                )}
                            </div>
                        );
                    }

                    if (actionType === 'updateRecord' && key === 'updates') {
                        return (
                            <div key={key} className="space-y-1">
                                <Label className="text-xs font-medium">{prop.displayName}</Label>
                                <UpdateRecordEditor
                                    databaseId={props.databaseId || databaseId}
                                    updates={props[key] || []}
                                    onUpdate={(newUpdates) => updateProp(key, newUpdates)}
                                    variableKeyMap={variableKeyMap}
                                />
                                {prop.description && (
                                    <p className="text-xs text-gray-500">{prop.description}</p>
                                )}
                            </div>
                        )
                    }

                    const FieldRenderer = getRendererForFieldType(prop.type);
                    if (FieldRenderer) {
                        return (
                            <FieldRenderer
                                key={key}
                                value={props[key]}
                                onChange={(value: unknown) => updateProp(key, value)}
                                propKey={key}
                                prop={prop}
                                disabled={false}
                                tagOptionsMap={variableKeyMap}
                            />
                        );
                    }

                    return (
                        <div key={key} className="text-xs text-gray-500">
                            Unsupported field type: {prop.type}
                        </div>
                    );
                })}
            </div>
            )}
        </div>
    );
};

interface ConditionEditorProps {
    condition: DbCondition;
    databaseId: string;
    onChange: (condition: DbCondition) => void;
    onDelete: () => void;
    variableKeyMap: Record<string, { label: string; description: string; tag: string }>;
}

export const ConditionEditor: React.FC<ConditionEditorProps> = ({
    condition,
    databaseId,
    onChange,
    onDelete,
    variableKeyMap
}) => {
    const { databaseStore } = useWorkspace();

    const database = databaseStore?.[databaseId];
    const columns = database?.database?.definition?.columnsMap || {};
    const column = columns[condition.columnId];

    return (
        <div className="flex items-center gap-2 p-2 border rounded-md bg-gray-50">
            <div className="w-32">
                <MentionInput
                    keyMap={variableKeyMap}
                    value={condition.columnId}
                    onChange={(value) => onChange({ ...condition, columnId: value })}
                    placeholder="Field"
                    className="text-xs"
                />
            </div>

            <div className="w-32">
                <CompareOperatorSelect
                    value={condition.op}
                    onChange={(op) => onChange({ ...condition, op })}
                    fieldType={column?.type}
                    className="text-xs"
                />
            </div>

            {!SingleCompare.includes(condition.op) && (
                <div className="flex-1">
                    <MentionInput
                        keyMap={variableKeyMap}
                        value={Array.isArray(condition.value) ? condition.value.join(', ') : (condition.value || '')}
                        onChange={(value) => onChange({ ...condition, value })}
                        placeholder="Value"
                        className="text-xs"
                    />
                </div>
            )}

            <Button
                variant="ghost"
                size="sm"
                onClick={onDelete}
                className="p-1 h-6 w-6 hover:bg-red-50 text-red-600"
            >
                <TrashIcon className="h-3 w-3" />
            </Button>
        </div>
    );
};

