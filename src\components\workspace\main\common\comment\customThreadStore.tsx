import { CommentBody, CommentData, ThreadData, ThreadStoreAuth, YjsThreadStoreBase } from "@blocknote/core/comments";
import * as Y from "yjs";
import { buildBlocknoteComment, commentToYMap, threadToYMap, yArrayFindIndex, yMapToComment, yMapToThread } from "./storeHelper";
import { httpRequest } from "@/utils/http";
import { Method } from "axios";
import { BackendAPIResponse, normalizeResponse } from "@/api/common";
import { Comment } from "@/typings/comment";

export class CustomThreadStore extends YjsThreadStoreBase {
  constructor(
      private readonly BASE_URL: string,
      private readonly queryStr:string,
    private readonly headers: Record<string, string>,
    threadsYMap: Y.Map<any>,
      auth: ThreadStoreAuth,
    private readonly userId:string
  ) {
    super(threadsYMap, auth);
  }

    private doRequest = async (path: string, method: Method, body?: any) => {
      const headers =  {
        "Content-Type": "application/json",
        ...this.headers,
      }
       const endpoint = `${this.BASE_URL}${path}?${this.queryStr}`;
       const response = await httpRequest(method, endpoint, headers,body);
       const responseData = normalizeResponse(response) as BackendAPIResponse<{ comment: Comment[] | Comment }>;
      
      
    if (!response.isSuccess) {
      throw new Error(`Failed to ${method} ${path}: ${response.error}`);
        }
        
    return responseData.data.data.comment;
  };

  public addThreadToDocument = undefined;

  // Helper method to execute Yjs operations with 'comments' origin
  private executeWithCommentsOrigin<T>(operation: () => T): T {
    return this.threadsYMap.doc!.transact(() => {
        // Add a custom property to identify this as a comment transaction
      (this.threadsYMap as any)._isCommentTransaction = true;
        const result = operation();
      delete (this.threadsYMap as any)._isCommentTransaction;
        return result;
    }, 'comments');
}


  public createThread = async (options: {
    initialComment: {
      body: CommentBody;
      metadata?: any;
    };
    metadata?: any;
  }) => {
    const reqBody = {
      content: options.initialComment.body,
      commentedText: options.initialComment.metadata.commentedText,
      position: options.initialComment.metadata.range
    }

    const response = await this.doRequest("/", "POST", reqBody) as Comment
    
    console.log({response})
    const comment = buildBlocknoteComment(response)
   
    const thread : ThreadData = {
      type: "thread",
      id: comment.id,
      createdAt: new Date(comment.createdAt),
      updatedAt: new Date(comment.updatedAt),
      comments: [comment],
      resolved: response.isResolved,
      metadata: { ...options.metadata, userId: this.userId },
    };

    // Update Yjs with 'comments' origin
    this.updateYjsThread(thread);
    
    return thread;
  };

  public addComment = async (options: {
    comment: {
      body: CommentBody;
      metadata?: any;
    };
    threadId: string;
  }) => {
    const { threadId, ...rest } = options;
    const reqBody = {
      content: rest.comment.body,
      parentId: threadId,
      commentedText: rest.comment.metadata.commentedText,
    }
    
    const response = await this.doRequest("/", "POST", reqBody) as Comment
    
    console.log({response})
    const comment = buildBlocknoteComment(response)
    
    // Update Yjs with 'comments' origin

      const yThread = this.threadsYMap.get(options.threadId);

      if (!yThread) {
        throw new Error("Thread not found");
      }
      
      (yThread.get("comments") as Y.Array<Y.Map<any>>).push([
        commentToYMap(comment),
      ]);

    yThread.set("updatedAt", new Date().getTime());

    return comment;
  };

  public updateComment = async (options: {
    comment: {
      body: CommentBody;
      metadata?: any;
    };
    threadId: string;
    commentId: string;
  }) => {
    const { threadId, commentId, ...rest } = options;
    const reqBody = {
      content: rest.comment.body,
    }
    
    await this.doRequest(`/${commentId}`, "patch", reqBody);
    
    // If you need to update the Yjs comment data, do it here with comments origin
    // this.executeWithCommentsOrigin(() => {
    //   // Update comment in Yjs
    // });
  };

  public deleteComment = async (options: {
    threadId: string;
    commentId: string;
    softDelete?: boolean;
  }) => {
    const { threadId, commentId, ...rest } = options;
    const yThread = this.threadsYMap.get(options.threadId);
    if (!yThread) {
      throw new Error("Thread not found");
    }



    const yCommentIndex = yArrayFindIndex(
      yThread.get("comments"),
      (comment) => comment.get("id") === options.commentId,
    );

    if (yCommentIndex === -1) {
      throw new Error("Comment not found");
    }

    const yComment = yThread.get("comments").get(yCommentIndex);

    if (yComment.get("deletedAt")) {
      throw new Error("Comment already deleted");
    }

    await this.doRequest(`/${commentId}`, "DELETE");

    if (options.softDelete) {
      yComment.set("deletedAt", new Date().getTime());
      yComment.set("body", undefined);
    } else {
      yThread.get("comments").delete(yCommentIndex);
    }

    if (
      (yThread.get("comments") as Y.Array<any>)
        .toArray()
        .every((comment) => comment.get("deletedAt"))
    ) {
      // all comments deleted
      if (options.softDelete) {
        yThread.set("deletedAt", new Date().getTime());
      } else {
        this.threadsYMap.delete(options.threadId);
      }
    }

    yThread.set("updatedAt", new Date().getTime());
  };

  public deleteThread = async (options: { threadId: string }) => {
    if (
      !this.auth.canDeleteThread(
        yMapToThread(this.threadsYMap.get(options.threadId)),
      )
    ) {
      throw new Error("Not authorized");
    }

    await this.doRequest(`/${options.threadId}`, "DELETE");
    
    // Update Yjs with 'comments' origin
    this.threadsYMap.delete(options.threadId);
  };

  public resolveThread = async (options: { threadId: string }) => {
    const yThread = this.threadsYMap.get(options.threadId);
    if (!yThread) {
      throw new Error("Thread not found");
    }

    if (!this.auth.canResolveThread(yMapToThread(yThread))) {
      throw new Error("Not authorized");
    }

    await this.doRequest(`/${options.threadId}`, "patch", { isResolved: true });
    
    // Update Yjs with 'comments' origin
      yThread.set("resolved", true);
      yThread.set("resolvedUpdatedAt", new Date().getTime());
    yThread.set("resolvedBy", this.userId);
  };

  public unresolveThread = async (options: { threadId: string }) => {
    const yThread = this.threadsYMap.get(options.threadId);
    if (!yThread) {
      throw new Error("Thread not found");
    }

    if (!this.auth.canUnresolveThread(yMapToThread(yThread))) {
      throw new Error("Not authorized");
    }

    await this.doRequest(`/${options.threadId}`, "patch", { isResolved: false });
    
    // Update Yjs with 'comments' origin
      yThread.set("resolved", false);
    yThread.set("resolvedUpdatedAt", new Date().getTime());
  };

  public addReaction = async (options: {
    threadId: string;
    commentId: string;
    emoji: string;
  }) => {
    const { threadId, commentId, ...rest } = options;

    const yThread = this.threadsYMap.get(options.threadId);
    if (!yThread) {
      throw new Error("Thread not found");
    }

    const yCommentIndex = yArrayFindIndex(
      yThread.get("comments"),
      (comment) => comment.get("id") === options.commentId,
    );

    if (yCommentIndex === -1) {
      throw new Error("Comment not found");
    }

    const yComment = yThread.get("comments").get(yCommentIndex);
    const key = `${this.userId}-${options.emoji}`;
    const reactionsByUser = yComment.get("reactionsByUser");

    if (reactionsByUser.has(key)) {
      // already exists
      return;
    }

    await this.doRequest(`/${commentId}/add-reaction`, "post", rest);
    
    // Update Yjs with 'comments' origin
      const date = new Date();
      const reaction = new Y.Map();
      reaction.set("emoji", options.emoji);
      reaction.set("createdAt", date.getTime());
      reaction.set("userId", this.userId);
    reactionsByUser.set(key, reaction);
  };

  public deleteReaction = async (options: {
    threadId: string;
    commentId: string;
    emoji: string;
  }) => {
    const { threadId, commentId, ...rest } = options;
    const yThread = this.threadsYMap.get(options.threadId);
    if (!yThread) {
      throw new Error("Thread not found");
    }

    const yCommentIndex = yArrayFindIndex(
      yThread.get("comments"),
      (comment) => comment.get("id") === options.commentId,
    );

    if (yCommentIndex === -1) {
      throw new Error("Comment not found");
    }

    const yComment = yThread.get("comments").get(yCommentIndex);

    if (
      !this.auth.canDeleteReaction(yMapToComment(yComment), options.emoji)
    ) {
      throw new Error("Not authorized");
    }

    await this.doRequest(`/${commentId}/delete-reaction`, "delete", rest);
    
    // Update Yjs with 'comments' origin
      const key = `${this.userId}-${options.emoji}`;
      const reactionsByUser = yComment.get("reactionsByUser");
    reactionsByUser.delete(key);
  };

  private updateYjsThread(threadData: ThreadData) {
    const existingThread = this.threadsYMap.get(threadData.id);
    
    if (existingThread) {
      // Update existing thread
      this.updateYjsThreadData(existingThread, threadData);
    } else {
      // Create new thread
      const yjsThread = this.convertToYjsThread(threadData);
      this.threadsYMap.set(threadData.id, yjsThread);
    }
  }

  private updateYjsThreadData(yjsThread: Y.Map<any>, threadData: any) {
    // Update basic properties
    yjsThread.set('resolved', threadData.resolved);
    yjsThread.set('updatedAt', threadData.updatedAt);

    // Update comments array
    const commentsArray = yjsThread.get('comments') as Y.Array<any>;
    if (commentsArray && threadData.comments) {
      // Clear and rebuild comments array
      commentsArray.delete(0, commentsArray.length);
      threadData.comments.forEach((comment: any) => {
        const yjsComment = new Y.Map();
        Object.entries(comment).forEach(([key, value]) => {
          yjsComment.set(key, value);
        });
        commentsArray.push([yjsComment]);
      });
    }
  }

  private convertToYjsThread(threadData: ThreadData): Y.Map<any> {
    return threadToYMap(threadData);
  }
}