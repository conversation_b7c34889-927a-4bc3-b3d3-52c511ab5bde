import * as React from "react"
import {Fragment, ReactNode, useState} from "react"
import {EllipsisHorizontalIcon} from "@heroicons/react/24/outline";
import {Checkbox} from "@/components/ui/checkbox";
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {Button} from "@/components/ui/button";
import {useSortable} from "@dnd-kit/sortable";
import {CSS} from "@dnd-kit/utilities";
import {DataViewRow} from "@/components/workspace/main/views/table";
import {ViewColumnCustomization} from "opendb-app-db-utils/lib/typings/view";
import {CreatedAtColumn, DatabaseDefinition, DatabaseFieldDataType, DateColumn, FilesColumnDbValue, LinkedColumn, SelectColumn, UpdatedAtColumn} from "opendb-app-db-utils/lib/typings/db";
import {recordValueToText} from "opendb-app-db-utils/lib/utils/db";
import {DatabaseFieldTypeIcon} from "@/components/workspace/main/database/databaseFieldTypeIcon";
import {getTextBasedColFormattedValue} from "@/components/workspace/main/views/table/renderer/fields/text";
import {RenderPureSelect} from "@/components/workspace/main/views/table/renderer/fields/select";
import {RenderPureLinked} from "@/components/workspace/main/views/table/renderer/fields/linked";
import {useWorkspace} from "@/providers/workspace";
import {RenderPureDate} from "@/components/workspace/main/views/table/renderer/fields/date";
import {RenderPurePerson} from "@/components/workspace/main/views/table/renderer/fields/person";
import {useViews} from "@/providers/views";
import {removeAllArrayItem} from "opendb-app-db-utils/lib";
import {useStackedPeek} from "@/providers/stackedpeek";

interface ItemCardProps {
    item: DataViewRow
    dataColumnsOrder: string[]
    dataColumnPropsMap: {
        [p: string]: ViewColumnCustomization
    }
    databaseDefinition: DatabaseDefinition
    titleColId: string
    databaseId: string
    disabled?: boolean
    columnId: string
}

export const ItemCard = (props: ItemCardProps) => {
    const {selectedIds, setSelectedIds} = useViews()
    const [mouseIsOver, setMouseIsOver] = useState(false);


    const {deleteRecords} = useViews()
    const {url} = useWorkspace()
    const {openRecord} = useStackedPeek()

    // const itemUrl = url(`/databases/${props.databaseId}/records/${props.item.id}`)
    // const router = useRouter()

    const {item, databaseDefinition, dataColumnsOrder, dataColumnPropsMap, titleColId} = props
    let {
        setNodeRef,
        attributes,
        listeners,
        transform,
        transition,
        isDragging,
    } = useSortable({
        id: `${item.id}|${props.columnId}`,
        data: {
            type: "Item",
            item,
            columnId: props.columnId
        },
        // disabled: editMode,
    });

    // isDragging = false
    const style = {
        transition,
        transform: CSS.Transform.toString(transform),
    };

    const title = recordValueToText(item.processedRecord.processedRecordValues[titleColId]) || 'Untitled'

    return <>
        <div className={`w-full border bg-white overflow-hidden ${isDragging ? 'opacity-0 invisible' : ''}`}
             ref={setNodeRef}
             style={style}
             onClick={() => openRecord(props.item.id, props.databaseId)}
             {...attributes}
             {...listeners}>
            <div className="p-2.5 text-sm font-semibold flex gap-2 items-center">
                {!props.disabled && <Checkbox
                    onClick={e => e.stopPropagation()}
                    checked={selectedIds.includes(props.item.id)}
                    onCheckedChange={c => {
                        if (c) setSelectedIds([...selectedIds, props.item.id])
                        else setSelectedIds([...removeAllArrayItem(selectedIds, props.item.id)])
                    }}/>}
                <div className="flex-1 text-xs truncate">
                    {title}
                </div>
                {!props.disabled && props.databaseId && <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant={"ghost"} className="rounded-full h-auto p-1"><
                            EllipsisHorizontalIcon className='size-4'/>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56  rounded-none text-neutral-800 font-semibold" align="end">
                        <DropdownMenuItem className="text-xs rounded-none p-2"
                                          onClick={() => {
                                              deleteRecords(props.databaseId, [props.item.id]).then()
                                          }}>
                            Delete
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>}
            </div>
            <div className="flex flex-col gap-3 px-2.5  pb-4">
                {dataColumnsOrder.map(colId => {
                    const colProps = dataColumnPropsMap[colId]
                    if (colProps.isHidden) return null

                    return <Fragment key={colId}>
                        <RenderField
                            definition={props.databaseDefinition}
                            columnId={colId}
                            item={props.item}/>
                    </Fragment>
                })}
            </div>
        </div>
    </>
};


interface RenderFieldProps {
    // adjacentDatabases: AdjacentDatabases
    definition: DatabaseDefinition
    item: DataViewRow
    columnId: string
}

export const RenderField = (props: RenderFieldProps) => {
    const {databaseStore, databaseErrorStore, members} = useWorkspace()
    const column = props.definition.columnsMap[props.columnId];

    if (!column) return null
    let content: ReactNode;

    const rowValue = props.item.processedRecord.processedRecordValues[props.columnId] || ''
    if (!rowValue
        || (typeof rowValue === 'string' && !rowValue.trim())
        || (Array.isArray(rowValue) && rowValue.length === 0)
    ) return null

    switch (column.type) {
        case DatabaseFieldDataType.UUID:
        case DatabaseFieldDataType.Text:
        case DatabaseFieldDataType.Number:
        case DatabaseFieldDataType.AI:
        case DatabaseFieldDataType.Summarize:
        case DatabaseFieldDataType.Derived:
            let textValue = String(rowValue)
            if (column.type === DatabaseFieldDataType.Text || column.type === DatabaseFieldDataType.Number) {
                const f = getTextBasedColFormattedValue(column, textValue)
                textValue = f.displayValue
            }
            content = <div className='font-medium truncate text-xs'>{textValue}</div>
            break;
        case DatabaseFieldDataType.Select:
            content = <RenderPureSelect column={column as SelectColumn} row={props.item}/>
            break;
        case DatabaseFieldDataType.Checkbox:
            content = <Checkbox
                checked={!!rowValue}
            />
            break;
        case DatabaseFieldDataType.Linked:
            content = <RenderPureLinked
                refDatabaseId={props.item.record.databaseId}
                column={column as LinkedColumn}
                row={props.item}
                databaseErrorStore={databaseErrorStore}
                databaseStore={databaseStore}
            />
            break;
        case DatabaseFieldDataType.Date:
        case DatabaseFieldDataType.CreatedAt:
        case DatabaseFieldDataType.UpdatedAt:
            content = <RenderPureDate
                column={column as (DateColumn | CreatedAtColumn | UpdatedAtColumn)}
                className='text-xs font-medium'
                row={props.item}
            />
            break;
        case DatabaseFieldDataType.CreatedBy:
        case DatabaseFieldDataType.UpdatedBy:
        case DatabaseFieldDataType.Person:
            content = <RenderPurePerson
                column={column}
                row={props.item}
                members={members}
            />
            break;
        case DatabaseFieldDataType.Files:
            let rowVal = rowValue as (FilesColumnDbValue | undefined)
            let files = rowVal && Array.isArray(rowVal) ? rowVal : []
            content = <div className='font-medium truncate text-xs'>{files.length} file(s)</div>
            break;


    }
    if (!content) return null;


    return (<div className='flex gap-2 items-center w-full overflow-hidden'>
        <DatabaseFieldTypeIcon type={column.type} className='col-3'/>
        <div className='flex-1 overflow-hidden'>
            {content}
        </div>
    </div>)
}