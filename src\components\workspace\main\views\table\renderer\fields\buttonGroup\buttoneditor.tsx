"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ButtonAction, ActionButton } from "opendb-app-db-utils/lib/typings/db";
import { PlusIcon } from "@heroicons/react/24/outline";
import { generateUUID } from "opendb-app-db-utils/lib/methods/string";
import { useWorkspace } from "@/providers/workspace";
import { useAuth } from "@/providers/user";
import { DNDSortable, SortItem } from "@/components/custom-ui/dndSortable";
import { getDefaultPropsForActionType } from "@/utils/buttonActionHelpers";
import { DbCondition } from "opendb-app-db-utils/lib/typings/db";
import { CompareOperator } from "opendb-app-db-utils/lib/methods/compare";
import { ViewFilter } from "@/components/workspace/main/views/common/viewFilter";
import { Db<PERSON><PERSON>ordFilter, Match } from "opendb-app-db-utils/lib/typings/db";
import { getWorkspaceSenders } from "@/api/workspace";
import { WorkspaceSenderEmail } from "@/typings/workspace";
import { getWorkflows } from "@/api/workflow";
import { Workflow, WorkflowTriggerType } from "@/typings/workflow";
import { InputWithEnter } from "@/components/custom-ui/inputWithEnter";
import { ActionConfigEditor, ConditionEditor } from "@/components/workspace/main/views/table/renderer/fields/buttonGroup/Editor";

export interface ButtonEditorProps {
    initialValue?: ActionButton;
    button?: ActionButton;
    onSave: (value: ActionButton) => void;
    onUpdate?: (value: ActionButton) => void;
    onCancel: () => void;
    databaseId: string;
    contextIsRecord?: boolean;
    saveTriggered?: boolean;
}

export const ButtonEditor = ({
    initialValue,
    button,
    onSave,
    onUpdate,
    onCancel,
    databaseId,
    contextIsRecord = false,
    saveTriggered,
}: ButtonEditorProps) => {
    const buttonData = initialValue || button;
    const saveHandler = onSave || onUpdate;
    const { databaseStore, workspace } = useWorkspace();
    const { token } = useAuth();

    const [label, setLabel] = useState<string>(buttonData?.label || "");
    const [actions, setActions] = useState<ButtonAction[]>(buttonData?.actions || []);
    const [visibleIfConditions, setVisibleIfConditions] = useState<DbCondition[]>((buttonData as { visibleIf?: DbCondition[]; visibleIfConditions?: DbCondition[] })?.visibleIf || (buttonData as { visibleIf?: DbCondition[]; visibleIfConditions?: DbCondition[] })?.visibleIfConditions || []);
    const [enabledIfConditions, setEnabledIfConditions] = useState<DbCondition[]>((buttonData as { enabledIf?: DbCondition[]; enabledIfConditions?: DbCondition[] })?.enabledIf || (buttonData as { enabledIf?: DbCondition[]; enabledIfConditions?: DbCondition[] })?.enabledIfConditions || []);
    const [visibleIfFilter, setVisibleIfFilter] = useState<DbRecordFilter>((buttonData as { visibleIfFilter?: DbRecordFilter })?.visibleIfFilter || { conditions: [], match: Match.All });
    const [enabledIfFilter, setEnabledIfFilter] = useState<DbRecordFilter>((buttonData as { enabledIfFilter?: DbRecordFilter })?.enabledIfFilter || { conditions: [], match: Match.All });
    const [workspaceSenders, setWorkspaceSenders] = useState<WorkspaceSenderEmail[]>([]);
    const [workflows, setWorkflows] = useState<Workflow[]>([]);

    // Load workspace senders
    useEffect(() => {
        const loadSenders = async () => {
            if (!token || !workspace) return;
            try {
                const response = await getWorkspaceSenders(token.token, workspace.workspace.id);
                if (response.data?.data?.senders) {
                    setWorkspaceSenders(response.data.data.senders);
                }
            } catch (error) {
                console.error('Failed to load workspace senders:', error);
            }
        };
        loadSenders();
    }, [token, workspace]);

    // Load workflows
    useEffect(() => {
        const loadWorkflows = async () => {
            if (!token || !workspace) return;
            try {
                const response = await getWorkflows(token.token, workspace.workspace.id, {
                    triggerTypes: [WorkflowTriggerType.OnDemand_Callable]
                });
                if (response.data?.data?.workflows) {
                    setWorkflows(response.data.data.workflows);
                }
            } catch (error) {
                console.error('Failed to load workflows:', error);
            }
        };
        loadWorkflows();
    }, [token, workspace]);

    const handleSave = () => {
        if (!saveHandler) return;
        const finalButton = {
            ...(buttonData as ActionButton),
            id: buttonData?.id || generateUUID(),
            label,
            actions,
            visibleIf: visibleIfConditions,
            enabledIf: enabledIfConditions,
            visibleIfFilter: visibleIfFilter,
            enabledIfFilter: enabledIfFilter,
            isReady: true
        } as ActionButton;
        saveHandler(finalButton);
    };

    // Listen for save requests from parent
    useEffect(() => {
        if (saveTriggered) {
            handleSave();
        }
    }, [saveTriggered]);


    // Create variable key map for mention inputs
    const variableKeyMap: Record<string, { label: string; description: string; tag: string }> = {};
    if (databaseId && databaseStore?.[databaseId]?.database?.definition?.columnsMap) {
        const columnsMap = databaseStore[databaseId].database.definition.columnsMap;
        Object.entries(columnsMap).forEach(([columnId, column]) => {
            variableKeyMap[`column.${columnId}`] = {
                label: `Column: ${column.title}`,
                description: `Column value for ${column.title}`,
                tag: `{{column.${columnId}}}`
            };
        });
    }

    variableKeyMap['person.current'] = { label: 'Current User', description: 'Current user ID', tag: '{{person.current}}' };
    variableKeyMap['person.email'] = { label: 'Current User Email', description: 'Current user email address', tag: '{{person.email}}' };
    variableKeyMap['person.name'] = { label: 'Current User Name', description: 'Current user name', tag: '{{person.name}}' };

    const addAction = () => {
        const newAction: ButtonAction = {
            id: generateUUID(),
            label: "New Action",
            isReady: true,
            actionType: "openUrl",
            props: getDefaultPropsForActionType("openUrl") as unknown as ButtonAction['props']
        };
        setActions([...actions, newAction]);
    };

    const updateAction = (index: number, updatedAction: ButtonAction) => {
        const newActions = [...actions];
        newActions[index] = updatedAction;
        setActions(newActions);
    };

    const deleteAction = (index: number) => {
        const newActions = [...actions];
        newActions.splice(index, 1);
        setActions(newActions);
    };

    const reorderActions = (items: SortItem<ButtonAction>[]) => {
        setActions(items.map(item => item.data));
    };

    return (
        <div className="max-w-[95vw] sm:max-w-[600px] w-full mx-auto">
            <div className="space-y-4 p-4 overflow-visible">
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label className="text-sm font-medium">Button Label</Label>
                        <InputWithEnter
                            value={label}
                            onChange={(value) => setLabel(value)}
                            placeHolder="New Button"
                            wrapperClassname="text-sm rounded-full"
                            shortEnter={true}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label className="text-sm font-medium">When</Label>
                        <div className="p-3 border rounded-md bg-gray-50">
                            <span className="text-sm text-gray-700">Button is clicked</span>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label className="text-sm font-medium">Do</Label>

                    {actions.length > 0 ? (
                        <DNDSortable
                            items={actions.map(action => ({ id: action.id, data: action }))}
                            itemRenderer={(index, item) => (
                                <ActionConfigEditor
                                    action={item.data}
                                    databaseId={databaseId}
                                    workspaceSenders={workspaceSenders}
                                    workflows={workflows}
                                    onUpdate={(updatedAction) => updateAction(index, updatedAction)}
                                    onDelete={() => deleteAction(index)}
                                />
                            )}
                            onChange={reorderActions}
                            useDragHandle
                            handlePosition="center"
                        />
                    ) : (
                        <div className="text-gray-400 text-center py-8 text-sm">
                            No actions configured
                        </div>
                    )}

                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={addAction}
                        className="text-xs mt-4 w-full text-gray-600 hover:text-gray-800"
                    >
                        <PlusIcon className="h-4 w-4 mr-1" />
                        <span>New action</span>
                    </Button>
                </div>

                <div className="border-t pt-4">
                    <h3 className="text-sm font-semibold mb-4">Conditions</h3>
                    <div className="mb-4">
                        <Label className="text-xs text-gray-600 mb-2 block">Show this button when:</Label>
                        <div className="space-y-2">
                            {visibleIfConditions.map((condition, index) => (
                                <ConditionEditor
                                    key={index}
                                    condition={condition}
                                    databaseId={databaseId}
                                    variableKeyMap={variableKeyMap}
                                    onChange={(updatedCondition) => {
                                        const newConditions = [...visibleIfConditions];
                                        newConditions[index] = updatedCondition;
                                        setVisibleIfConditions(newConditions);
                                    }}
                                    onDelete={() => {
                                        const newConditions = [...visibleIfConditions];
                                        newConditions.splice(index, 1);
                                        setVisibleIfConditions(newConditions);
                                    }}
                                />
                            ))}
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setVisibleIfConditions([...visibleIfConditions, { columnId: '', op: CompareOperator.Equals, value: '' }])}
                                className="text-xs rounded-full w-full text-gray-600 hover:text-gray-800"
                            >
                                + Add visibility condition
                            </Button>

                            {/* Add database filter option for record context */}
                            {contextIsRecord && (
                                <div className="mt-3 pt-3 border-t border-gray-200">
                                    <Label className="text-xs text-gray-600 mb-2 block">And when record matches the filter:</Label>
                                    <ViewFilter
                                        database={databaseStore?.[databaseId]?.database}
                                        filter={visibleIfFilter}
                                        onChange={setVisibleIfFilter}
                                        trigger={
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-xs rounded-full w-full text-gray-600 hover:text-gray-800"
                                            >
                                                {visibleIfFilter.conditions.length > 0
                                                    ? `${visibleIfFilter.conditions.length} filter${visibleIfFilter.conditions.length > 1 ? 's' : ''} applied`
                                                    : '+ Add database filter'
                                                }
                                            </Button>
                                        }
                                        tagOptionsMap={variableKeyMap}
                                    />
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="mb-4">
                        <Label className="text-xs text-gray-600 mb-2 block">Enable this button when:</Label>
                        <div className="space-y-2">
                            {enabledIfConditions.map((condition, index) => (
                                <ConditionEditor
                                    key={index}
                                    condition={condition}
                                    databaseId={databaseId}
                                    variableKeyMap={variableKeyMap}
                                    onChange={(updatedCondition) => {
                                        const newConditions = [...enabledIfConditions];
                                        newConditions[index] = updatedCondition;
                                        setEnabledIfConditions(newConditions);
                                    }}
                                    onDelete={() => {
                                        const newConditions = [...enabledIfConditions];
                                        newConditions.splice(index, 1);
                                        setEnabledIfConditions(newConditions);
                                    }}
                                />
                            ))}
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setEnabledIfConditions([...enabledIfConditions, { columnId: '', op: CompareOperator.Equals, value: '' }])}
                                className="text-xs rounded-full w-full text-gray-600 hover:text-gray-800"
                            >
                                + Add enabled condition
                            </Button>

                            {/* Add database filter option for record context */}
                            {contextIsRecord && (
                                <div className="mt-3 pt-3 border-t border-gray-200">
                                    <Label className="text-xs text-gray-600 mb-2 block">And when record matches the filter:</Label>
                                    <ViewFilter
                                        database={databaseStore?.[databaseId]?.database}
                                        filter={enabledIfFilter}
                                        onChange={setEnabledIfFilter}
                                        trigger={
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-xs rounded-full w-full text-gray-600 hover:text-gray-800"
                                            >
                                                {enabledIfFilter.conditions.length > 0
                                                    ? `${enabledIfFilter.conditions.length} filter${enabledIfFilter.conditions.length > 1 ? 's' : ''} applied`
                                                    : '+ Add database filter'
                                                }
                                            </Button>
                                        }
                                        tagOptionsMap={variableKeyMap}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
