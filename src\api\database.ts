import {apiUrl, BackendAPIResponse, normalizeResponse} from "@/api/common";
import {httpRequest} from "@/utils/http";
import {Database, PermissibleDatabaseWithPermissions, RecordMatchMap} from "@/typings/database";
import { DocumentNotificationType, Visibility } from "@/typings/page";
import {DatabaseDefinition, RecordValues} from "opendb-app-db-utils/lib/typings/db";
import { UpdateDocumentNotificationParams } from "./workspace";

export const getDatabases = async (token: string, workspaceId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        databases: PermissibleDatabaseWithPermissions[];
    }>;
};


export const getDatabase = async (token: string, workspaceId: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases/${id}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        database: PermissibleDatabaseWithPermissions;
    }>;
};

export const createDatabase = async (token: string, workspaceId: string, body: {
    name: string,
    visibility: Visibility,
    definition?: DatabaseDefinition
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        database: PermissibleDatabaseWithPermissions;
    }>;
};

export const matchDatabaseRecords = async (token: string, workspaceId: string, id: string, data: {
    matchByColId: string
    matchExisting?: boolean
    createMissing?: boolean
    values: RecordValues[]
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases/${id}/match-records`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{
        matchMap: RecordMatchMap;
    }>;
};

export const activateMessaging = async (token: string, workspaceId: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases/${id}/activate-messaging`;
    const response = await httpRequest("post", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

interface UpdateRecordNotificationParams {
    id: string,
    notification: DocumentNotificationType
}
export const updateRecordNotification = async (token: string, workspaceId: string, databaseId: string, params: UpdateRecordNotificationParams) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases/${databaseId}/records/${params.id}/notification-settings`;
    const response = await httpRequest("post", endpoint, headers, params);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};


export const setTitleFormat = async (token: string, workspaceId: string, databaseId: string, titleFormat: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases/${databaseId}/title-format`;
    const response = await httpRequest("post", endpoint, headers, { titleFormat });
    return normalizeResponse(response) as BackendAPIResponse<{
        database: Database;
    }>;
};




