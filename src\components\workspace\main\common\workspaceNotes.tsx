import {<PERSON><PERSON><PERSON><PERSON>} from "@/components/ui/scroll-area";
import {<PERSON><PERSON>} from "@/components/ui/button";
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import {Dialog, DialogContent, DialogHeader, DialogTitle} from "@/components/ui/dialog";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import {YJSDoc} from "@/components/workspace/main/common/YJSDoc";
import {useWorkspace} from "@/providers/workspace";
import {useAuth} from "@/providers/user";
import {ItemDataLoad} from "@/api/common";
import {MyWorkspaceMember, WorkspaceNote} from "@/typings/workspace";
import { createNote, deleteNote, getNotes, GetNotesParams, ShareNote, updateNote } from "@/api/workspace";
import {PageLoader} from "@/components/custom-ui/loader";
import {useAlert} from "@/providers/alert";
import { EllipsisVerticalIcon, NoteIcon, RectangleHistoryIcon, CommentIcon, MessageLinesIcon } from "@/components/icons/FontAwesomeRegular";
import {getDatabaseTitleCol, getRecordTitle} from "@/components/workspace/main/views/form/components/element/linked";
import {ViewWarning, WarningMessage} from "@/components/workspace/main/views/common/contentLocked";
import {timeAgo} from "@/utils/timeAgo";
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {useWorkspaceSocket} from "@/providers/workspaceSocket";
import {DocumentHistoryModal} from "@/components/workspace/main/common/documentHistory";
import {useSearchParams, useRouter, usePathname} from "next/navigation";
import { ShareView } from "../views/common/shareView";
import ShareWrapper from "@/components/custom-ui/share";
import { AccessLevel } from "@/typings/page";
import ShareNotePermission from "./notePermission";
import CommentSidebarModal from "./comment/commentSidebarModal";
import { CommentProvider } from "@/providers/comment";
import { RiMessageLine } from "react-icons/ri"
import { FaRegMessage } from "react-icons/fa6";

export interface WorkspaceNotesProps {
    recordId?: string | null
    databaseId?: string | null
    hideTitle?: boolean
}

export interface NotesRef {
    createNewNote: () => void;
}

const WorkspaceNotesInner = (props: WorkspaceNotesProps, ref: React.Ref<NotesRef>) => {
    const {workspace, membersMap} = useWorkspace()
    const {token} = useAuth()
    const {toast} = useAlert()

    const perPage = 24
    const [page, setPage] = useState(1)
    const [load, setLoad] = useState<ItemDataLoad<{ notes: WorkspaceNote[] }>>({})
    const [hasMore, setHasMore] = useState(true)
    const [isCreating, setIsCreating] = useState(false)

    const [focusNote, setFocusNote] = useState<WorkspaceNote>()

    const loadNotes = async (page = 1) => {
        if (!token) return
        setLoad({isLoading: true, error: undefined})

        const params: GetNotesParams = {
            perPage,
            page
        }
        if (props.recordId && props.databaseId) {
            params.type = 'record'
            params.recordId = props.recordId
            params.databaseId = props.databaseId
        } else {
            params.type = 'user'
        }
        const res = await getNotes(token.token, workspace.workspace.id, params)
        if (res.error) {
            setLoad({isLoading: false, error: res.error})
            if (page > 1) toast.error(res.error)
            return
        }
        const oldData = load?.data || {notes: []}
        if (page === 1) oldData.notes = []
        setLoad({isLoading: false, data: {notes: [...oldData.notes, ...res.data.data.notes]}})
        setPage(page)

        if (res.data.data.notes.length === 0 || (page === 1 && res.data.data.notes.length < perPage)) setHasMore(false)
    }

    const loadMore = () => {
        loadNotes(page + 1)
    }

    const newNote = async () => {
        if (isCreating || !token) return
        const {recordId, databaseId} = props
        const res = await createNote(token.token, workspace.workspace.id, {recordId, databaseId})
        setIsCreating(false)
        if (res.error) {
            toast.error(res.error)
            return
        }
        setLoad({data: {notes: [res.data.data.note, ...(load.data?.notes || [])]}})
        setFocusNote(res.data.data.note)
    }

    const router = useRouter();
    const pathname = usePathname();

    const finishEdit = (note: WorkspaceNote) => {
        setIsClosing(true);

        if (!load.data) return
        const notes = [...load.data.notes]

        for (let i = 0; i < notes.length; i++) {
            let note1 = load.data.notes[i];
            if (note1.document.id === note.document.id) {
                notes[i] = note
                break
            }
        }
        setLoad({data: {notes}})
        setFocusNote(undefined)

        if (noteIdParam) {
            const params = new URLSearchParams();
            searchParams.forEach((value, key) => {
                if (key !== 'noteId') {
                    params.append(key, value);
                }
            });

            const newQuery = params.toString();
            const newPath = pathname + (newQuery ? `?${newQuery}` : '');

            router.replace(newPath, { scroll: false });

            setTimeout(() => {
                setIsClosing(false);
            }, 300);
        } else {
            setIsClosing(false);
        }
    }

    const onDelete = (id: string) => {
        setIsClosing(true);

        if (!load.data) return
        const notes = [...load.data.notes].filter(n => n.document.id !== id)
        setLoad({data: {notes}})
        setFocusNote(undefined)

        if (noteIdParam && noteIdParam === id) {
            const params = new URLSearchParams(); 
            searchParams.forEach((value, key) => {
                if (key !== 'noteId') {
                    params.append(key, value);
                }
            });

            const newQuery = params.toString();
            const newPath = pathname + (newQuery ? `?${newQuery}` : '');

            router.replace(newPath, { scroll: false });

            setTimeout(() => {
                setIsClosing(false);
            }, 300);
        } else {
            setIsClosing(false);
        }
    }
    useImperativeHandle(ref, () => ({
        createNewNote: newNote,
    }));

    const {socket, isConnected} = useWorkspaceSocket()

    const loadRef = useRef(load)
    loadRef.current = load
    useEffect(() => {
        if (!socket || !isConnected) return

        socket.on("note", (d: {
            note: WorkspaceNote
        }) => {
            console.log("New note callback", d)
            if (!loadRef.current.data) return
            if (d.note.document.recordId !== props.recordId || d.note.document.databaseId !== props.databaseId) return

            const notes = [...(loadRef.current.data?.notes || [])]
            const index = notes.findIndex(r => r.document.id === d.note.document.id)
            if (index === -1) {
                notes.unshift(d.note)
            } else {
                notes[index] = d.note
            }
            setLoad({...loadRef.current, data: {notes}})
        })

        socket.on("note-deleted", (d: {
            note: WorkspaceNote
        }) => {
            console.log("Deleted note callback", d)
            if (!loadRef.current.data) return
            if (d.note.document.recordId !== props.recordId || d.note.document.databaseId !== props.databaseId) return

            const notes = [...(loadRef.current.data?.notes || [])].filter(r => r.document.id !== d.note.document.id)
            setLoad({...loadRef.current, data: {notes}})
        })

        console.log("Notes listener defined")
        return () => {
            if (!socket) return
            socket.off('note')
            socket.off('note-deleted')

            console.log("Notes listener cleared")

        }
    }, [isConnected, socket])

    const searchParams = useSearchParams();
    const noteIdParam = searchParams.get('noteId');

    useEffect(() => {
        setLoad({data: undefined})
        loadNotes(1).then()
    }, []);

    const [isClosing, setIsClosing] = useState(false);

    useEffect(() => {
        if (noteIdParam && load.data && !isClosing && !focusNote) {
            const noteToOpen = load.data.notes.find(note => note.document.id === noteIdParam);
            if (noteToOpen) {
                setFocusNote(noteToOpen);
            }
        }
    }, [noteIdParam, load.data, isClosing, focusNote]);
    return <>

        <div className="h-full w-full flex flex-col">
            {!props.hideTitle && <div className="flex p-4 gap-2">
                <h2 className="font-semibold flex-1">Notes</h2>
                {load.data && <Button
                    disabled={isCreating}
                    onClick={newNote}
                    className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                    New note
                </Button>}
            </div>
            }
            <div className="flex-1 overflow-hidden">
                {page === 1 && (load.isLoading || load.error || !load.data) && <>
                    <PageLoader
                        error={load.error}
                        size='full'
                        cta={load.error ? {'label': 'Retry', onClick: loadNotes} : undefined}/>
                </>}
                {load.data && load.data.notes.length === 0 && <>
                    <PageLoader
                        error={"It's empty here"}
                        size='full'
                        icon={<NoteIcon className="size-12"/>}
                        cta={{'label': 'Create Note', onClick: newNote}}/>
                </>}
                {load.data && load.data.notes.length > 0 && <>
                    <ScrollArea className="w-full h-full">
                        <div className="p-4 pb-12 pt-0">
                            <div className='grid grid-cols-2 md:grid-cols-3 gap-4'>
                                {load.data.notes.map((n, i) => {
                                    return <NoteRender
                                        workspaceId={workspace.workspace.id}
                                        onDelete={() => onDelete(n.document.id)}
                                        n={n}
                                        onClick={() => setFocusNote(n)}
                                        membersMap={membersMap} key={n.document.id}/>
                                })}
                            </div>

                            {hasMore && <>
                                <div className='flex justify-center my-8 pb-16'>
                                    <Button
                                        variant="link"
                                        disabled={load.isLoading}
                                        onClick={loadMore}
                                        className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                                        {load.isLoading ? 'Loading...' : 'Load More'}
                                    </Button>
                                </div>
                            </>}

                        </div>
                    </ScrollArea>
                </>}
            </div>
            {focusNote && <EditNote
                close={finishEdit}
                membersMap={membersMap}
                note={focusNote}/>}
        </div>
    </>
}

export const WorkspaceNotes = React.forwardRef<NotesRef, WorkspaceNotesProps>(WorkspaceNotesInner);

const NoteRender = ({n, onClick, membersMap, onDelete, workspaceId}: { n: WorkspaceNote, onClick: () => void, membersMap: { [p: string]: MyWorkspaceMember }, onDelete?: () => void, workspaceId: string }) => {
    const {confirm, toast} = useAlert()
    const { token, user } = useAuth()
    let recordTitle = ''
    let recordImage = ''
    if (n.database && n.record) {
        const {defaultTitle, titleColId, isContacts} = getDatabaseTitleCol(n.database)
                        recordTitle = getRecordTitle(n.record, titleColId, defaultTitle, isContacts, n.database)

    }
    const member: MyWorkspaceMember | undefined = membersMap[n.document.createdById]
    const memberName = `${member?.user?.firstName || ''} ${member?.user?.lastName || ''}`.trim() || 'Unknown member'

    const [isDeleting, setIsDeleting] = useState(false)

    const doDelete = async () => {
        if (isDeleting || !token) return
        setIsDeleting(true)
        const res = await deleteNote(token.token, workspaceId, {id: n.document.id})
        setIsDeleting(false)
        if (res.error) {
            toast.error("Error deleting note:" + res.error)
            return
        }
        onDelete?.()
    }


    const confirmDelete = () => {
        confirm("Delete note?", "This cannot be reversed", async () => {
            doDelete().then()
        })
    }



    return <>
        <div onClick={onClick} className='overflow-hidden p-3 border border-neutral-200 flex flex-col gap-2 hover:border-black transition-all relative select-none'>
            <div className='flex gap-2'>
                {n.record && <>
                    <Button className='!p-0.5 gap-2 text-xs text-[10px] !h-auto hover:bg-neutral-200 truncate' variant='ghost'>
                        <Avatar className="size-4">
                            <AvatarImage className="size-full" src={recordImage}/>
                            <AvatarFallback className='text-xs text-[10px]'>{(recordTitle || 'Untitled')[0]}</AvatarFallback>
                        </Avatar>
                        <span className='text-xs text-[10px] font-semibold underline decoration-dashed underline-offset-4 decoration-neutral-300'>{recordTitle}</span>
                    </Button>
                </>}
                <div className='flex-1'></div>
            </div>
            {user?.id === n.document.createdById && <div className='absolute right-2 top-2'>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="rounded-full h-auto p-1.5 size-6 text-xs gap-2">
                            <EllipsisVerticalIcon className="size-full"/>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-28 rounded-none text-neutral-800 font-semibold"
                        align="end">
                        <DropdownMenuItem
                            disabled={isDeleting}
                            onClick={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                                confirmDelete()
                            }} className="text-xs rounded-none p-2 flex gap-2">
                            Delete
                        </DropdownMenuItem>

                    </DropdownMenuContent>
                </DropdownMenu>
            </div>}


            <div className='text-xs font-semibold truncate'>{n.document.name || 'Untitled'}</div>
            <div className='text-xs text-muted-foreground font-medium text-[10px] h-16 overflow-hidden'>
                {n.document.contentText}
            </div>
            <div className='flex gap-2 text-xs text-muted-foreground font-medium text-[10px] border-t pt-2'>
                <div className='flex-1'>
                    <div className='!p-0 gap-2 text-xs text-[10px] flex !h-auto'>
                        <Avatar className="size-4">
                            <AvatarImage className="size-full" src={member?.user?.profilePhoto}/>
                            <AvatarFallback className='text-xs text-[10px]'>{memberName[0]}</AvatarFallback>
                        </Avatar>
                        <span className='text-xs text-[10px]'>{memberName}</span>
                    </div>
                </div>
                <div>
                    Edited {timeAgo(new Date(n.document.createdAt))}
                </div>
            </div>
        </div>
    </>
}

interface EditNoteProps {
    close: (note: WorkspaceNote) => void
    note: WorkspaceNote 
    membersMap: { [p: string]: MyWorkspaceMember }
}

const EditNote = (props: EditNoteProps) => {
    const {token} = useAuth()
    const {workspace} = useWorkspace()
    const {toast} = useAlert()
    const [note, setNote] = useState<WorkspaceNote>(props.note)
    const [title, setTitle] = useState(props.note.document.name)
    const [isSaving, setIsSaving] = useState(false)
    const [editorReady, setIsEditorReady] = useState(false)
    const [connected, setConnected] = useState(false)
    const roomName = `n:${props.note.document.id}|${workspace.workspace.id}`

    let recordTitle = ''
    let recordImage = ''
    if (note.database && note.record) {
        const {defaultTitle, titleColId, isContacts} = getDatabaseTitleCol(note.database)
                    recordTitle = getRecordTitle(note.record, titleColId, defaultTitle, isContacts, note.database)
    }

    const updateText = (text: string) => {
        setNote({...note, document: {...note.document, contentText: text}})
    }

    const updateTitle = async () => {
        if (!token) return
        setIsSaving(true)
        const res = await updateNote(token.token, workspace.workspace.id, {id: note.document.id, name: title})
        setIsSaving(false)
        if (res.error) {
            toast.error('Error saving title: ' + res.error)
            return
        }
        setNote({...note, document: {...note.document, name: title, updatedAt: new Date().toISOString()}})
    }

    const contentJSON = props.note.document.contentJSON
    const initialContent = Array.isArray(contentJSON) ? contentJSON as any : []



    return (<>
        <Dialog open onOpenChange={() => props.close(note)}>
            <DialogContent className="max-w-[850px] h-3/4 !rounded-none p-4 overflow-hidden">
                <div className='size-full overflow-hidden flex flex-col gap-0.5 relative'>
                    <DialogHeader className='h-8'>
                        <DialogTitle className="font-bold text-xs flex gap-1 pr-4">
                            <div className='flex-1 overflow-hidden'>
                                {note.document.name || 'Untitled Note'}
                                {note.record && <>
                                    in
                                    <Button className='!p-0.5 gap-2 text-xs text-[10px] ml-2 !h-auto hover:bg-neutral-200 truncate' variant='ghost'>
                                        <Avatar className="size-4">
                                            <AvatarImage className="size-full" src={recordImage}/>
                                            <AvatarFallback className='text-xs text-[10px]'>{(recordTitle || 'Untitled')[0]}</AvatarFallback>
                                        </Avatar>
                                        <span className='text-xs text-[10px] font-semibold underline decoration-dashed underline-offset-4 decoration-neutral-300'>{recordTitle}</span>
                                    </Button>
                                </>}
                            </div>
                            <CommentSidebarModal
                                documentId={note.document.id}
                                databaseId={note.database?.id}
                                recordId={note.record?.id}
                                trigger={
                                    <Button variant="ghost" title='Version History' className='size-6 p-1.5 rounded-full mr-2 relative -top-1'>
                                        <FaRegMessage className='size-full' />
                                </Button>
                                }
                                triggerClassName="size-6 p-1.5 rounded-full mr-2 relative -top-1"
                                className="top-7"

                            />


                            <DocumentHistoryModal
                                documentId={note.document.id}
                                membersMap={props.membersMap}
                                workspace={workspace.workspace}>
                                <Button variant="ghost" title='Version History' className='size-6 p-1.5 rounded-full mr-2 relative -top-1'>
                                    <RectangleHistoryIcon className='size-full'/>
                                </Button>
                            </DocumentHistoryModal> 
                            <ShareNotePermission note={note} />
                        </DialogTitle>
                    </DialogHeader>
                    {editorReady && !connected && <ViewWarning message={WarningMessage.ConnectionLost}/>}
                    <div className="flex-1 flex flex-col gap-2 py-2 overflow-hidden">
                        <ScrollArea className="size-full scrollBlockChild">
                            <div className='px-1 w-full flex gap-1 items-center'>
                                <input
                                    onBlur={e => {
                                        const val = e.target.value.trim()
                                        if (val === note.document.name) return
                                        updateTitle()
                                    }}
                                    onChange={e => setTitle(e.target.value)}
                                    // readOnly={!!template}
                                    className='text-2xl p-2 h-auto lg:p-8 lg:px-12 lg:pb-4 lg:h-18 font-black text-black border-none outline-none flex-1' placeholder='Untitled'
                                    value={title}/>
                            </div>
                            <YJSDoc
                                documentId={`WorkspaceNotes:${props.note.document.id}`}
                                roomName={roomName}
                                className={"min-h-[calc(100%-100px)]"}
                                initialContent={initialContent}
                                onEditorReady={() => setIsEditorReady(true)}
                                onConnectionStatusChanged={setConnected}
                                onChange={updateText}
                                collaborationEnabled={true} />
                        </ScrollArea>

                    </div>
                </div>
            </DialogContent>
        </Dialog>
    </>)
}




