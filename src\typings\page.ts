import {TableViewDefinition, ViewDefinition, ViewType, CalendarViewDefinition, ListViewDefinition} from "opendb-app-db-utils/lib/typings/view";
import {Emoji, Icon} from "opendb-app-db-utils/lib/typings/common";
import { Record } from "@/typings/database";
import { ProcessedDbRecord } from "opendb-app-db-utils/lib/typings/db";

export enum AccessLevel {
    View = "view",
    Edit = "edit",
    Full = "full",

}

export enum Visibility {
    Private = "private",
    Open = "open",
}

export type PickedIcon = Icon | Emoji;

export interface Page {
    id: string
    name: string
    icon?: PickedIcon
    workspaceId: string
    databaseId: string
    slug: string
    visibility: Visibility
    accessLevel: AccessLevel
    viewsOrder?: string[]
    ownerId: string
    createdById: string
    createdAt: string
    updatedAt: string
    deletedAt: string
}

export interface PermissiblePage {
    page: Page
    views: View[]
    accessLevel?: AccessLevel
}

export interface PermissiblePageWithPermissions extends PermissiblePage {
    permissions: PagePermission[]
}

export interface View {
    id: string
    name: string
    description: string
    pageId: string
    slug: string
    type: ViewType
    definition: ViewDefinition | TableViewDefinition
    isPublished: boolean
    allowSearchEngineIndex: boolean
    publishedAt: string
    createdById: string
    createdAt: string
    updatedAt: string
    deletedAt: string
}

export interface ViewRenderProps {
  view: View;
  definition: ViewDefinition;
}

export interface CalendarViewRenderProps extends ViewRenderProps {
  definition: CalendarViewDefinition;
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  record: Record;
  processedRecord: ProcessedDbRecord;
  description?: string;
  isMultiDay?: boolean;
  isAllDay?: boolean;
}

export type CalendarViewType = "day" | "week" | "month";

export interface ListViewRenderProps extends ViewRenderProps {
  definition: ListViewDefinition;
}

export interface DataViewRow {
  id: string;
  record: Record;
  processedRecord: ProcessedDbRecord;
  updatedAt: string;
}

export interface PagePermission {
    id: number
    workspaceId: string
    pageId?: string
    noteId?: string
    userId: string
    accessLevel: AccessLevel
    createdAt: string
    updatedAt: string
    deletedAt: string
}

export enum DocumentNotificationType {
    AllComments = "all_comments",
    RepliesAndMentions = "replies_and_mentions"
}
export interface UserNotificationSettings {
    [userId: string]: DocumentNotificationType
}

export interface Document {
    id: string
    workspaceId: string
    name: string
    contentJSON: object
    contentText: string
    viewId: string
    userNotificationSettings: UserNotificationSettings
    createdById: string
    updatedById: string
    createdAt: string
    updatedAt: string
    deletedAt: string
    databaseId?: string
    recordId?: string
    assignedToUserIds?: string
}
