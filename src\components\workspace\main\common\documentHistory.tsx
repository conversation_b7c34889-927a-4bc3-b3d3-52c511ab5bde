import React, {PropsWithChildren, useEffect, useRef, useState} from "react";
import {Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger} from "@/components/ui/dialog";
import {Button} from "@/components/ui/button";
import {LockIcon, RectangleHistoryIcon, XmarkIcon} from "@/components/icons/FontAwesomeRegular";
import {PageLoader} from "@/components/custom-ui/loader";
import {ScrollArea} from "@/components/ui/scroll-area";
import useAutoHeightTextarea from "@/components/custom-ui/autoHeightTextArea";
import {useAuth} from "@/providers/user";
import {useAlert} from "@/providers/alert";
import {DocumentHistory, MyWorkspaceMember, Workspace} from "@/typings/workspace";
import {ItemDataLoad} from "@/api/common";
import {getDocumentHistory, GetDocumentHistoryParams} from "@/api/workspace";
import {differenceInHours, format, isToday, isYesterday} from 'date-fns';
import {YJSDoc} from "@/components/workspace/main/common/YJSDoc";

export function getFormattedDate(date: Date): string {
    // if it's less than an hour, show ago time
    // else if it's today, show Today at time eg 10:49 am
    // else if it's yesterday, show Yesterday at them
    // else if it's this year, show Mon, Jan 2 at 10:49 am
    // else show Mon, Jan 2 2024 at 10:49am

    const now = new Date();

    // Calculate the difference in hours between now and the given date
    const hoursDifference = differenceInHours(now, date);

    if (hoursDifference < 1) {
        const minutesDifference = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
        return `${minutesDifference} minute${minutesDifference !== 1 ? 's' : ''} ago`;
    } else if (isToday(date)) {
        return `Today at ${format(date, 'h:mm a')}`;
    } else if (isYesterday(date)) {
        return `Yesterday at ${format(date, 'h:mm a')}`;
    } else if (date.getFullYear() === now.getFullYear()) {
        return `${format(date, 'EEE, MMM d')} at ${format(date, 'h:mm a')}`;
    } else {
        return `${format(date, 'EEE, MMM d yyyy')} at ${format(date, 'h:mm a')}`;
    }
}

export function isLocked(updatedAt: string, historyCutOffDays: number): boolean {
    const updatedAtDate = new Date(updatedAt);
    const today = new Date();
    const differenceInDays = Math.round((today.getTime() - updatedAtDate.getTime()) / (1000 * 3600 * 24));
    return differenceInDays > historyCutOffDays;
}

export const HistoryCutOffDays = 30;

interface DocumentHistoryModalProps {
    documentId: string
    workspace: Workspace,
    recordId?: string
    membersMap: {
        [key: string]: MyWorkspaceMember
    }
} 

export const DocumentHistoryModal = (props: PropsWithChildren<DocumentHistoryModalProps>) => {

    const {token} = useAuth()
    const autoHeightTextArea = useAutoHeightTextarea()


    const {toast} = useAlert()

    const perPage = 24
    const [page, setPage] = useState(1)
    const [load, setLoad] = useState<ItemDataLoad<{ histories: DocumentHistory[] }>>({})
    const [hasMore, setHasMore] = useState(true)
    const [initialContent, setInitialContent] = useState<any[]>([]);


    const loadRef = useRef(load)
    loadRef.current = load

    const loadNotes = async (page = 1) => {
        if (!token || loadRef.current.isLoading) return
        setLoad({ isLoading: true, error: undefined, ...load })

        const {workspace, documentId, recordId} = props
        const params: GetDocumentHistoryParams = {
            perPage,
            page,
            documentId,
            recordId
        }

        const res = await getDocumentHistory(token.token, workspace.id, params)
        if (res.error) {
            setLoad({isLoading: false, error: res.error})
            if (page > 1) toast.error(res.error)
            return
        }

        const oldData = load.data?.histories ? load.data.histories : [];
        const newHistories = page === 1
            ? res.data.data.histories
            : [...oldData, ...res.data.data.histories];

        setLoad({
            isLoading: false,
            data: { histories: newHistories }
        }); setPage(page)


        if (res.data.data.histories.length === 0 || res.data.data.histories.length < perPage) {
            setHasMore(false)
        } 
    }

    const loadMore = () => {
        loadNotes(page + 1)
    }



    useEffect(() => {
        setLoad({data: undefined})
        loadNotes(1).then()
    }, []);

    const [focus, setFocus] = useState<DocumentHistory | undefined>(undefined);

    useEffect(() => {
        if (load.data && load.data.histories.length > 0) {
            setFocus(load.data.histories[0]);
            const content = Array.isArray(load.data.histories[0].contentJSON) ? load.data.histories[0].contentJSON as any : [];
            setInitialContent(content);
        }
    }, [load.data]);

    const focusLocked = isLocked(focus?.updatedAt || '0', HistoryCutOffDays);


    const handleViewHistory = (v: DocumentHistory) => {
        setFocus(v);
        const content = Array.isArray(v.contentJSON) ? v.contentJSON as any[] : [];
        setInitialContent(content);
    }

    const [open, setOpen] = useState(false)

    const hideCloseBtn = !!(load.data && load.data.histories.length > 0)
    return <>
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                {props.children}
            </DialogTrigger>
            <DialogContent hideCloseBtn={hideCloseBtn} className="w-full md:max-w-[80%]  md:w-[80%] h-3/4 !rounded-none p-0">
                {page === 1 && initialContent.length < 1 && (load.isLoading || load.error) && (
                    <PageLoader
                        error={load.error}
                        size='full'
                        cta={load.error ? { label: 'Retry', onClick: () => loadNotes(1) } : undefined}
                    />
                )}
                {load.data && load.data.histories.length === 0 && <>
                    <PageLoader
                        error={"It's empty here"}
                        size='full'
                        icon={<RectangleHistoryIcon className="size-12"/>}/>
                </>}
                {initialContent && load.data && load.data.histories.length > 0 && <>
                    <div className='size-full overflow-hidden flex flex-row-reverse'>
                        <div className='w-72 border-l overflow-hidden h-full'>
                            <div className='size-full flex flex-col overflow-hidden'>
                                <DialogHeader className='gap-1 flex-row p-3 py-1.5 items-center'>
                                    <DialogTitle className="font-bold text-sm flex-1">
                                        Version History
                                    </DialogTitle>
                                    <Button
                                        onClick={() => setOpen(false)}
                                        variant="ghost" className='size-6 p-1.5 rounded-full -mr-2'>
                                        <XmarkIcon className='size-full'/>
                                    </Button>
                                </DialogHeader>
                                <div className='flex-1 overflow-hidden'>
                                    <div className='hidden'>
                                        <PageLoader size={"full"}/>
                                    </div>
                                    <ScrollArea className="size-full scrollBlockChild">
                                        <div className='flex flex-col gap-0 p-1.5 pr-3 pb-10'>
                                            {load.data.histories.map((v, i) => {
                                                const formattedDate = getFormattedDate(new Date(v.updatedAt))
                                                const member = props.membersMap[v.createdById || '']
                                                const name = `${member?.user.firstName || ''} ${member?.user.lastName || ''}`.trim() || 'Unknown member'
                                                const locked = isLocked(v.updatedAt, HistoryCutOffDays);
                                                return <Button variant='ghost'
                                                    onClick={() => handleViewHistory(v)}
                                                               className={`w-full text-left text-xs gap-2 rounded-none p-1.5 px-2 h-auto font-semibold ${focus?.id === v.id && 'bg-neutral-200'}`} key={v.id}>
                                                    <div className='flex-1 flex flex-col gap-0.5'>
                                                        <div className='text-xs text-black'>{formattedDate}</div>
                                                        <div className='text-xs text-[10px] text-muted-foreground'>{name}</div>
                                                    </div>
                                                    {locked && <LockIcon className='size-3'/>}
                                                </Button>
                                            })}
                                        </div>
                                        {hasMore && <>
                                            <div className='flex justify-center my-8 pb-16'>
                                                <Button
                                                    variant="link"
                                                    disabled={load.isLoading}
                                                    onClick={loadMore}
                                                    className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                                                    {load.isLoading ? 'Loading...' : 'Load More'}
                                                </Button>
                                            </div>
                                        </>}

                                    </ScrollArea>
                                </div>
                            </div>
                        </div>
                        <div className='flex-1 overflow-hidden flex flex-col gap-1 p-4'>
                            {!focus && <PageLoader
                                error="Nothing to show yet"
                                icon={<RectangleHistoryIcon className="size-12"/>}
                                size={"full"}/>}
                            {focus && <>
                                {focusLocked ? <>
                                    <PageLoader
                                        error={`Please upgrade your plan to access versions older than ${HistoryCutOffDays} days.`}
                                        cta={{label: 'Upgrade plan', href: `/${props.workspace.domain}/settings/plans`}}
                                        icon={<RectangleHistoryIcon className="size-12"/>}
                                        size={"full"}/>
                                </> : <>
                                     <div className='truncate font-semibold text-black text-xs'>
                                         {getFormattedDate(new Date(focus.updatedAt))}
                                     </div>
                                     <div className='flex-1 overflow-hidden'>
                                         <div className="size-full flex flex-col gap-2 py-2 overflow-hidden" key={focus.id}>
                                             <ScrollArea className="size-full scrollBlockChild">
                                                 {focus.name && <div className='px-1 w-full flex gap-1 items-center'>
                                                    <textarea
                                                        readOnly
                                                        ref={autoHeightTextArea}
                                                        className='text-2xl p-2 h-auto lg:p-8 lg:px-12 lg:pb-4 lg:h-18 font-black text-black border-none outline-none flex-1' placeholder='Untitled'
                                                        value={focus.name}/>
                                                 </div>}
                                                        <YJSDoc
                                                     documentId={`VersionHistory:${focus.id}`}
                                                     roomName={`history:${focus.id}`}
                                                     className={"min-h-[calc(100%-100px)]"}
                                                     initialContent={initialContent}
                                                        readonly
                                                        onToggleSidebar={() =>{}}
                                                        isSidebarOpen ={false}
                                                     // onEditorReady={() => setIsEditorReady(true)}
                                                     // onConnectionStatusChanged={setConnected}
                                                     // onChange={updateText}
                                                 />
                                             </ScrollArea>
                                         </div>
                                     </div>
                                 </>}
                            </>}
                        </div>
                    </div>
                </>}


            </DialogContent>
        </Dialog>
    </>
}