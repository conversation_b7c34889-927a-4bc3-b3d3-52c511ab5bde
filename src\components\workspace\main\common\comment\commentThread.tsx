"use client"

import { use<PERSON><PERSON>back, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { MessageSquare, Send, X, Check, MoreHorizontal, Trash2, ChevronDown, ChevronUp } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import type { Block } from "@blocknote/core"
// import type { Thread } from "@/typings/comment"
import type { MyWorkspaceMember } from "@/typings/workspace"
import type { User } from "@/typings/user"
import { Separator } from "@/components/ui/separator"
import { FloatingCommentPopup } from "./commentPopup"
import { useComments } from "@/providers/comment"
import { ThreadData } from "@blocknote/core/comments"
import { cn } from "@/lib/utils"

interface CommentThreadProps {
  thread: ThreadData
  user: User
  workspaceMembers: MyWorkspaceMember[],
  className?: string
  maxCommentsBeforeCollapse?: number
  isSelected?: boolean
  // onAddComment: (content: Block<any, any, any>[]) => void
  // onAddReply: (threadId: string, content: Block<any, any, any>[]) => void
  // onResolve: (threadId: string) => void
  // onDelete?: () => void
  // onCancel?: () => void
}



export function CommentThread({
  thread,
  user,
  workspaceMembers,
  className,
  maxCommentsBeforeCollapse = 3,
  isSelected = false
}: CommentThreadProps) {
  const {editor} = useComments()
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [showReplies, setShowReplies] = useState(false)

  // const comment = thread.comments.find((c) => thread.id === c.id)!
  const comment = thread.comments.find(c => c.metadata.parentId === null) || thread.comments[0]
  const replies = thread.comments.filter((c) => c.id !== comment.id)

  // Determine if replies should be collapsed
  const shouldCollapseReplies = replies.length > maxCommentsBeforeCollapse
  const visibleReplies = shouldCollapseReplies && !showReplies
    ? replies.slice(0, maxCommentsBeforeCollapse)
    : replies

  console.log({ thread })

  // if (!editor) {
  //   return null
  // }
  if (!editor?.comments) {
    throw new Error("Comments plugin not found");
  }

  const threadStore = editor.comments.threadStore;

  const onFocus = useCallback(
    (event: React.FocusEvent) => {
      // If the focused element is within the action toolbar, we don't want to
      // focus the thread for UX reasons.
      if ((event.target as HTMLElement).closest(".bn-action-toolbar")) {
        return;
      }

      if (editor && editor.comments) {
        editor.comments.selectThread(thread.id);
      }
    },
    [editor.comments, thread.id],
  );

  const onBlur = useCallback(
    (event: React.FocusEvent) => {
      // If the focused element is within the action toolbar, we don't want to
      // blur the thread for UX reasons.
      if (
        !event.relatedTarget ||
        event.relatedTarget.closest(".bn-action-toolbar")
      ) {
        return;
      }

      const targetElement =
        event.target instanceof Node ? event.target : null;
      const parentThreadElement =
        event.relatedTarget instanceof Node
          ? event.relatedTarget.closest(".bn-thread")
          : null;

      // When you focus the editor (reply composer), we don't want to unselect the thread
      // This check prevents that. But we still want to unselect the thread when it gets blurred in all other cases
      if (
        !targetElement ||
        !parentThreadElement ||
        !parentThreadElement.contains(targetElement)
      ) {
        if (editor && editor.comments) {
          editor.comments.selectThread(undefined);
        }
      }
    },
    [editor?.comments],
  );
  

  
  const handleSubmitReply = async (content: Block<any, any, any>[]) => {
    const cleanedContent = content.filter(k => Array.isArray(k.content) && k.content.length > 0)

       const newThread = await threadStore.addComment({
        comment: {
           body: cleanedContent,
           metadata: {
             parentId: comment.id,
             userId: user.id
           }
        },
        threadId: thread.id,
       });
    
        console.log({newThread,thread})


  }

  console.log("CommentThread", thread);

  const onEditSubmit = useCallback(
    async (content: Block<any, any, any>[]) => {
      const cleanedContent = content.filter(k => Array.isArray(k.content) && k.content.length > 0)
      await threadStore.updateComment({
        commentId: comment.id,
        comment: {
          body: cleanedContent,
          // metadata: {
          //   parentId: comment.metadata.parentId,
          // }
        },
        threadId: thread.id,
      });

      // setEditing(false);
    },
    [comment, thread.id, editor, threadStore],
  );

  const onAddReaction = useCallback(
    async (emoji: string, commentId: string) => {
      await threadStore.addReaction({
        commentId: commentId,
        threadId: thread.id,
        emoji
      });
    }, [comment, thread.id, threadStore]);

  const onDeleteReaction = useCallback(
    async (emoji: string, commentId: string) => {
      await threadStore.deleteReaction({
        commentId: commentId,
        threadId: thread.id,
        emoji
      });
    }, [comment, thread.id, threadStore]);

  const onDeleteThread = useCallback(async () => {
    await threadStore.deleteThread({
      threadId: thread.id,
    });
  }, [thread.id, threadStore]);

  const onDelete = useCallback(async () => {
    await threadStore.deleteComment({
      commentId: comment.id,
      threadId: thread.id,
    });
  }, [comment, thread.id, threadStore]);


  const onResolve = useCallback(async () => {
    if (thread.resolved) {
      await threadStore.unresolveThread({
        threadId: thread.id,
      });
    } else {
      await threadStore.resolveThread({
      threadId: thread.id,
    });
    }
  }, [thread.id, threadStore]);


  const onReopen = useCallback(async () => {
    await threadStore.unresolveThread({
      threadId: thread.id,
    });
  }, [thread.id, threadStore]);
  return (
    
    <div
      onFocus={onFocus}
      onBlur={onBlur}
      className={cn(`${className}`)}>
          {comment && (
            <FloatingCommentPopup
          comment={comment}
          commentThread={thread}
          currentUser={user}

          workspaceMembers={workspaceMembers}
          onAddReaction={onAddReaction}
          onDeleteReaction={onDeleteReaction}
          onEditComment={onEditSubmit}
              readonly
              onReply={(val) => setShowReplyForm(!showReplyForm)}
              onResolve={onResolve}
          onDelete={onDeleteThread}
          className="shadow-none border-none bg-transparent hover:bg-transparent active:bg-transparent focus:bg-transparent w-full"

            />
          )}

          

          {replies.length > 0 && (
            <>
          {shouldCollapseReplies && (
            <div className="">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs w-full justify-between"
                onClick={() => setShowReplies(!showReplies)}
              >
                <span>
                  {showReplies
                    ? "Hide replies"
                    : `Show ${replies.length - maxCommentsBeforeCollapse} more replies`
                  }
                </span>
                {showReplies ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
              </Button>
            </div>
          )}

          <div className="mt-2">
            {visibleReplies.map((reply) => (
              <FloatingCommentPopup
                comment={reply}
                currentUser={user}
                workspaceMembers={workspaceMembers}
                onAddReaction={onAddReaction}
                onDeleteReaction={onDeleteReaction}
                onEditComment={onEditSubmit}
                key={reply.id}
                readonly={true}
                onDelete={onDelete}
                className="shadow-none border-none bg-transparent hover:bg-transparent active:bg-transparent focus:bg-transparent"
              />
            ))}
          </div>

            </>
      )}
      {thread && !thread.resolved && (
        <div className="mt-1.5 relative">
              <span className="absolute -left-3 top-2 bg-background px-1 text-xs text-muted-foreground rounded">
              </span>
              <FloatingCommentPopup
          currentUser={user}
          workspaceMembers={workspaceMembers}
            onAddComment={handleSubmitReply}
            className="shadow-none border-none bg-transparent hover:bg-transparent active:bg-transparent focus:bg-transparent"
              />
            </div>
      )}
      <Separator className="my-2" />
            </div>
  )
}