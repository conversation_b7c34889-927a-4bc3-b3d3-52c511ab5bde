"use client"

import { use<PERSON><PERSON>back, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { MessageSquare, Send, X, Check, MoreHorizontal, Trash2, ChevronDown, ChevronUp } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import type { Block } from "@blocknote/core"
// import type { Thread } from "@/typings/comment"
import type { MyWorkspaceMember } from "@/typings/workspace"
import type { User } from "@/typings/user"
import { Separator } from "@/components/ui/separator"
import { FloatingCommentPopup } from "./commentPopup"
import { useComments } from "@/providers/comments"
import { ThreadData } from "@blocknote/core/comments"
import { cn } from "@/lib/utils"
import { Thread } from "@/typings/comment"

interface CommentThreadProps {
  thread: Thread
  user: User
  workspaceMembers: MyWorkspaceMember[],
  className?: string
  maxCommentsBeforeCollapse?: number
  isSelected?: boolean
}



export function CommentThread({
  thread,
  user,
  workspaceMembers,
  className,
  maxCommentsBeforeCollapse = 3,
  isSelected = false
}: CommentThreadProps) {
  // const { editor } = useComments()
  const { commentStore } = useComments()
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [showReplies, setShowReplies] = useState(false)

  // const comment = thread.comments.find((c) => thread.id === c.id)!
  const comment = thread.comments.find(c => c.parentId === null) || thread.comments[0]
  const replies = thread.comments.filter((c) => c.id !== comment.id)

  // Determine if replies should be collapsed
  const shouldCollapseReplies = replies.length > maxCommentsBeforeCollapse
  const visibleReplies = shouldCollapseReplies && !showReplies
    ? replies.slice(0, maxCommentsBeforeCollapse)
    : replies

  console.log("CommentThread", thread);





  // const onFocus = useCallback(
  //   (event: React.FocusEvent) => {
  //     // If the focused element is within the action toolbar, we don't want to
  //     // focus the thread for UX reasons.
  //     if ((event.target as HTMLElement).closest(".bn-action-toolbar")) {
  //       return;
  //     }

  //     if (editor && editor.comments) {
  //       editor.comments.selectThread(thread.id);
  //     }
  //   },
  //   [editor.comments, thread.id],
  // );

  // const onBlur = useCallback(
  //   (event: React.FocusEvent) => {
  //     // If the focused element is within the action toolbar, we don't want to
  //     // blur the thread for UX reasons.
  //     if (
  //       !event.relatedTarget ||
  //       event.relatedTarget.closest(".bn-action-toolbar")
  //     ) {
  //       return;
  //     }

  //     const targetElement =
  //       event.target instanceof Node ? event.target : null;
  //     const parentThreadElement =
  //       event.relatedTarget instanceof Node
  //         ? event.relatedTarget.closest(".bn-thread")
  //         : null;

  //     // When you focus the editor (reply composer), we don't want to unselect the thread
  //     // This check prevents that. But we still want to unselect the thread when it gets blurred in all other cases
  //     if (
  //       !targetElement ||
  //       !parentThreadElement ||
  //       !parentThreadElement.contains(targetElement)
  //     ) {
  //       if (editor && editor.comments) {
  //         editor.comments.selectThread(undefined);
  //       }
  //     }
  //   },
  //   [editor?.comments],
  // );
  

  
  const handleSubmitReply = async (content: Block<any, any, any>[]) => {
    const cleanedContent = content.filter(k => Array.isArray(k.content) && k.content.length > 0)

    const newThread = await commentStore.addComment({
      contentJson: cleanedContent,
      parentId: thread.id,
       });
    
        console.log({newThread,thread})


  }


  const onEditSubmit = useCallback(
    async (content: Block<any, any, any>[], commentId: string) => {
      const cleanedContent = content.filter(k => Array.isArray(k.content) && k.content.length > 0)
      await commentStore.updateComment({
        commentId,
        contentJson: cleanedContent
      });

      // setEditing(false);
    },
    [comment, thread.id, commentStore],
  );

  const onAddReaction = useCallback(
    async (emoji: string, commentId: string) => {
      await commentStore.addReaction({
        commentId: commentId,
        threadId: thread.id,
        emoji
      });
    }, [comment, thread.id, commentStore]);

  const onDeleteReaction = useCallback(
    async (emoji: string, commentId: string) => {
      await commentStore.deleteReaction({
        commentId: commentId,
        threadId: thread.id,
        emoji
      });
    }, [comment, thread.id, commentStore]);

  const onDeleteThread = useCallback(async () => {
    await commentStore.deleteThread({
      threadId: thread.id,
    });
  }, [thread.id, commentStore]);

  const onDelete = useCallback(async (commentId: string) => {
    await commentStore.deleteComment({
      commentId,
      threadId: thread.id,
    });
  }, [comment, thread.id, commentStore]);


  const onResolve = useCallback(async () => {
    await commentStore.resolveThread({
      threadId: thread.id,
    });
  }, [thread.id, commentStore]);

  const onReopen = useCallback(async () => {
    await commentStore.unresolveThread({
      threadId: thread.id,
    });
  }, [thread.id, commentStore]);



  return (
    
    <div
      // onFocus={onFocus}
      // onBlur={onBlur}
      className={cn(`${className}`)}>
          {comment && (
            <FloatingCommentPopup
          comment={comment}
          commentThread={thread}
          currentUser={user}

          workspaceMembers={workspaceMembers}
          onAddReaction={onAddReaction}
          onDeleteReaction={onDeleteReaction}
          onEditComment={(content) => onEditSubmit(content, comment.id)}
              readonly
              onReply={(val) => setShowReplyForm(!showReplyForm)}
          onResolve={onResolve}
          onOpen={onReopen}
          onDelete={onDeleteThread}
          className="shadow-none border-none bg-transparent hover:bg-transparent active:bg-transparent focus:bg-transparent w-full"

            />
          )}

          

          {replies.length > 0 && (
            <>
          {shouldCollapseReplies && (
            <div className="">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs w-full justify-between"
                onClick={() => setShowReplies(!showReplies)}
              >
                <span>
                  {showReplies
                    ? "Hide replies"
                    : `Show ${replies.length - maxCommentsBeforeCollapse} more replies`
                  }
                </span>
                {showReplies ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
              </Button>
            </div>
          )}

          <div className="mt-2">
            {visibleReplies.map((reply) => (
              <FloatingCommentPopup
                comment={reply}
                currentUser={user}
                workspaceMembers={workspaceMembers}
                onAddReaction={onAddReaction}
                onDeleteReaction={onDeleteReaction}
                onEditComment={(content) => onEditSubmit(content, reply.id)}
                key={reply.id}
                readonly={true}
                onDelete={() => onDelete(reply.id)}
                className="shadow-none border-none bg-transparent hover:bg-transparent active:bg-transparent focus:bg-transparent"
              />
            ))}
          </div>

            </>
      )}
      {thread && !thread.isResolved && (
        <div className="mt-1.5 relative">
              <span className="absolute -left-3 top-2 bg-background px-1 text-xs text-muted-foreground rounded">
              </span>
              <FloatingCommentPopup
          currentUser={user}
          workspaceMembers={workspaceMembers}
            onAddComment={handleSubmitReply}
            className="shadow-none border-none bg-transparent hover:bg-transparent active:bg-transparent focus:bg-transparent"
              />
            </div>
      )}
      <Separator className="my-2" />
            </div>
  )
}