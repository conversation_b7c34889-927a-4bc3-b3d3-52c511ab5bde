import { BlockNoteSchema, defaultBlockSpecs, defaultInlineContentSpecs, defaultStyleSpecs, createStyleSpecFromTipTapMark, } from "@blocknote/core";
import {createReactInlineContentSpec} from "@blocknote/react"
import{ Mark, mergeAttributes } from "@tiptap/core";

export const CommentMark = Mark.create({
  name: "comment",
  excludes: "",
  inclusive: false,
    keepOnSplit: true,
  // group: "",


  addAttributes() {
    // Return an object with attribute configuration
    return {
      // orphans are marks that currently don't have an active thread. It could be
      // that users have resolved the thread. Resolved threads by default are not shown in the document,
      // but we need to keep the mark (positioning) data so we can still "revive" it when the thread is unresolved
      // or we enter a "comments" view that includes resolved threads.
      orphan: {
        parseHTML: (element) => !!element.getAttribute("data-orphan"),
        renderHTML: (attributes) => {
          return (attributes as { orphan: boolean }).orphan
            ? {
                "data-orphan": "true",
              }
            : {};
        },
        default: false,
      },
      threadId: {
        parseHTML: (element) => element.getAttribute("data-bn-thread-id"),
        renderHTML: (attributes) => {
          return {
            "data-bn-thread-id": (attributes as { threadId: string }).threadId,
          };
        },
        default: "",
      },
    };
  },

  renderHTML({ HTMLAttributes }: { HTMLAttributes: Record<string, any> }) {
    return [
      "span",
      mergeAttributes(HTMLAttributes, {
        class: "bn-thread-mark",
      }),
    ];
  },

  parseHTML() {
    return [{ tag: "span.bn-thread-mark" }];
  },

  extendMarkSchema(extension) {
    if (extension.name === "comment") {
      return {
        blocknoteIgnore: true,
      };
    }
    return {};
  },
});


export const commentStyleSpec = createStyleSpecFromTipTapMark(
  CommentMark,
  "string"
);


export const Mention = createReactInlineContentSpec(
    {
        type: "mention",
        propSchema: {
            user: {
                default: "Unknown",
            },
            id: {
                default: "unknown-id",
            },
        },
        content: "none",
    } as const,
    {
        render: (props) => (
            <span className="bn-mention">
                <span className="text-muted-foreground">
                    <span className="text-muted-foreground">@</span>
                    {props.inlineContent.props.user}</span>
            </span>
        ),
    }
);


export function extractMentionUserIds(block: any): string[] {
    const userIds: string[] = [];
    
    // Check if block has content array (inline content)
    if (block.content && Array.isArray(block.content)) {
        block.content.forEach((contentItem: any) => {
            // Check if this is a mention inline content
            if (contentItem.type === 'mention' && contentItem.props && contentItem.props.id) {
                userIds.push(contentItem.props.id);
            }
            
            // Handle nested content (like in styled text)
            if (contentItem.content && Array.isArray(contentItem.content)) {
                userIds.push(...extractMentionUserIds({ content: contentItem.content }));
            }
        });
    }
    
    if (block.children && Array.isArray(block.children)) {
        block.children.forEach((child: any) => {
            userIds.push(...extractMentionUserIds(child));
        });
    }
    
    return userIds;
}

export function extractMentionsFromYjsEvents(events: any[]): string[] {
    const mentionedUserIds: string[] = [];
    
    events.forEach(event => {
        // Handle different types of YJS events
        if (event.target && event.target.toJSON) {
            try {
                const content = event.target.toJSON();
                
                // If it's an array of blocks
                if (Array.isArray(content)) {
                    content.forEach(block => {
                        mentionedUserIds.push(...extractMentionUserIds(block));
                    });
                }
                // If it's a single block
                else if (content && typeof content === 'object') {
                    mentionedUserIds.push(...extractMentionUserIds(content));
                }
            } catch (error) {
                console.error('Error parsing YJS event content:', error);
            }
        }
        
        // Handle delta changes (for text insertions)
        if (event.changes && event.changes.delta) {
            event.changes.delta.forEach((change: any) => {
                if (change.insert && typeof change.insert === 'object') {
                    // Check if inserted content is a mention
                    if (change.insert.type === 'mention' && change.insert.props && change.insert.props.id) {
                        mentionedUserIds.push(change.insert.props.id);
                    }
                }
            });
        }
    });
    
    return [...new Set(mentionedUserIds)]; // Remove duplicates
}

export const Schema = BlockNoteSchema.create({
  blockSpecs: {
    ...defaultBlockSpecs,
  },
  inlineContentSpecs: {
    ...defaultInlineContentSpecs,
    mention:Mention
  },
  styleSpecs: {
    ...defaultStyleSpecs,
    comment: commentStyleSpec,
  },
});