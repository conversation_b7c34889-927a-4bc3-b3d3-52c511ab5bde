"use client"

import React from "react"
import { en } from "@blocknote/core/locales";
import { useCallback, useEffect, useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Check, Edit, EditIcon, MessageSquare, MoreHorizontal, Smile, Trash2, SendHorizontal, X, CheckCircle, Undo2Icon } from "lucide-react"
import { BlockNoteViewEditor, SuggestionMenuController, useCreateBlockNote, useDictionary } from "@blocknote/react"
import { Block, BlockNoteEditor, filterSuggestionItems } from "@blocknote/core"
import { useBlockNoteEditor, useComponentsContext } from "@blocknote/react"
import { MyWorkspaceMember, WorkspaceMemberRole } from "@/typings/workspace"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User } from "@/typings/user"
import styles from "./commentBox.module.css";
import { BlockNoteView } from "@blocknote/mantine"
import { cn } from "@/lib/utils"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { timeAgo } from "@/lib/utils";
import { getMentionMenuItems, CommentSchema } from "./schema";
import { EmojiPicker } from "@/components/custom-ui/emojiPicker";
import { ReactionBar } from "./reactionBar";
import { MessageLinesIcon, SendBackIcon, XIcon } from "@/components/icons/FontAwesomeRegular";
import { RiChat3Line } from "react-icons/ri";
import { IoIosCheckmarkCircle } from "react-icons/io";
import { Comment, Thread } from "@/typings/comment";
import { toast } from "sonner";

export const CommentButton = React.memo(() => {
  const dict = useDictionary();
  const Components = useComponentsContext()!;
  const editor = useBlockNoteEditor();

  const onClick = useCallback(() => {
    editor.comments?.startPendingComment();
    editor.formattingToolbar.closeMenu();
  }, [editor]);

  return (
    <Components.FormattingToolbar.Button
      label={dict.formatting_toolbar.comment.tooltip}
      mainTooltip={dict.formatting_toolbar.comment.tooltip}
      onClick={onClick}
      icon={<RiChat3Line className="w-4 h-4" />}
      className="bn-button"
    />
  );
});

CommentButton.displayName = 'CommentButton';

interface CommentPopupProps {
  position?: { x: number; y: number }
  className?: string;
  comment?: Comment;
  commentThread?: Thread;
  selectedText?: string
  onClose?: () => void,
  readonly?: boolean
  workspaceMembers: MyWorkspaceMember[];
  currentUser?: User;
  onAddComment?: (content: Block<any, any, any>[]) => void;
  onResolve?: (commentId: string) => void;
  onOpen?: (commentId: string) => void;
  onReply?: (commentId: string) => void;
  onAddReaction?: (emoji: string, commentId: string) => void;
  onDeleteReaction?: (emoji: string, commentId: string) => void;
  onDelete?: (commentId: string) => void
  onEditComment?: (content: Block<any, any, any>[]) => void
}

export const FloatingCommentPopup: React.FC<CommentPopupProps> = ({
  position,
  className,
  currentUser,
  workspaceMembers,
  comment,
  commentThread,
  onEditComment,
  onAddReaction,
  onDeleteReaction,
  readonly = false,
  onAddComment,
  onDelete,
  onResolve,
  onOpen,
  onReply,
}) => {
  const [newComment, setNewComment] = useState<Block<any, any, any>[]>([]);
  const [isHovered, setIsHovered] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  console.log({ commentThread })
  // Memoize expensive calculations
  const member = useMemo(() =>
    workspaceMembers.find((m) => m.user.id === comment?.userId),
    [workspaceMembers, comment?.userId]
  );

  const hasPermission = useMemo(() => {
    const user = workspaceMembers.find((m) => m.user.id === currentUser?.id);
    // WIP: confirm during meeting if admin can delete other users comment
    return user?.workspaceMember.role === WorkspaceMemberRole.Admin ||
      user?.workspaceMember.role === WorkspaceMemberRole.Owner ||
      currentUser?.id === comment?.userId;
    // return currentUser?.id === comment?.userId;
  }, [workspaceMembers, currentUser?.id, comment?.userId]);

  const showThreadLine = useMemo(() => {
    return (commentThread && commentThread.comments && commentThread.comments.length > 1) ||
      (comment && comment.parentId && comment?.parentId);
  }, [commentThread, comment?.id]);

  // Memoize editor creation to prevent unnecessary re-renders
  const newCommentEditor = useCreateBlockNote({
    trailingBlock: false,
    dictionary: {
      ...en,
      placeholders: {
        emptyDocument: en.placeholders.new_comment,
      },
    },
    schema: CommentSchema,
    sideMenuDetection: "editor",
  });

  const editCommentEditor = useMemo(() => {
    // if (!comment?.body) return null

    return BlockNoteEditor.create({
      initialContent: (comment && comment.contentJSON.length !== 0) ? comment.contentJSON : undefined,
      trailingBlock: false,
      dictionary: {
        ...en,
        placeholders: {
          emptyDocument: en.placeholders.edit_comment,
        },
      },
      schema: CommentSchema,
      sideMenuDetection: "editor",
    })
  }, [comment?.contentJSON]);

  // Auto-focus when editing starts
  useEffect(() => {
    if (isEditing && editCommentEditor) {
      // Small delay to ensure editor is rendered
      setTimeout(() => {
        editCommentEditor.focus();
      }, 100);
    }
  }, [isEditing, editCommentEditor]);

  const handleSubmit = useCallback(() => {
    if (!newCommentEditor || !onAddComment) return;

    const hasContent = newCommentEditor.document.length > 1 ||
      newCommentEditor.document[0]?.content?.length > 0;
    const cleanedContent = newComment.filter(k => Array.isArray(k.content) && k.content.length > 0)
    if (!hasContent || cleanedContent.length === 0) {
      toast("Comment can not be empty")
      return
    }

      onAddComment(newComment);
      newCommentEditor.replaceBlocks(newCommentEditor.document, [
        { type: "paragraph", content: [] },
      ]);
      setNewComment([]);

  }, [newCommentEditor, onAddComment, newComment]);

  const handleSubmitEdit = useCallback(() => {
    if (!comment || !onEditComment || !editCommentEditor) return;


    const hasContent = editCommentEditor.document.length > 1 ||
      editCommentEditor.document[0]?.content?.length > 0;

    const cleanedContent = editCommentEditor.document.filter(k => Array.isArray(k.content) && k.content.length > 0)
    if (!hasContent || cleanedContent.length === 0) {
      toast("Comment can not be empty")
      return
    }

    onEditComment(editCommentEditor.document);
    setIsEditing(false);
    setIsDropdownOpen(false)
  }, [comment, onEditComment, editCommentEditor]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (isEditing) {
        handleSubmitEdit();
      } else {
        handleSubmit();
      }
    }
    if (event.key === "Escape" && isEditing) {
      event.preventDefault();
      onEditCancel();
    }
  }, [isEditing, handleSubmitEdit, handleSubmit]);

  const handleEmojiSelect = useCallback((emoji: string) => {
    if (onAddReaction && comment) {
      onAddReaction(emoji, comment.id);
    }
  }, [onAddReaction, comment]);

  const handleDeleteEmoji = useCallback((emoji: string) => {
    if (onDeleteReaction && comment) {
      onDeleteReaction(emoji, comment.id);
    }
  }, [onAddReaction, comment]);

  const handleCommentClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (onReply && comment?.id) {
      onReply(comment.id);
    }
  }, [onReply, comment?.id]);

  const handleEditToggle = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditing(!isEditing);
  }, [isEditing]);

  const handleDelete = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete && comment?.id) {
      onDelete(comment.id);
    }
  }, [onDelete, comment?.id]);

  const handleResolve = useCallback(() => {
    if (onResolve && comment?.id) {
      onResolve(comment.id);
    }
  }, [onResolve, comment?.id]);

  // Improved positioning with viewport constraints
  const getConstrainedPosition = useCallback(() => {
    if (!position) return {};

    const maxWidth = 400; // Max width of comment box
    const maxHeight = 300; // Estimated max height
    const padding = 16; // Padding from viewport edges

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let { x, y } = position;

    // Constrain horizontally
    if (x + maxWidth > viewportWidth - padding) {
      x = viewportWidth - maxWidth - padding;
    }
    if (x < padding) {
      x = padding;
    }

    // Constrain vertically
    if (y + maxHeight > viewportHeight - padding) {
      y = viewportHeight - maxHeight - padding;
    }
    if (y < padding) {
      y = padding;
    }

    return {
      position: 'fixed' as const,
      top: y,
      left: x,
      zIndex: 50,
      maxWidth: maxWidth,
      maxHeight: '60vh'
    };
  }, [position]);

  const onEditCancel = useCallback(() => {
    if (editCommentEditor && comment) {
      editCommentEditor.replaceBlocks(editCommentEditor.document, comment.contentJSON);
    }
    setIsEditing(false);
  }, [editCommentEditor, comment?.contentJSON]);

  const positionStyle = getConstrainedPosition();

  return (
    <Card
      className={cn(
        "shadow-lg border bg-background transition-all duration-200",
        "", // Prevent content overflow
        commentThread?.isResolved && "opacity-75",
        className,
        styles.wrapper
      )}
      style={positionStyle}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-1.5 max-h-[50vh] relative">
        {comment && member ? (
          <div
            key={comment.id}
            className="relative text-sm gap-3 min-h-0" // Changed to items-start and added min-h-0
            onClick={handleCommentClick}
          >
            {/* Avatar Column */}
            <div className="flex flex-row items-center justify-between flex-shrink-0">
              <div className="flex items-center flex-row select-none text-sm gap-1">
                <div className="items-center flex flex-row gap-2">
                  <Avatar className="h-7 w-7">
                    <AvatarImage src={member.user?.profilePhoto} />
                    <AvatarFallback className="text-xs">
                      {member.user ?
                        (member.user.firstName[0] + member.user.lastName[0]).toUpperCase() :
                        "A"
                      }
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-semibold truncate">
                    {member.user?.firstName} {member.user?.lastName}
                  </span>
                </div>
                <span className="text-xs text-muted-foreground flex-shrink-0">
                  {timeAgo(new Date(comment.createdAt))}
                </span>
              </div>
            </div>
            {/* Thread Line */}
            {showThreadLine && (
              <div
                className=" bg-gray-200 absolute rounded-sm h-[calc(100%-20px)] left-[11px] top-[30px] flex-1 min-h-[40px] mt-1"
                style={{ minHeight: '40px', width: "1.5px" }}
              />
            )}
            <div className="flex-1 z-20 min-w-0 space-y-2 absolute top-0 right-0 ">


              {!isEditing && (isHovered || isDropdownOpen) && (
                <div className="flex absolute top-0 right-2 items-center gap-1 bg-background border rounded-md shadow-sm p-1 flex-shrink-0">
                  {onAddReaction && !commentThread?.isResolved && (
                    <EmojiPicker
                      emoji={Smile.toString()}
                      onChange={handleEmojiSelect}
                      triggerClassName="h-6 w-6 p-0"
                      trigger={
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <Smile className="h-3 w-3" />
                        </Button>
                      }
                    />
                  )}

                  {commentThread && onOpen && onResolve &&
                    (!commentThread?.isResolved ? <Button
                    variant="ghost"
                    size="sm"
                    className={`h-6 w-6 p-0`}
                    onClick={() => onResolve(commentThread.id)}
                    title={"resolve"}
                  >
                    <Check className="h-3 w-3 " />
                  </Button> :
                      <Button
                        variant="ghost"
                        size="sm"
                      className={`h-6 w-6 p-0  bg-gray-200`}
                        onClick={() => onOpen(commentThread.id)}
                        title={`open`}
                      >
                        <Undo2Icon className="h-3 w-3 " />
                      </Button>
                    )}

                  {hasPermission && !commentThread?.isResolved && <DropdownMenu onOpenChange={setIsDropdownOpen}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-40">
                      {onEditComment && (
                        <DropdownMenuItem onClick={handleEditToggle} className="text-sm flex gap-1 items-center">
                          <EditIcon className="h-4 w-4 mr-2" />
                          Edit comment
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={handleDelete} className="text-sm flex gap-1 items-center">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete comment
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  }
                </div>
              )}
            </div>
            <div className=" pl-7 mt-2 space-y-2">


              {/* Quoted Text */}
              {onResolve && comment?.contentText && (
                <div className="border-l-2 border-yellow-400 pl-3 py-1 text-xs text-muted-foreground  rounded-r">
                  <span className="break-words">{comment.contentText}</span>
                </div>
              )}

              {/* Comment Content */}
              <div className={cn(
                "relative min-h-0 overflow-hidden flex items-start justify-between gap-3", // Added overflow-hidden and min-h-0
                styles.editorWrapper,
                isEditing && "ring-2 ring-blue-500/50 shadow-lg rounded-lg p-1",
              )}>
                <BlockNoteView
                  editor={editCommentEditor}
                  onKeyDown={handleKeyDown}
                  slashMenu={false}
                  draggable={false}
                  sideMenu={false}
                  editable={isEditing}
                  theme="light"
                  className={cn("text-sm min-h-0 flex-1")}
                >
                  <SuggestionMenuController
                    triggerCharacter="@"
                    getItems={async (query) =>
                      filterSuggestionItems(
                        getMentionMenuItems(newCommentEditor, workspaceMembers),
                        query
                      )
                    }
                  />
                </BlockNoteView>

                {/* Edit Controls - Improved styling and positioning */}
                {isEditing && (
                  <div className="flex-shrink-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onEditCancel}
                      className="h-6 w-6 p-0"
                      title="Cancel (Esc)"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSubmitEdit}
                      className="h-6 w-6 p-0"
                      title="Save (Enter)"
                    >
                      <SendHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
              {/* Reactions - Hide when editing */}
              {!isEditing && onAddReaction && comment.reactions?.length > 0 && (
                <ReactionBar
                  reactions={comment.reactions}
                  onReactionClick={handleDeleteEmoji}
                  onAddReaction={handleEmojiSelect}
                  currentUserId={currentUser?.id!}
                />
              )}
            </div>
          </div>
        ) : (
          // New Comment Form
          <div className="flex items-start gap-3">
            <Avatar className="h-7 w-7 flex-shrink-0">
              <AvatarImage src={currentUser?.profilePhoto} />
                <AvatarFallback className="text-xs">
                  {currentUser ?
                    (currentUser.firstName[0] + currentUser.lastName[0]).toUpperCase() :
                    "U"
                  }
                </AvatarFallback>
              </Avatar>

              <div className={cn("relative flex-1 min-w-0 pr-8", styles.editorWrapper)}>
                <BlockNoteView
                  editor={newCommentEditor}
                  onKeyDown={handleKeyDown}
                  theme="light"
                  autoFocus
                  editable={!readonly}
                  slashMenu={false}
                  draggable={false}
                  sideMenu={false}
                  onChange={() => setNewComment(newCommentEditor.document)}
                  className="min-h-0"
                >
                  <SuggestionMenuController
                    triggerCharacter="@"
                    getItems={async (query) =>
                      filterSuggestionItems(
                        getMentionMenuItems(newCommentEditor, workspaceMembers),
                        query
                      )
                    }
                  />
                </BlockNoteView>


              </div>
              <div className="h-7 w-7 flex-shrink-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSubmit}
                    className="h-6 w-6 p-0"
                  >
                    <SendHorizontal className="h-4 w-4" />
                  </Button>
              </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};