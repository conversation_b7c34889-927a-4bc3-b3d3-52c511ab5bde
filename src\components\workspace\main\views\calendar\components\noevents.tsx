import React from 'react';
import { Button } from '@/components/ui/button';
import { PlusIcon } from '@heroicons/react/24/outline';

interface NoEventsProps {
  title: string;
  message: string;
  showCreateButton: boolean;
  onCreate: () => void;
}

export const NoEvents: React.FC<NoEventsProps> = ({ title, message, showCreateButton, onCreate }) => {
  return (
    <div className="flex-1 flex items-center justify-center bg-neutral-50">
      <div className="text-center max-w-md mx-auto px-6">
        <div className="w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center border border-neutral-300">
          <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 className="text-xs font-semibold text-black mb-2">
          {title}
        </h3>
        <p className="text-xs text-black mb-6">
          {message}
        </p>
        {showCreateButton && (
          <Button
            onClick={onCreate}
            className="rounded-full h-8 px-3 text-xs border bg-white hover:bg-neutral-100 text-black gap-1"
          >
            <PlusIcon className="w-3 h-3" />
            Create Event
          </Button>
        )}
      </div>
    </div>
  );
}; 
