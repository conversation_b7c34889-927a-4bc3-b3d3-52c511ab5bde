// // types/suggestions.ts
// export interface Suggestion {
//   id: string;
//   userId: string;
//   blockId: string;
//   type: 'insert' | 'delete' | 'replace' | 'style';
//   originalContent?: any;
//   suggestedContent: any;
//   timestamp: number;
//   status: 'pending' | 'accepted' | 'rejected';
//   startPos?: number;
//   endPos?: number;
// }

// export interface SuggestionModeState {
//   isEnabled: boolean;
//   userId: string;
//   suggestions: Map<string, Suggestion>;
// }

// // Custom inline content for suggestions
// import { createReactInlineContentSpec } from "@blocknote/react";
// import { InlineContentSchema } from "@blocknote/core";

// export const SuggestionInline = createReactInlineContentSpec(
//   {
//     type: "suggestion",
//     propSchema: {
//       suggestionId: { default: "" },
//       type: { default: "insert" as "insert" | "delete" | "replace" },
//       originalText: { default: "" },
//       suggestedText: { default: "" },
//     },
//     content: "styled",
//   },
//   {
//     render: (props) => {
//       const { type, originalText, suggestedText } = props.inlineContent.props;
      
//       if (type === "insert") {
//         return (
//           <span
//             className="suggestion-insert"
//             style={{
//               backgroundColor: "rgba(59, 130, 246, 0.2)",
//               borderBottom: "2px solid #3b82f6",
//               color: "#1e40af",
//             }}
//             data-suggestion-id={props.inlineContent.props.suggestionId}
//           >
//             {suggestedText}
//           </span>
//         );
//       }
      
//       if (type === "delete") {
//         return (
//           <span
//             className="suggestion-delete"
//             style={{
//               backgroundColor: "rgba(239, 68, 68, 0.2)",
//               textDecoration: "line-through",
//               color: "#991b1b",
//             }}
//             data-suggestion-id={props.inlineContent.props.suggestionId}
//           >
//             {originalText}
//           </span>
//         );
//       }
      
//       if (type === "replace") {
//         return (
//           <span data-suggestion-id={props.inlineContent.props.suggestionId}>
//             <span
//               className="suggestion-delete"
//               style={{
//                 backgroundColor: "rgba(239, 68, 68, 0.2)",
//                 textDecoration: "line-through",
//                 color: "#991b1b",
//                 marginRight: "4px",
//               }}
//             >
//               {originalText}
//             </span>
//             <span
//               className="suggestion-insert"
//               style={{
//                 backgroundColor: "rgba(59, 130, 246, 0.2)",
//                 borderBottom: "2px solid #3b82f6",
//                 color: "#1e40af",
//               }}
//             >
//               {suggestedText}
//             </span>
//           </span>
//         );
//       }
      
//       return <span>{props.children}</span>;
//     },
//   }
// );

// // Custom block for suggested blocks
// import { createReactBlockSpec } from "@blocknote/react";

// export const SuggestionBlock = createReactBlockSpec(
//   {
//     type: "suggestionBlock",
//     propSchema: {
//       suggestionId: { default: "" },
//       type: { default: "insert" as "insert" | "delete" },
//       originalBlock: { default: null },
//       suggestedBlock: { default: null },
//     },
//     content: "none",
//   },
//   {
//     render: (props) => {
//       const { type, originalBlock, suggestedBlock } = props.block.props;
      
//       if (type === "insert") {
//         return (
//           <div
//             className="suggestion-block-insert"
//             style={{
//               border: "2px solid #3b82f6",
//               borderRadius: "4px",
//               backgroundColor: "rgba(59, 130, 246, 0.1)",
//               padding: "8px",
//               margin: "4px 0",
//             }}
//             data-suggestion-id={props.block.props.suggestionId}
//           >
//             <div style={{ fontSize: "12px", color: "#3b82f6", marginBottom: "4px" }}>
//               Suggested Addition
//             </div>
//             {/* Render the suggested block content */}
//             <div>{JSON.stringify(suggestedBlock)}</div>
//           </div>
//         );
//       }
      
//       if (type === "delete") {
//         return (
//           <div
//             className="suggestion-block-delete"
//             style={{
//               border: "2px solid #ef4444",
//               borderRadius: "4px",
//               backgroundColor: "rgba(239, 68, 68, 0.1)",
//               padding: "8px",
//               margin: "4px 0",
//               opacity: 0.6,
//             }}
//             data-suggestion-id={props.block.props.suggestionId}
//           >
//             <div style={{ fontSize: "12px", color: "#ef4444", marginBottom: "4px" }}>
//               Suggested Deletion
//             </div>
//             <div style={{ textDecoration: "line-through" }}>
//               {JSON.stringify(originalBlock)}
//             </div>
//           </div>
//         );
//       }
      
//       return <div>Suggestion Block</div>;
//     },
//   }
// );
