"use client"


import React, {create<PERSON><PERSON>xt, PropsWithC<PERSON>dren, use<PERSON><PERSON>back, useContext, useEffect, useRef, useState} from "react";
import {usePara<PERSON>, useRouter} from "next/navigation";
import {useAuth} from "@/providers/user";
import {PageLoader} from "@/components/custom-ui/loader";
import {defaultAPIMessage} from "@/api/common";
import {getMemberSettings, getNotificationStats, getWorkspace, getWorkspaceMembers} from "@/api/workspace";
import {getPages} from "@/api/page";
import {getDatabases} from "@/api/database";
import {KeyValueStore, MyWorkspace, MyWorkspaceMember, Notification, Workspace, WorkspaceInvitation, WorkspaceMemberSettings} from "@/typings/workspace";
import {DatabaseErrorStore, DatabasePageStore, DatabaseRecordStore, PageStore, RecordsIdMap} from "@/typings/utilities";
import {useWorkspaceSocket, WorkspaceSocketProvider} from "@/providers/workspaceSocket";
import {AdjacentDatabases, Database, PermissibleDatabaseWithPermissions, Record} from "@/typings/database";
import { AccessLevel, PagePermission, PermissiblePageWithPermissions, UserNotificationSettings, View } from "@/typings/page";
import {arrayDeDuplicate} from "opendb-app-db-utils/lib";
import {cleanUpPermissions} from "@/utils/permission";
import {RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {ViewsProvider} from "@/providers/views";
import {useAlert} from "@/providers/alert";

export interface WorkspaceProviderProps {
}


export const WorkspaceProvider = (props: PropsWithChildren<WorkspaceProviderProps>) => {
    const params = useParams();
    const domain: string = params.domain as string
    const url = (path: string = "") => {
        return `/${domain}${path}`
    }

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string>("");
    const [canRetry, setCanRetry] = useState(false);
    const pageStoreRef = useRef<PageStore>({})
    const databasePageStoreRef = useRef<DatabasePageStore>({})
    const databaseStoreRef = useRef<DatabaseRecordStore>({})
    const databaseErrorStoreRef = useRef<DatabaseErrorStore>({})

    const [pagesId, setPagesId] = useState<string[]>([])
    const [databasePagesId, setDatabasePagesId] = useState<string[]>([])

    const [workspace, setWorkspace] = useState<MyWorkspace | null>(null)
    const [members, setMembers] = useState<MyWorkspaceMember[]>([])
    const [memberSettings, setMemberSettings] = useState<WorkspaceMemberSettings>()
    const [invitations, setInvitations] = useState<WorkspaceInvitation[]>([])

    const firstLoadRef = useRef(true);
    const router = useRouter();

    const [, setRenderCount] = useState(0);

    const forceRender = useCallback(() => {
            setRenderCount(prevCount => prevCount + 1);
        },
        []
    );

    const {token, workspaces, isAuthenticated, error: authError, updateWorkspaces} = useAuth();

    // const handleWorkspaceRedirect = () => {
    //     const location = window.location.href;
    //     setRedirectLocation(location);
    //     router.push(`/auth/sign-in`);
    //     return;
    // };

    const initContext = async () => {
        if (!token || !workspaces) {
            setError("Token not available");
            return;
        }
        if (loading) return;
        let workspaceId = ''
        for (const w of workspaces) {
            if (w.workspace.domain === domain) {
                workspaceId = w.workspace.id
                break
            }
        }
        if (!workspaceId) {
            return router.push("/welcome")
        }

        setLoading(true);
        // get the workspace,members, pages and databases

        const res1 = await getWorkspace(token.token, workspaceId)
        if (res1.error) {
            setError(res1.error || defaultAPIMessage())
            setLoading(false)
            return
        }
        const workspace = res1.data.data.workspace

        const res2 = await getWorkspaceMembers(token.token, workspaceId)
        if (res2.error) {
            setError(res2.error || defaultAPIMessage())
            setLoading(false)
            return
        }
        const {members, invitations} = res2.data.data

        const res3 = await getPages(token.token, workspaceId)
        if (res3.error) {
            setError(res3.error || defaultAPIMessage())
            setLoading(false)
            return
        }
        const {pages} = res3.data.data

        const res4 = await getDatabases(token.token, workspaceId)
        if (res4.error) {
            setError(res4.error || defaultAPIMessage())
            setLoading(false)
            return
        }
        const {databases} = res4.data.data

        const res5 = await getMemberSettings(token.token, workspaceId)
        if (res5.error) {
            setError(res5.error || defaultAPIMessage())
            setLoading(false)
            return
        }
        const {settings} = res5.data.data

        const pageIds: string[] = []
        for (const p of pages) {
            pageStoreRef.current[p.page.id] = p
            pageIds.push(p.page.id)
        }

        const databaseIds: string[] = []
        for (const d of databases) {
            databaseIds.push(d.database.id)
            databasePageStoreRef.current[d.database.id] = d

            databaseStoreRef.current[d.database.id] = databaseStoreRef.current[d.database.id] || {
                database: d.database,
                recordsIdMap: {}
            }
            databaseStoreRef.current[d.database.id].database = d.database
        }
        setPagesId(pageIds)
        setDatabasePagesId(databaseIds)
        // setDatabases(databases)
        // setPages(pages)
        setInvitations(invitations)
        setMembers(members)
        setWorkspace(workspace)
        setMemberSettings(settings)

        setLoading(false);
    }

    const refreshPagesAndDatabases = async () => {
        if (!token || !workspaces) return;
        let workspaceId = ''
        for (const w of workspaces) {
            if (w.workspace.domain === domain) {
                workspaceId = w.workspace.id
                break
            }
        }
        const res3 = await getPages(token.token, workspaceId)
        if (res3.error) {
            return
        }
        const {pages} = res3.data.data

        const res4 = await getDatabases(token.token, workspaceId)
        if (res4.error) {
            return
        }
        const {databases} = res4.data.data

        const pageIds: string[] = []
        for (const p of pages) {
            pageStoreRef.current[p.page.id] = p
            pageIds.push(p.page.id)
        }
        const databaseIds: string[] = []
        for (const d of databases) {
            databaseIds.push(d.database.id)
            databasePageStoreRef.current[d.database.id] = d

            databaseStoreRef.current[d.database.id] = databaseStoreRef.current[d.database.id] || {
                database: d.database,
                recordsIdMap: {}
            }
            databaseStoreRef.current[d.database.id].database = d.database
        }
        setPagesId(pageIds)
        setDatabasePagesId(databaseIds)
    }

    const ready = isAuthenticated() || !!authError

    useEffect(() => {
        if (!ready) return;
        initContext().then().finally();
        firstLoadRef.current = false;
        return;
    }, [ready]);

    const updateWorkspace = (partial: Partial<Workspace>) => {
        if (!workspace || !workspaces) return
        const update = {...workspace.workspace, ...partial}
        const updated = {...workspace}
        updated.workspace = update


        const updatedWorkspaces = [...workspaces]
        for (const w of updatedWorkspaces) {
            if (w.workspace.id === workspace.workspace.id) {
                w.workspace = {...w.workspace, ...partial}
            }
        }
        setWorkspace(updated)
        updateWorkspaces([...updatedWorkspaces])
    }

    const updateDatabaseStore = (id: string, database: Partial<Database>, recordsIdMap?: RecordsIdMap, recordsLoaded = false) => {
        if (!databaseStoreRef.current[id]) return
        if (databaseStoreRef.current[id].database) {
            databaseStoreRef.current[id].database = {...(databaseStoreRef.current[id].database as Database), ...database}
            databaseStoreRef.current[id].isAdjacent = false
            databaseStoreRef.current[id].recordsLoaded = databaseStoreRef.current[id].recordsLoaded || recordsLoaded
            databaseStoreRef.current[id].updatedAt = new Date()
            if (recordsIdMap) {
                databaseStoreRef.current[id].recordsIdMap = {
                    ...databaseStoreRef.current[id].recordsIdMap,
                    ...recordsIdMap
                }
            }
            forceRender()
        }
    }

    const updateDatabaseRecordValues = (id: string, recordIds: string[], values: RecordValues, meta: KeyValueStore = {}, recordExtra: Partial<Record> = {}, render: boolean = false, userNotificationSettings: UserNotificationSettings = {}) => {
        if (!databaseStoreRef.current[id]) return

        let hasUpdate = false
        for (let recordId of recordIds) {
            const record = databaseStoreRef.current[id].recordsIdMap?.[recordId]
            if (!record) continue
            record.record.recordValues = {...record.record.recordValues, ...values}

            meta = meta || {}
            record.record = { ...record.record, ...recordExtra }
            record.record.userNotificationSettings = { ...record.record.userNotificationSettings, ...userNotificationSettings }
            record.record.meta = record.record.meta || {}
            record.record.meta = {...record.record.meta, ...meta}
            record.record.updatedAt = new Date().toISOString()

            hasUpdate = true
        }

        if (!hasUpdate) return

        databaseStoreRef.current[id].updatedAt = new Date()

        // @todo: transform record to processed record
        if (render) forceRender()
    }

    const deletePage = (id: string, render: boolean = true) => {
        if (!pageStoreRef.current[id]) return
        delete pageStoreRef.current[id]
        if (render) forceRender()
    }

    const deleteDatabase = (id: string, render: boolean = true) => {
        if (!databasePageStoreRef.current[id]) return
        delete databasePageStoreRef.current[id]

        if (databaseStoreRef.current[id]) delete databaseStoreRef.current[id]

        if (render) forceRender()
    }

    const addDatabase = (d: PermissibleDatabaseWithPermissions) => {
        const {id} = d.database
        databasePageStoreRef.current[id] = d
        databaseStoreRef.current[id] = databaseStoreRef.current[id] || {
            database: d.database,
            recordsIdMap: {}
        }
        databaseStoreRef.current[id].database = d.database

        const ids = arrayDeDuplicate([...databasePagesId, id])
        setDatabasePagesId(ids)
        forceRender()
    }

    const addPage = (p: PermissiblePageWithPermissions) => {
        const {id} = p.page
        pageStoreRef.current[id] = p
        const ids = arrayDeDuplicate([...pagesId, id])
        setPagesId(ids)
        forceRender()
    }

    const updateDatabasePageStore = (id: string, database: Partial<PermissibleDatabaseWithPermissions>, render: boolean = false) => {
        if (!databasePageStoreRef.current[id] || !workspace) return
        databasePageStoreRef.current[id] = {
            ...databasePageStoreRef.current[id],
            ...database,
            updatedAt: new Date()
        }
        const {accessLevel, permissions} = cleanUpPermissions(
            workspace.workspaceMember,
            databasePageStoreRef.current[id].page,
            databasePageStoreRef.current[id].permissions
        )
        databasePageStoreRef.current[id].permissions = permissions
        databasePageStoreRef.current[id].accessLevel = accessLevel as AccessLevel // @Todo: Shouldn't return null

        if (render) forceRender()
    }

    const updatePageStore = (id: string, page: Partial<PermissiblePageWithPermissions>, render: boolean = false) => {
        if (!pageStoreRef.current[id] || !workspace) return
        pageStoreRef.current[id] = {
            ...pageStoreRef.current[id],
            ...page,
            updatedAt: new Date()
        }
        const {accessLevel, permissions} = cleanUpPermissions(
            workspace.workspaceMember,
            pageStoreRef.current[id].page,
            pageStoreRef.current[id].permissions
        )
        pageStoreRef.current[id].permissions = permissions
        pageStoreRef.current[id].accessLevel = accessLevel as AccessLevel // @Todo: Shouldn't return null

        if (render) forceRender()
    }

    const updatePageViews = (context: 'database' | 'page', id: string, viewsOrder: string[], views: View[]) => {
        if (context === 'database') {
            if (!databasePageStoreRef.current[id]) return
            databasePageStoreRef.current[id].views = views
            databasePageStoreRef.current[id].page.viewsOrder = arrayDeDuplicate(viewsOrder)
            databasePageStoreRef.current[id].updatedAt = new Date()
            forceRender()
            return
        }
        if (!pageStoreRef.current[id]) return
        pageStoreRef.current[id].views = views
        pageStoreRef.current[id].page.viewsOrder = arrayDeDuplicate(viewsOrder)
        pageStoreRef.current[id].updatedAt = new Date()
        forceRender()
    }

    const updateMyWorkspace = (partial: Partial<MyWorkspace>) => {
        if (!workspace) return
        setWorkspace({...workspace, ...partial})
    }

    const updateMembers = (members: MyWorkspaceMember[]) => setMembers(members)
    const updateInvitations = (invitations: WorkspaceInvitation[]) => setInvitations(invitations)

    const updateMemberSettings = (update: Partial<WorkspaceMemberSettings>) => {
        if (!memberSettings) return
        const updated = {...memberSettings, ...update}
        setMemberSettings(updated)
    }

    const addPagePermissions = (id: string, databaseId: string, added: PagePermission[]) => {
        if (!workspace) return
        if (databaseId && databasePageStoreRef.current[databaseId]) {
            databasePageStoreRef.current[databaseId].permissions = databasePageStoreRef.current[databaseId].permissions || []
            const allPermissions = [...databasePageStoreRef.current[databaseId].permissions, ...added]
            const {accessLevel, permissions} = cleanUpPermissions(
                workspace.workspaceMember,
                databasePageStoreRef.current[databaseId].page,
                allPermissions
            )
            databasePageStoreRef.current[databaseId].permissions = permissions
            databasePageStoreRef.current[databaseId].accessLevel = accessLevel as AccessLevel // @Todo: Shouldn't return null

            forceRender();
            return;
        }
        if (pageStoreRef.current[id]) {
            pageStoreRef.current[id].permissions = pageStoreRef.current[id].permissions || []
            const allPermissions = [...pageStoreRef.current[id].permissions, ...added]
            const {accessLevel, permissions} = cleanUpPermissions(
                workspace.workspaceMember,
                pageStoreRef.current[id].page,
                allPermissions
            )
            pageStoreRef.current[id].permissions = permissions
            pageStoreRef.current[id].accessLevel = accessLevel as AccessLevel // @Todo: Shouldn't return null

            forceRender();
            return
        }
    }

    const updatePagePermission = (id: string, databaseId: string, data: {
        userId: string,
        accessLevel: AccessLevel
    }) => {
        if (!workspace) return
        if (databaseId && databasePageStoreRef.current[databaseId]) {
            databasePageStoreRef.current[databaseId].permissions = databasePageStoreRef.current[databaseId].permissions || []
            const allPermissions = [...databasePageStoreRef.current[databaseId].permissions]
            for (const p of allPermissions) {
                if (p.userId === data.userId) p.accessLevel = data.accessLevel
            }
            const {accessLevel, permissions} = cleanUpPermissions(
                workspace.workspaceMember,
                databasePageStoreRef.current[databaseId].page,
                allPermissions
            )
            databasePageStoreRef.current[databaseId].permissions = permissions
            databasePageStoreRef.current[databaseId].accessLevel = accessLevel as AccessLevel // @Todo: Shouldn't return null

            forceRender();
            return;
        }
        if (pageStoreRef.current[id]) {
            pageStoreRef.current[id].permissions = pageStoreRef.current[id].permissions || []
            const allPermissions = [...pageStoreRef.current[id].permissions]
            for (const p of allPermissions) {
                if (p.userId === data.userId) p.accessLevel = data.accessLevel
            }
            const {accessLevel, permissions} = cleanUpPermissions(
                workspace.workspaceMember,
                pageStoreRef.current[id].page,
                allPermissions
            )
            pageStoreRef.current[id].permissions = permissions
            pageStoreRef.current[id].accessLevel = accessLevel as AccessLevel // @Todo: Shouldn't return null

            forceRender();
            return
        }
    }

    const deletePagePermissions = (id: string, databaseId: string, data: {
        userIds: string[]
    }) => {
        if (!workspace) return
        if (databaseId && databasePageStoreRef.current[databaseId]) {
            databasePageStoreRef.current[databaseId].permissions = databasePageStoreRef.current[databaseId].permissions || []
            const allPermissions = databasePageStoreRef.current[databaseId].permissions.filter(p => !data.userIds.includes(p.userId))
            const {accessLevel, permissions} = cleanUpPermissions(
                workspace.workspaceMember,
                databasePageStoreRef.current[databaseId].page,
                allPermissions
            )
            databasePageStoreRef.current[databaseId].permissions = permissions
            databasePageStoreRef.current[databaseId].accessLevel = accessLevel as AccessLevel // @Todo: Shouldn't return null

            forceRender();
            return;
        }
        if (pageStoreRef.current[id]) {
            pageStoreRef.current[id].permissions = pageStoreRef.current[id].permissions || []
            const allPermissions = pageStoreRef.current[databaseId].permissions.filter(p => !data.userIds.includes(p.userId))
            const {accessLevel, permissions} = cleanUpPermissions(
                workspace.workspaceMember,
                pageStoreRef.current[id].page,
                allPermissions
            )
            pageStoreRef.current[id].permissions = permissions
            pageStoreRef.current[id].accessLevel = accessLevel as AccessLevel // @Todo: Shouldn't return null

            forceRender();
            return
        }
    }

    const addAdjacentDatabases = (adjacentDatabases: AdjacentDatabases) => {
        for (const id of Object.keys(adjacentDatabases)) {
            const adjacentDatabase = adjacentDatabases[id]

            const {database, recordsMap, page, error} = adjacentDatabase

            if (!database || !recordsMap || !page) {
                // databaseStoreRef.current[]
                databaseErrorStoreRef.current[id] = {
                    error: error || defaultAPIMessage(),
                    updatedAt: new Date()
                }
                continue
            }
            databaseStoreRef.current[id] = databaseStoreRef.current[id] || {
                database, recordsIdMap: {}, updatedAt: new Date(), isAdjacent: true
            }
            databaseStoreRef.current[id].database = database
            databaseStoreRef.current[id].updatedAt = new Date()

            databasePageStoreRef.current[id] = databasePageStoreRef.current[id] || {
                page: page, views: [],
            }
            databasePageStoreRef.current[id].page = page
            databasePageStoreRef.current[id].updatedAt = new Date()

            for (const r of Object.values(recordsMap)) {
                const {record} = r
                databaseStoreRef.current[id].recordsIdMap[record.id] = databaseStoreRef.current[id].recordsIdMap[record.id] || {
                    record
                }
                databaseStoreRef.current[id].recordsIdMap[record.id].record = record
            }
        }
        forceRender()
    }

    const membersMap: {
        [key: string]: MyWorkspaceMember
    } = {}
    if (members && Array.isArray(members)) {
        for (const member of members) {
            membersMap[member.workspaceMember.userId] = member
        }
    }

    const updateDatabaseErrorState = (id: string, data: {
        error?: string
        updatedAt?: Date
        loading?: boolean
    }, clear = false) => {
        if (clear) {
            delete databaseErrorStoreRef.current[id]
        } else {
            databaseErrorStoreRef.current[id] = data
        }
        forceRender()
    }

    const contextProps: WorkspaceContextProps = {
        domain,
        invitations,
        members,
        url,
        workspace: workspace as MyWorkspace,
        workspaceMeta: workspace?.workspace.meta || {},
        updateWorkspace,
        pageStore: pageStoreRef.current,
        databasePageStore: databasePageStoreRef.current,
        databaseStore: databaseStoreRef.current,
        memberSettings: memberSettings as WorkspaceMemberSettings,
        databaseErrorStore: databaseErrorStoreRef.current,
        refreshPagesAndDatabases,
        updateDatabaseErrorState,
        addPagePermissions,
        updatePagePermission,
        deletePagePermissions,
        membersMap,
        deletePage,
        deleteDatabase,
        addPage,
        addDatabase,
        updatePageStore,
        updateDatabaseStore,
        updateDatabasePageStore,
        addAdjacentDatabases,
        updateMemberSettings,
        updateDatabaseRecordValues,
        pagesId,
        databasePagesId,
        updateMembers,
        updateInvitations,
        updateMyWorkspace,
        updatePageViews
    }
    return <WorkspaceContext.Provider value={contextProps}>
        {(!ready || error || authError) && <>
            <PageLoader
                size="screen"
                error={error || authError}
                showLogo={!!error}
                cta={
                    (error || authError) ? canRetry
                                           ? {
                                                 label: "Reload",
                                                 onClick: () => initContext().then(),
                                             }
                                           : {
                                                 label: "Go Home!",
                                                 onClick: () => router.push("/"),
                                             }
                                         : null
                }
            />
        </>}
        {ready && isAuthenticated() && workspace && <>
            <WorkspaceSocketProvider>
                <WorkspaceNotificationProvider>
                    <ViewsProvider>
                        {props.children}
                    </ViewsProvider>
                </WorkspaceNotificationProvider>
            </WorkspaceSocketProvider>
        </>}
    </WorkspaceContext.Provider>
}

export interface WorkspaceContextProps extends WorkspaceProviderProps {
    domain: string
    url: (relPath?: string) => string
    pagesId: string[]
    databasePagesId: string[]
    workspace: MyWorkspace
    members: MyWorkspaceMember[]
    membersMap: {
        [key: string]: MyWorkspaceMember
    }
    workspaceMeta: KeyValueStore
    memberSettings: WorkspaceMemberSettings
    invitations: WorkspaceInvitation[]
    updateMembers: (members: MyWorkspaceMember[]) => void
    updateMemberSettings: (update: Partial<WorkspaceMemberSettings>) => void
    updateInvitations: (invitations: WorkspaceInvitation[]) => void
    updateWorkspace: (partial: Partial<Workspace>) => void
    updateMyWorkspace: (partial: Partial<MyWorkspace>) => void
    deletePage: (id: string, render?: boolean) => void
    deleteDatabase: (id: string, render?: boolean) => void
    refreshPagesAndDatabases: () => void
    addPage: (p: PermissiblePageWithPermissions) => void
    addDatabase: (d: PermissibleDatabaseWithPermissions) => void
    updateDatabaseRecordValues: (id: string, recordIds: string[], values: RecordValues, meta?: KeyValueStore, recordExtra?: Partial<Record>, render?: boolean, userNotificationSettings?: UserNotificationSettings) => void
    addPagePermissions: (id: string, databaseId: string, added: PagePermission[]) => void
    updateDatabaseErrorState: (id: string, data: {
        error?: string
        updatedAt?: Date
        loading?: boolean
    }, clear?: boolean) => void
    updatePagePermission: (id: string, databaseId: string, data: {
        userId: string,
        accessLevel: AccessLevel
    }) => void
    deletePagePermissions: (id: string, databaseId: string, data: {
        userIds: string[]
    }) => void
    databasePageStore: DatabasePageStore
    pageStore: PageStore
    updatePageStore: (id: string, page: Partial<PermissiblePageWithPermissions>, render?: boolean) => void
    updateDatabaseStore: (id: string, database: Partial<Database>, recordsIdMap?: RecordsIdMap, recordsLoaded?: boolean) => void
    updateDatabasePageStore: (id: string, database: Partial<PermissibleDatabaseWithPermissions>, render?: boolean) => void
    updatePageViews: (context: 'database' | 'page', id: string, viewsOrder: string[], views: View[]) => void
    databaseStore: DatabaseRecordStore
    addAdjacentDatabases: (adjacentDatabases: AdjacentDatabases) => void
    databaseErrorStore: DatabaseErrorStore
}

export const WorkspaceContext = createContext<WorkspaceContextProps | undefined>(
    undefined
);

export const useWorkspace = () => {
    const context = useContext(WorkspaceContext)
    if (!context) throw new Error('useWorkspace must be used within a WorkspaceProvider');
    return context as WorkspaceContextProps;
}


export interface WorkspaceNotificationContextProps {
    unreadNotifications: number
    pendingReminders: number
    decrementUnreadNotifications: (count: number) => void
    decrementPendingReminders: (count: number) => void
}

export const WorkspaceNotificationContext = createContext<WorkspaceNotificationContextProps | undefined>(
    undefined
);

export const WorkspaceNotificationProvider = (props: PropsWithChildren) => {
    const {token} = useAuth()
    const {workspace} = useWorkspace()
    const {socket, isConnected} = useWorkspaceSocket()
    const {toast} = useAlert()

    const [unreadNotifications, setUnreadNotifications] = useState(0)
    const [pendingReminders, setPendingReminders] = useState(0)

    const decrementPendingReminders = (n: number) => {
        setPendingReminders(Math.max(pendingReminders - n, 0))
    }
    const decrementUnreadNotifications = (n: number) => {
        setUnreadNotifications(Math.max(unreadNotifications - n, 0))
    }

    const [isRefreshing, setIsRefreshing] = useState(false)

    const refreshBadge = async () => {
        if (!token || isRefreshing) return
        setIsRefreshing(true)
        const res = await getNotificationStats(token.token, workspace.workspace.id)
        setIsRefreshing(false)
        if (!res.error) {
            setUnreadNotifications(res.data.data.stats.unreadNotifications)
            setPendingReminders(res.data.data.stats.pendingReminders)
        }
    }

    useEffect(() => {
        refreshBadge().then()

        // Set interval to refresh every 5 minutes
        const intervalId = setInterval(() => {
            refreshBadge().then();
        }, 5 * 60 * 1000); // 5 minutes in milliseconds

        // Cleanup on component unmount
        return () => {
            clearInterval(intervalId);
        };
    }, [])

    const statsRef = useRef({
        pendingReminders, unreadNotifications
    })
    statsRef.current = {
        pendingReminders, unreadNotifications
    }

    useEffect(() => {
        if (!socket || !isConnected) return

        socket.on("notifications", (d: {
            notifications: Notification[]
        }) => {
            const unread = d.notifications.filter(n => !n.isSeen).length
            setUnreadNotifications(unread + statsRef.current.unreadNotifications)
        })

        // socket.on("notifications", (d: {
        //     notifications: Notification[]
        // }) => {
        //     const unread = d.notifications.filter(n => !n.isSeen).length
        //     setUnreadNotifications(unread + statsRef.current.unreadNotifications)
        // })

        return () => {
            if (!socket) return
            socket.off('notifications')
        }

    }, [isConnected, socket])

    return <>
        <WorkspaceNotificationContext.Provider value={{
            unreadNotifications,
            pendingReminders,
            decrementPendingReminders,
            decrementUnreadNotifications
        }}>
            {props.children}
        </WorkspaceNotificationContext.Provider>

    </>
}


export const useWorkspaceNotification = () => {
    const context = useContext(WorkspaceNotificationContext)
    if (!context) throw new Error('useWorkspaceNotification must be used within a WorkspaceNotificationProvider');
    return context as WorkspaceNotificationContextProps;
}