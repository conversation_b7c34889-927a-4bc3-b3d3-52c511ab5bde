import {type ClassValue, clsx} from "clsx"
import {twMerge} from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function isDesktop(): boolean {
  return typeof window !== 'undefined' && window.innerWidth >= 1024;
}


export function timeAgo(date: Date | string): string {
  const now = new Date();
  const target = typeof date === "string" ? new Date(date) : date;
  const diff = Math.floor((now.getTime() - target.getTime()) / 1000);

  if (diff < 60) return `${diff}s`;
  const minutes = Math.floor(diff / 60);
  if (minutes < 60) return `${minutes}m`;
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h`;
  const days = Math.floor(hours / 24);
  if (days < 7) return `${days}d`;

  const formatter = new Intl.DateTimeFormat("en", { day: "2-digit", month: "short" });
  return formatter.format(target); // e.g. "12 Jun"
}
