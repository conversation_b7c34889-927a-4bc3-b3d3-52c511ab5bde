"use client"

import React, {PropsWith<PERSON>hildren, useCallback, useContext, useEffect, useState} from 'react';
import {firebaseCloudMessaging} from "@/config/firebase";
import {MessagePayload} from "firebase/messaging";

interface FCMContextProps {
    permission: NotificationPermission;
    fcmToken: string | undefined;
    requestPermission: () => Promise<string | undefined | null>;
    notificationDisabled: boolean;
    disableNotification: (value: boolean) => void;
    tokenRegistered: boolean;
    setTokenRegistered: (value: boolean) => void;
}

export const FCMContext = React.createContext<FCMContextProps | null>(null);

export const FCMProvider = (props: PropsWithChildren<{}>) => {
    const [fcmToken, setFcmToken] = useState<string | undefined>(localStorage.getItem('fcm_token') || undefined);
    const [tokenRegistered, setTokenRegistered] = useState(false);
    const [notificationDisabled, setNotificationDisabled] = useState(localStorage.getItem('notificationDisabled') === 'true');

    const getToken = useCallback(async () => {
        let token: string | null = null
        try {
            token = await firebaseCloudMessaging.getToken()
        } catch (e) {
            console.error("Error getting fcm token", e)
        }
        if (!token) return null
        setFcmToken(token);
        return token
    }, [])

    const requestPermission = useCallback(async () => {
        await Notification.requestPermission();
        return await getToken()
    }, [getToken]);

    const onMessage = (payload: MessagePayload) => {
        console.log("On message:", payload)
    }

    useEffect(() => {
        console.log("firebase1")
        const initFCM = async () => {
            await firebaseCloudMessaging.init({onMessage});
            await getToken()
        }
        console.log("firebase2")
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                console.log('event for the service worker', event)
            })
        }
        initFCM().then()
    }, [])

    const disableNotification = (value: boolean) => {
        setNotificationDisabled(value)
        localStorage.setItem('notificationDisabled', String(value))
    }

    useEffect(() => {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                console.log('event for the service worker', event)
            })
        }
        if ("permissions" in navigator) {
            navigator.permissions
                .query({name: "notifications" as PermissionName})
                .then((notificationPerm) => {
                    notificationPerm.onchange = async function () {
                        await getToken();
                    };
                });
        }
    }, [getToken])

    const permission = typeof Notification === 'undefined' ? "denied" : Notification.permission

    return <FCMContext.Provider value={{permission, fcmToken, requestPermission, notificationDisabled, disableNotification, tokenRegistered, setTokenRegistered}}>
        {props.children}
    </FCMContext.Provider>
}


export function useFCM() {
    return useContext(FCMContext) as FCMContextProps;
}