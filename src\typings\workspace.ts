import {User} from "@/typings/user";
import { AccessLevel, Document, PagePermission } from "@/typings/page";
import {Database, Record} from "@/typings/database";
import {Creator, TemplateInstall, TemplateListing} from "@/typings/creator";
import {DropdownOption} from "@opendashboard-inc/integration-core";
import {ViewType} from "opendb-app-db-utils/lib/typings/view";

export interface SearchResult {
    id: string;
    title?: string;
    name?: string;
    content?: string;
    path: string;
    publishedAt?: string;
    viewType?: ViewType;
    source?: {
      databaseId?: string;
      recordId?: string;
      pageId?: string;
      viewId?: string;
      documentId?: string;
      reminderId?: string;
    };
    highlight?: {
      start: number;
      end: number;
    };
    image?: string;
  }

  export interface SearchPagination {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  }

  export interface SearchResponse {
    results: {
      results: SearchResult[];
      pagination: SearchPagination;
    };
  }

export interface Workspace {
    id: string
    name: string
    domain: string
    isSetupCompleted: boolean
    logo: string
    createdById: string
    ownerId: string
    stripeCustomerId: string
    createdAt: string
    updatedAt: string
    deletedAt: string
    isSupportAccessEnabled: boolean
    isFunctionalityLimited: boolean
    timezone: string
    meta?: {}
}

export enum WorkspaceMemberRole {
    Owner = "owner",
    Admin = "admin",
    Member = "member",
    Collaborator = "collaborator",
    SupportUser = "supportUser"
}

export interface WorkspaceMember {
    id: number
    workspaceId: string
    userId: string
    role: WorkspaceMemberRole
    invitedById: string
    createdAt: Date
    updatedAt: Date
    deletedAt?: Date
}

export interface MyWorkspace {
    workspace: Workspace;
    workspaceMember: WorkspaceMember
    membersCount: number
    planId: string
}

export interface MyWorkspaceMember {
    user: User;
    workspaceMember: WorkspaceMember
}

export interface WorkspaceInvitation {
    id: number
    workspaceId: string
    email: string
    userId: string
    role: WorkspaceMemberRole
    token: string
    expiresAt: string
    invitedById: string
    createdAt: string
    updatedAt: string
    deletedAt: string

}

interface UsageInfo {
    limit: number
    usage: number
}

export interface WorkspaceUsage {
    stats: {
        users: UsageInfo
        collaborators: UsageInfo
        records: UsageInfo
        emails: UsageInfo
        senderDomains: UsageInfo
        senderEmails: UsageInfo
        enrichment: UsageInfo
        aiGeneration: UsageInfo
    }
    startsAt: string
    endsAt: string
}

export type SubscriptionCommitment = {
    [key in 'monthToMonth' | 'annual']: {
        costInCents: number
        priceId: string
        // cycles: SubscriptionPaymentCycle
    }
}


export interface WorkspacePlan {
    name: string
    id: string
    description: string
    priceId: string
    usersCount: number
    collaboratorsCount: number
    recordAccessLimit: number
    automationRunsCount: number
    dataHistoryDays: number
    emailsPerHour: number
    commitments: SubscriptionCommitment
    limits: UsageLimits
}

export enum RiskType {
    QuotaExceeded = "quotaExceeded",
    UnpaidInvoice = "unpaidInvoice",
    UnpaidSubscription = "unpaidSubscription"
}

export interface WorkspaceRiskLog {
    workspaceId: string;
    riskType: RiskType;
    isResolved: boolean;
    isClosed: boolean
    startAt: Date
    resolvedAt: Date
    lastNotifiedAt: Date
    notificationCount: number
    createdAt: Date
    updatedAt: Date
    deletedAt: Date
}
export interface MyWorkspaceInvitation {
    invitation: WorkspaceInvitation;
    workspace: Workspace;
}

export interface WorkspaceSenderEmail {
    id: number
    workspaceId: string
    addedByUserId: string
    email: string
    name: string
    isVerified: boolean
    verifiedAt: string
    createdAt: string
    updatedAt: string
    deletedAt: string
}

export enum DomainConfigType {
    DKIM = 'DKIM',
    SPF = 'SPF',
    DMARC = 'DMARC',
}

export enum RecordType {
    CNAME = 'CNAME',
    A = 'A',
    TXT = 'TXT',
    MX = 'MX',
}

export interface DomainConfig {
    type: DomainConfigType
    recordType: RecordType
    host: string
    value: string
}

export interface WorkspaceDomain {
    id: number
    workspaceId: string
    addedByUserId: string
    domain: string
    configs: DomainConfig[]
    isVerified: boolean
    verifiedAt: Date
    createdAt: Date
    updatedAt: Date
    deletedAt: Date

}


export interface UsageLimits {
    users: number
    collaborators: number
    records: number
    emails: number
    senderDomains: number
    senderEmails: number
    enrichment: number
    workflowTask: number
    aiGeneration: number
    dataHistory: number
}

export interface AddOnsQuota {
    users: number
    collaborators: number
    records: number
    sendingDomains: number
    sendingEmails: number
    aiGeneration: number
    workflowTask: number
    enrichment: number
}


export interface BillingCycle {
    id: number;
    workspaceId: string
    stripeSubscriptionId: string
    planId: string
    priceId: string
    anchorDay: number
    futurePriceId: string
    futureStripeSubscriptionId: string
    startsAt: string
    endsAt: string
    endedAt: string
    isActive: boolean
    isPaid: boolean
    isRenewing: boolean
    costInCents: number
    amountPaidInCents: number
    paidAt: string
    createdAt: string
    updatedAt: string
    deletedAt: string
    cyclePlanQuota: UsageLimits
    cycleUsage: UsageLimits
    addOnsQuota: AddOnsQuota
    usersQuota: number
    collaboratorsQuota: number
    recordsQuota: number
    meta?: KeyValueStore

}

export interface WorkspaceStats {
    id: number
    workspaceId: string
    users: number
    collaborators: number
    records: number
    sendingEmails: number
    sendingDomains: number
    createdAt: string
    updatedAt: string
    deletedAt: string
}

export interface PayPerUse {
    enrichment: number
    aiGeneration: number
    email: number
    workspaceTask: number
}

export interface PlanAddOn {
    id: string
    name: string
    priceId: string
    commitments: SubscriptionCommitment
    extra: {
        enrichment: number,
        aiGeneration: number,
        collaborators: number,
        emails: number,
        records: number,
        workflowTasks: number,
    }
}

export interface KeyValueStore {
    [key: string]: object;
}

export enum WorkspaceMemberSettingsKey {
    RestrictedPagesOrder = "restricted-pages-order",
    RestrictedDatabasesOrder = "restricted-databases-order",
    PrivatePagesOrder = "private-pages-order",
    RecordTabsOrder = "record-tabs-order"
}

export enum WorkspaceMetaKey {
    SharedPagesOrder = "shared-pages-order",
    SharedDatabasesOrder = "shared-databases-order"
}

export interface WorkspaceMemberSettings {
    id: number;
    userId: string
    workspaceId: string
    settings: KeyValueStore
    createdAt: string
    updatedAt: string
}

export interface WorkspaceUpload {
    id: number
    userId: string
    workspaceId: string
    creatorId: string
    name: string
    mimeType: string
    type: string
    finalUrl: string
    doSpaceKey: string
    thumbnailUrl: string
    size: number
    width: number
    height: number
    createdAt: string
    updatedAt: string
    deletedAt: string
    meta: object
}

export enum SubscriptionStatus {
    Active = 'active',
    Cancelled = 'canceled',
    Past_Due = 'past_due',
    Unpaid = 'unpaid',
    Incomplete = 'incomplete',
    Incomplete_expired = 'incomplete_expired',
    Trialing = 'trailing',
    Paused = 'paused'
}

export interface Subscription {
    id: number;
    workspaceId: string;
    stripeSubscriptionId: string;
    status: SubscriptionStatus;
    planId?: string;
    priceId?: string;
    anchorDay: number;
    addOnUsers: number;
    startsAt?: string; // ISO 8601 date string
    endsAt?: string;   // ISO 8601 date string
    endedAt?: string;  // ISO 8601 date string
    createdAt: string; // ISO 8601 date string
    updatedAt: string; // ISO 8601 date string
    auditLog?: string[];
    meta?: object;
}

export enum NotifyTime {
    _2Hours = "2_hours",
    _1Day = "1_day",
    _1Week = "1_week",
    _1Month = "1_month",
    _6Months = "6_months",
    _1Year = "1_year",
}


export interface Reminder {
    id: string;
    workspaceId: string;
    title: string;
    description?: string;
    databaseId?: string;
    recordId?: string;
    isResolved: boolean;
    resolvedById?: string;
    resolvedAt?: string; // Date as string
    nextNotifyAt?: string; // Date as string
    notifyDates?: string[]; // Array of dates as strings
    notifyTimes?: (string | NotifyTime)[]; // Mixed array of strings and NotifyTime objects
    assignedToUserIds?: string;
    taggedRecordIds?: string;
    createdById?: string;
    updatedById?: string;
    meta?: KeyValueStore;
    createdAt: string; // Date as string
    updatedAt: string; // Date as string
    deletedAt?: string; // Date as string
}


export interface Notification {
    id: string;
    userId: string;
    workspaceId: string;
    title: string;
    description?: string;
    linkUrl?: string;
    isSeen: boolean;
    seenAt?: string;
    meta?: KeyValueStore;
    createdAt: string;
    updatedAt: string;
    deletedAt?: string;
}

export interface InstalledTemplate {
    install: TemplateInstall
    listing: TemplateListing
    creator: Creator
    versionNumber: number
}

export interface WorkspaceNote {
    document: Document
    record?: Record
    database?: Database
}


export interface WorkspaceNoteWithPermission {
    note: Document,
    accessLevel: AccessLevel,
    permissions: PagePermission[]
}
export interface WorkspaceReminder {
    reminder: Reminder
    record?: Record
    database?: Database
}

export interface NotificationStats {
    unreadNotifications: number
    pendingReminders: number
}

export enum DocumentType {
    Document = 'document',
    Workflow = 'workflow',
    Page = 'page',
}

export interface DocumentHistory {
    id: string;
    workspaceId: string;
    documentId?: string;
    recordId?: string;
    name?: string;
    contentJSON?: object;
    contentText?: string;
    createdById?: string;
    updatedById?: string;
    type: DocumentType
    createdAt: string;
    updatedAt: string;
    deletedAt?: string;
}

export enum SecretType {
    Variable = "variable",
    Secret = "secret"
}

export interface WorkspaceSecret {
    id: number;
    workspaceId: string;
    type: SecretType;
    name: string;
    value?: string;
    createdById: string;
    createdAt: string;
    updatedAt: string;
    deletedAt?: string;
}

export interface ConnectionCredentials {
    [key: string]: string;
}

export interface WorkspaceIntegrationConnection {
    id: string;
    workspaceId: string;
    integration: string;
    name: string;
    credentials?: ConnectionCredentials;
    createdAt: string;
    updatedAt: string;
    deletedAt?: string;
    meta?: KeyValueStore;
}

export type ResolveDropdownResponse = Array<{
    label: string;
    value: string;
}>;
