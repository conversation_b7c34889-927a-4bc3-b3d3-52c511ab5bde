import { httpRequest } from "@/utils/http";
import { Method } from "axios";
import { BackendAPIResponse, normalizeResponse } from "@/api/common";
import { Comment, CommentEnum, CreateThread, Reaction } from "@/typings/comment";

export interface CommentStoreAuth {
  canCreateComment: (metadata?: any) => boolean;
  canUpdateComment: (comment: Comment) => boolean;
  canDeleteComment: (comment: Comment) => boolean;
  canResolveComment: (comment: Comment) => boolean;
  canUnresolveComment: (comment: Comment) => boolean;
  canAddReaction: (comment: Comment, emoji: string) => boolean;
  canDeleteReaction: (comment: Comment, emoji: string) => boolean;
}

export class CustomCommentStore {
  constructor(
    private readonly BASE_URL: string,
    private readonly queryStr: string,
    private readonly headers: Record<string, string>,
    private readonly auth: CommentStoreAuth,
    private readonly userId: string,
    private readonly entityParams: {
      documentId?: string,
      databaseId?: string,
      recordId?: string,
      pageId?: string
    }
  ) {}

  private doRequest = async (path: string, method: Method, body?: any) => {
    const headers = {
      "Content-Type": "application/json",
      ...this.headers,
    };
    const endpoint = `${this.BASE_URL}${path}?${this.queryStr}`;
    const response = await httpRequest(method, endpoint, headers, body);
    const responseData = normalizeResponse(response) as BackendAPIResponse<{ comment: Comment[] | Comment }>;

    if (!response.isSuccess) {
      throw new Error(`Failed to ${method} ${path}: ${response.error}`);
    }

    return responseData.data.data.comment;
  };

  public createComment = async (options: CreateThread) => {
    if (!this.auth.canCreateComment()) {
      throw new Error("Not authorized to create comment");
    }

    const entityIds = this.entityParams
    const reqBody = {
      contentJSON: options.contentJson,
      contentText: options.contentText ,
      position: options.position,
      parentId: options.parentId,
      ...entityIds
    };

    const response = await this.doRequest("/", "POST", reqBody) as Comment;
    console.log({ response });
    return response;
  };

  public createThread = async (options:CreateThread) => {
    return this.createComment(options);
  };

    public addComment = async (options: { contentJson: any, parentId: string}) => {
    return this.createComment({
        contentJson: options.contentJson,
        parentId:options.parentId
    });
  };

    public updateComment = async (options: {
        contentJson: any;
    commentId: string;
  }) => {
    const reqBody = {
      contentJSON: options.contentJson,
    //   contentText: options.comment.metadata?.contentText,
    };

    const response = await this.doRequest(`/${options.commentId}`, "PATCH", reqBody) as Comment;
    console.log({ response });
    return response;
  };

  public deleteComment = async (options: {
    commentId: string;
    threadId?: string;
    softDelete?: boolean;
  }) => {
    const response = await this.doRequest(`/${options.commentId}`, "DELETE");
    console.log({ response });
    return response;
  };

  public deleteThread = async (options: { threadId: string }) => {
    const response = await this.doRequest(`/${options.threadId}`, "DELETE");
    console.log({ response });
    return response;
  };

  public resolveComment = async (options: { commentId: string }) => {
    const reqBody = { isResolved: true };
    const response = await this.doRequest(`/${options.commentId}/resolve`, "post", reqBody) as Comment;
    console.log({ response });
    return response;
  };

  public unresolveComment = async (options: { commentId: string }) => {
    const reqBody = { isResolved: false };
    const response = await this.doRequest(`/${options.commentId}/resolve`, "post", reqBody) as Comment;
    console.log({ response });
    return response;
  };

  public resolveThread = async (options: { threadId: string }) => {
    return this.resolveComment({ commentId: options.threadId });
  };

  public unresolveThread = async (options: { threadId: string }) => {
    return this.unresolveComment({ commentId: options.threadId });
  };

  public addReaction = async (options: {
    commentId: string;
    emoji: string;
    threadId?: string; 
  }) => {
    const reqBody = { reaction: options.emoji };
    const response = await this.doRequest(`/${options.commentId}/reactions`, "POST", reqBody);
    console.log({ response });
    return response;
  };

  public deleteReaction = async (options: {
    commentId: string;
    emoji: string;
    threadId?: string;
  }) => {
    const reqBody = { reaction: options.emoji };
    const response = await this.doRequest(`/${options.commentId}/reactions`, "DELETE", reqBody);
    console.log({ response });
    return response;
  };

  public getComments = async (filters?: {
    documentId?: string;
    pageId?: string;
    databaseId?: string;
    recordId?: string;
    isResolved?: boolean;
    userId?: string;
  }) => {
    let queryParams = this.queryStr;
    
    if (filters) {
      const filterParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          filterParams.append(key, value.toString());
        }
      });
      
      if (filterParams.toString()) {
        queryParams += `&${filterParams.toString()}`;
      }
    }


    const endpoint = `${this.BASE_URL}?${queryParams}`;
    const response = await httpRequest("GET", endpoint, this.headers);
    const responseData = normalizeResponse(response) as BackendAPIResponse<{ comments: Comment[] }>;

    if (!response.isSuccess) {
      throw new Error(`Failed to get comments: ${response.error}`);
    }

    return responseData.data.data.comments as Comment[];
  };

  public getComment = async (commentId: string) => {
    const response = await this.doRequest(`/${commentId}`, "GET") as Comment;
    console.log({ response });
    return response;
  };

  public getThread = async (threadId: string) => {
    return this.getComment(threadId);
  };

  public getThreadComments = async (threadId: string) => {
    return this.getComments({ parentId: threadId } as any);
  };

  // Helper method to check if user can perform actions
  public canCreateComment = (metadata?: any) => {
    return this.auth.canCreateComment(metadata);
  };

  public canUpdateComment = (comment: Comment) => {
    return this.auth.canUpdateComment(comment);
  };

  public canDeleteComment = (comment: Comment) => {
    return this.auth.canDeleteComment(comment);
  };

  public canResolveComment = (comment: Comment) => {
    return this.auth.canResolveComment(comment);
  };

  public canUnresolveComment = (comment: Comment) => {
    return this.auth.canUnresolveComment(comment);
  };

  public canAddReaction = (comment: Comment, emoji: string) => {
    return this.auth.canAddReaction(comment, emoji);
  };

  public canDeleteReaction = (comment: Comment, emoji: string) => {
    return this.auth.canDeleteReaction(comment, emoji);
  };

  // Utility methods for working with reactions
  public hasUserReacted = (comment: Comment, emoji: string): boolean => {
    if (!comment.reactions) return false;
    
    const reaction = comment.reactions.find(r => r.emoji === emoji);
    return reaction ? reaction.userIds.includes(this.userId) : false;
  };

  public getReactionCount = (comment: Comment, emoji: string): number => {
    if (!comment.reactions) return 0;
    
    const reaction = comment.reactions.find(r => r.emoji === emoji);
    return reaction ? reaction.userIds.length : 0;
  };

  public getAllReactions = (comment: Comment): Reaction[] => {
    return comment.reactions || [];
  };

  public isCommentResolved = (comment: Comment): boolean => {
    return comment.isResolved;
  };

  public isCommentDeleted = (comment: Comment): boolean => {
    return !!comment.deletedAt;
  };

  public isCommentByUser = (comment: Comment, userId?: string): boolean => {
    return comment.userId === (userId || this.userId);
  };

//   public getCommentType = (comment: Comment): CommentEnum => {
//     return comment.type || CommentEnum.COMMENT;
//   };

  public isThread = (comment: Comment): boolean => {
    return !comment.parentId;
  };

  public isReply = (comment: Comment): boolean => {
    return !!comment.parentId;
  };
}