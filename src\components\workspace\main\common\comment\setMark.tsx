import {BlockNoteEditor, defaultInlineContentSpecs} from "@blocknote/core"
import { EditorState, TextSelection } from "prosemirror-state";
import {
  initProseMirrorDoc,
  relativePositionToAbsolutePosition,
  updateYFragment,
} from "y-prosemirror";
import * as Y from "yjs";

export function setMark(
    editor: BlockNoteEditor<any,any,any>,
    fragment: Y.XmlFragment,
    doc: Y.Doc,
    yjsSelection: {
      anchor: any;
      head: any;
    },
    markName: string,
    markAttributes: any,
    fallbackText?: string
) {
  console.log("🔍 Starting FIXED setMark");

  doc.transact(() => {
    try {
      const { doc: pNode, meta } = initProseMirrorDoc(
        fragment,
        editor.pmSchema as any
      );

      console.log("📄 Document text content:", pNode.textContent);
      console.log("📄 Document size:", pNode.content.size);

      // Validate that we have a valid mark type
      if (!editor.pmSchema.marks[markName]) {
        console.error(`❌ Mark type '${markName}' not found in schema`);
        return;
      }

      // Try to resolve positions normally first
      let anchor = relativePositionToAbsolutePosition(
        doc,
        fragment,
        yjsSelection.anchor,
        meta.mapping
      );
      let head = relativePositionToAbsolutePosition(
        doc,
        fragment,
        yjsSelection.head,
        meta.mapping
      );

      console.log("📍 Raw positions - Anchor:", anchor, "Head:", head);

      // If positions can't be resolved and we have fallback text, try to find it
      if ((anchor === null || head === null) && fallbackText) {
        console.log("🔄 Trying fallback text search for:", fallbackText);
        const textContent = pNode.textContent;
        const index = textContent.indexOf(fallbackText);
        
        if (index !== -1) {
          // Find the actual position in the document structure
          let pos = 1; // Start after the document node
          let found = false;
          
          pNode.descendants((node, nodePos) => {
            if (found) return false;
            
            if (node.isText && node.text) {
              const textIndex = node.text.indexOf(fallbackText);
              if (textIndex !== -1) {
                anchor = nodePos + textIndex;
                head = anchor + fallbackText.length;
                found = true;
                console.log("✅ Found fallback text at positions:", anchor, "to", head);
                return false;
              }
            }
          });
        }
      }

      if (anchor === null || head === null) {
        console.error("❌ Could not resolve positions even with fallback");
        return;
      }

      // Ensure positions are within bounds and valid
      const maxPos = pNode.content.size;
      
      // Clamp positions to valid range
      anchor = Math.max(0, Math.min(anchor, maxPos));
      head = Math.max(0, Math.min(head, maxPos));

      // Ensure we have a valid selection range
      if (anchor === head) {
        console.warn("⚠️ Empty selection, expanding to at least 1 character");
        if (head < maxPos) {
          head = head + 1;
        } else if (anchor > 0) {
          anchor = anchor - 1;
        } else {
          console.error("❌ Cannot create valid selection");
          return;
        }
      }

      // Ensure proper order (anchor <= head)
      if (head < anchor) {
        [anchor, head] = [head, anchor];
      }

      console.log("📍 Final Positions - Anchor:", anchor, "Head:", head, "Max:", maxPos);

      // Validate positions before creating selection
      try {
        // Check if positions are valid for TextSelection
        if (anchor < 0 || head < 0 || anchor > maxPos || head > maxPos) {
          console.error("❌ Invalid positions for selection");
          return;
        }

        const selection = TextSelection.create(pNode, anchor, head);
        
        const state = EditorState.create({
          doc: pNode,
          schema: editor.pmSchema as any,
          selection: selection,
        });

        console.log("📝 Selected text:", state.doc.textBetween(selection.from, selection.to));

        // Check if selection is empty
        if (selection.from === selection.to) {
          console.warn("⚠️ Empty selection, cannot apply mark");
          return;
        }

        const tr = setMarkInProsemirror(
          editor.pmSchema.marks[markName],
          markAttributes,
          state
        );

        // Validate the transaction
        if (!tr.doc) {
          console.error("❌ Transaction produced invalid document");
          return;
        }

        // Additional check: ensure the document wasn't completely emptied
        if (tr.doc.textContent.length === 0 && pNode.textContent.length > 0) {
          console.error("❌ Transaction emptied the document, reverting");
          return;
        }

        console.log("✅ Applying mark transaction");
        updateYFragment(doc, fragment, tr.doc, meta);

      } catch (selectionError) {
        console.error("❌ Error creating selection:", selectionError);
        console.log("Attempted positions:", { anchor, head, maxPos });
        return;
      }

    } catch (error) {
      console.error("❌ Error in setMark:", error);
      console.warn("⚠️ Skipping this mark due to error");
    }
  }, "comment-mark");
}

export const setMarkInProsemirror = (
  type: any,
  attributes = {},
  state: EditorState
) => {
  let tr = state.tr;
  const { selection } = state;
  const { ranges } = selection;

  console.log("🎯 Applying mark to ranges:", ranges.length);

  ranges.forEach((range, rangeIndex) => {
    const from = range.$from.pos;
    const to = range.$to.pos;

    console.log(`📍 Range ${rangeIndex}: from ${from} to ${to}`);

    if (from === to) {
      console.warn(`⚠️ Empty range ${rangeIndex}, skipping`);
      return;
    }

    state.doc.nodesBetween(from, to, (node, pos) => {
      if (!node.isText) {
        return; // Skip non-text nodes
      }

      const trimmedFrom = Math.max(pos, from);
      const trimmedTo = Math.min(pos + node.nodeSize, to);

      if (trimmedFrom >= trimmedTo) {
        return; // Skip if no content to mark
      }

      const existingMark = node.marks.find((mark) => mark.type === type);

      // if there is already a mark of this type
      // we know that we have to merge its attributes
      // otherwise we add a fresh new mark
      if (existingMark) {
        console.log("🔄 Merging with existing mark");
        // Remove the old mark first, then add the new one
        tr = tr.removeMark(trimmedFrom, trimmedTo, type);
        tr = tr.addMark(
          trimmedFrom,
          trimmedTo,
          type.create({
            ...existingMark.attrs,
            ...attributes,
          })
        );
      } else {
        console.log("✨ Adding new mark");
        tr = tr.addMark(trimmedFrom, trimmedTo, type.create(attributes));
      }
    });
  });

  console.log("✅ Mark application complete");
  return tr;
};