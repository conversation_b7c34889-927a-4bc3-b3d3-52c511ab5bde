import React, { useMemo, useState, useCallback } from 'react';
import { format, isToday, setHours } from 'date-fns';
import { cn } from '@/lib/utils';
import { CalendarEvent } from '@/typings/page';
import { CalendarEventSegment } from '@/components/workspace/main/views/calendar/components/eventsegment';
import { NoEvents } from '@/components/workspace/main/views/calendar/components/noevents';
import {eventsToSegments, getSegmentsForDay, getSegmentHeight, getSegmentTopOffset, getAllDaySegments, getTimeSlotSegments} from '@/utils/multiDay';
import { calculateLayout } from '@/utils/eventCollision';
import { AllDayRow } from '@/components/workspace/main/views/calendar/components/allday';
import { useDroppable } from '@dnd-kit/core';


interface DayViewProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  openAddEventForm: (date: Date) => void;
  canEditData: boolean;
  savedScrollTop: React.MutableRefObject<number>;
  handleEventClick: (event: CalendarEvent) => void;
  activeDragData: any;
}

const TimeSlot = ({ 
  hour, 
  date, 
  onDoubleClick, 
  children,
  isDragging
}: {
  hour: number;
  date: Date;
  onDoubleClick: (minute: number) => void;
  children: React.ReactNode;
  isDragging: boolean;
}) => {
  const [isHovering, setIsHovering] = useState(false);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);
  const [currentMinute, setCurrentMinute] = useState<number>(0);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const shouldShowPrecision = isHovering || isDragging;

  const calculateMinuteFromPosition = useCallback((clientY: number) => {
    if (!containerRef.current) return 0;
    
    const rect = containerRef.current.getBoundingClientRect();
    const y = clientY - rect.top;
    const minute = Math.floor((y / rect.height) * 60);
    return Math.max(0, Math.min(59, minute));
  }, []);

  const handleMouseEnter = useCallback(() => {
    setIsHovering(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (!isDragging) {
      setIsHovering(false);
      setMousePosition(null);
      setCurrentMinute(0);
    }
  }, [isDragging]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const minute = calculateMinuteFromPosition(e.clientY);
    setMousePosition({ x: e.clientX, y: e.clientY });
    setCurrentMinute(minute);
  }, [calculateMinuteFromPosition]);

  React.useEffect(() => {
    if (!isDragging) return;

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        // Check if mouse is over this time slot
        if (e.clientX >= rect.left && e.clientX <= rect.right && 
            e.clientY >= rect.top && e.clientY <= rect.bottom) {
          const minute = calculateMinuteFromPosition(e.clientY);
          setMousePosition({ x: e.clientX, y: e.clientY });
          setCurrentMinute(minute);
          setIsHovering(true);
        }
      }
    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    return () => document.removeEventListener('mousemove', handleGlobalMouseMove);
  }, [isDragging, calculateMinuteFromPosition]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    if (e.detail === 2) { // Double click
      onDoubleClick(currentMinute);
    }
  }, [currentMinute, onDoubleClick]);

  return (
    <div
      ref={containerRef}
      className="flex-1 relative min-h-[60px] cursor-pointer"
      style={{ height: '60px' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      onClick={handleClick}
    >
      {/* Single droppable zone for the entire hour */}
      <HourDropZone 
        date={date} 
        hour={hour} 
        currentMinute={currentMinute}
        isActive={shouldShowPrecision}
      />
      
      {children}
    </div>
  );
};

// Optimized single drop zone per hour with minute precision
const HourDropZone = ({
  date,
  hour,
  currentMinute,
  isActive
}: {
  date: Date;
  hour: number;
  currentMinute: number;
  isActive: boolean;
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `hour-${format(date, 'yyyy-MM-dd')}-${hour}`,
    data: {
      date: date,
      hour,
      minute: currentMinute, // Use the calculated minute from mouse position
      type: 'timeslot-minute'
    }
  });

  return (
    <div
      ref={setNodeRef}
      className="absolute inset-0 w-full h-full"
    />
  );
};

export const DayView: React.FC<DayViewProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  openAddEventForm,
  canEditData,
  savedScrollTop,
  handleEventClick,
  activeDragData,
}) => {
  const hours = Array.from({ length: 24 }, (_, i) => i);
  
  // Memoize event segments to prevent unnecessary recalculations
  const daySegments = useMemo(() => {
    const allSegments = eventsToSegments(events);
    return getSegmentsForDay(allSegments, selectedDate);
  }, [events, selectedDate]);

  // Separate all-day and time-slot segments
  const allDaySegments = useMemo(() => getAllDaySegments(daySegments), [daySegments]);
  const timeSlotSegments = useMemo(() => getTimeSlotSegments(daySegments), [daySegments]);

  // Calculate layout for overlapping segments
  const { segmentLayouts } = useMemo(() => {
    return calculateLayout(timeSlotSegments);
  }, [timeSlotSegments]);

  // Memoize current time position
  const currentTimePosition = useMemo(() => 
    isToday(selectedDate) 
      ? {
          hour: new Date().getHours(),
          minutes: new Date().getMinutes()
        } 
      : null, 
    [selectedDate]
  );

  // Render empty state when no events
  const renderEmptyState = () => (
    <NoEvents
      title="No events scheduled"
      message={isToday(selectedDate)
        ? "You have a free day ahead! Add an event to get started."
        : `${format(selectedDate, 'EEEE, MMMM d')} is completely free.`}
      showCreateButton={canEditData}
      onCreate={() => {
        const newDate = new Date(selectedDate);
        newDate.setHours(9, 0, 0, 0);
        openAddEventForm(newDate);
      }}
    />
  );

  // Render time slots with events
  const renderTimeSlots = () => (
    <div className="flex-1 overflow-auto relative bg-white" id="day-view-container">
      {hours.map((hour, i) => (
        <div 
          key={i} 
          className={cn(
            "flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors",
            i === hours.length - 1 && "border-b-neutral-300"
          )} 
          style={{ height: '60px' }}
        >
          {/* Time Label */}
          <div 
            data-time-labels="true"
            className="flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20"
          >
            <div className="text-right">
              <div className="text-xs font-semibold">
                {format(setHours(selectedDate, hour), 'h')}
              </div>
              <div className="text-xs text-black opacity-60">
                {format(setHours(selectedDate, hour), 'a')}
              </div>
            </div>
          </div>

          {/* Time Slot */}
          <TimeSlot
            hour={hour}
            date={selectedDate}
            isDragging={!!activeDragData}
            onDoubleClick={(minute) => {
              if (canEditData) {
                const newDate = new Date(selectedDate);
                newDate.setHours(hour, minute, 0, 0);
                openAddEventForm(newDate);
              }
            }}
          >
            {segmentLayouts.map((layout) => {
              const segmentStart = layout.segment.startTime;
              const isFirstHour = segmentStart.getHours() === hour;

              if (!isFirstHour) return null;

              const segmentHeight = getSegmentHeight(layout.segment);
              const topOffset = getSegmentTopOffset(layout.segment);

              return (
                <CalendarEventSegment
                  key={layout.segment.id}
                  segment={layout.segment}
                  style={{
                    height: `${segmentHeight}px`,
                    position: 'absolute',
                    top: `${topOffset}px`,
                    left: `${layout.left}%`,
                    width: `${layout.width}%`,
                    zIndex:
                      activeDragData?.payload?.id === layout.segment.id ? 50 : 
                      layout.zIndex,
                    paddingRight: '2px', // Add small gap between columns
                    border: layout.hasOverlap ? '1px solid white' : 'none', // White border for overlapping events
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    const container = document.getElementById('day-view-container');
                    if(container) {
                      savedScrollTop.current = container.scrollTop;
                    }
                    setSelectedEvent(layout.segment.originalEventId);
                    handleEventClick(layout.segment.originalEvent);
                  }}
                  view="day"
                  isDragging={activeDragData?.payload?.id === layout.segment.id}
                />
              );
            })}
          </TimeSlot>
        </div>
      ))}

      {/* Current Time Indicator */}
      {currentTimePosition && (
        <div
          className="absolute flex items-center z-30 pointer-events-none left-14 lg:left-20"
          style={{
            top: `${(currentTimePosition.hour + currentTimePosition.minutes / 60) * 60}px`,
            right: '4px'
          }}
        >
          <div className="w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5" />
          <div className="flex-1 border-t-2 border-red-500 shadow-sm" />
        </div>
      )}
    </div>
  );

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0">
        <div className="font-semibold text-black mb-1 text-xs">
          {format(selectedDate, 'EEEE')}
        </div>
        <div className={cn(
          "inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full",
          isToday(selectedDate)
            ? "bg-black text-white"
            : "text-black hover:bg-neutral-100"
        )}>
          {format(selectedDate, 'd')}
        </div>
      </div>

      {/* All-Day Section */}
      {daySegments.length > 0 && (
        <AllDayRow
          selectedDate={selectedDate}
          segments={allDaySegments}
          selectedEvent={selectedEvent}
          setSelectedEvent={setSelectedEvent}
          handleEventClick={handleEventClick}
          canEditData={canEditData}
          openAddEventForm={openAddEventForm}
          view="day"
          activeDragData={activeDragData}
        />
      )}

      {/* Main Content */}
      {daySegments.length === 0 
        ? renderEmptyState() 
        : renderTimeSlots()}
    </div>
  );
};