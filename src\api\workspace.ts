import {apiUrl, BackendAPIResponse, normalizeResponse, UploadCallback, UploadFile} from "@/api/common";
import {httpRequest, UpstreamApiResponse} from "@/utils/http";
import {
    BillingCycle,
    DocumentHistory,
    DocumentType,
    InstalledTemplate,
    MyWorkspace,
    MyWorkspaceInvitation,
    MyWorkspaceMember,
    Notification,
    NotificationStats,
    PayPerUse,
    PlanAddOn, ResolveDropdownResponse,
    SearchResponse,
    SecretType,
    Subscription,
    WorkspaceDomain,
    WorkspaceIntegrationConnection,
    WorkspaceInvitation,
    WorkspaceMemberRole,
    WorkspaceMemberSettings,
    WorkspaceNote,
    WorkspaceNoteWithPermission,
    WorkspacePlan,
    WorkspaceReminder,
    WorkspaceRiskLog,
    WorkspaceSecret,
    WorkspaceSenderEmail,
    WorkspaceStats,
    WorkspaceUpload
} from "@/typings/workspace";
import {User} from "@/typings/user";
import { AccessLevel, DocumentNotificationType, Page, PagePermission } from "@/typings/page";
import {GetMyTemplatePurchasesParams} from "@/api/template";
import {queryObjectToString} from "@/api/admin";
import {PaymentCurrency} from "@/typings/creator";
import { CampaignAttachment } from "@/typings/campaign";


export const searchWorkspaces = async (token: string, workspaceId: string, query: string, page: number = 1, pageSize: number = 25) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/search?query=${encodeURIComponent(query)}&page=${page}&pageSize=${pageSize}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<SearchResponse>;
};

export const getWorkspaces = async (token: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        workspaces: MyWorkspace[];
    }>;
};

export const createWorkspace = async (token: string, body: { name: string, domain: string }) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ workspace: MyWorkspace }>;
};

export interface OnboardingDetails {
    myselfOrCompany?: string
    orgUseCase?: string
    orgType?: string
    orgSize?: string
    orgRole?: string
    orgPhoneNumber?: string
    getStartedFrom?: string
    name?: string
    domain?: string
    logo?: string
    teamEmails?: string
    gettingStarted_Templates?: string[]
    gettingStarted_Code?: string
    referralCode?: string
}

export const createWorkspaceViaOnboarding = async (token: string, body: OnboardingDetails) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/v2`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ workspace: MyWorkspace, page: Page }>;
};

export const getWorkspace = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ workspace: MyWorkspace; }>;
};

export const updateWorkspace = async (token: string, id: string, body: { name: string, timezone: string }) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}`;
    const response = await httpRequest("patch", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ workspace: MyWorkspace; }>;
};

export const getWorkspaceMembers = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/members`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        members: MyWorkspaceMember[],
        invitations: WorkspaceInvitation[]
    }>;
};

export const inviteWorkspaceMember = async (token: string, id: string, body: {
    email: string,
    role: WorkspaceMemberRole
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/members`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ invitation: WorkspaceInvitation }>;
};

export const getCustomerPortalUrl = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/customer-portal`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ url: string }>;
};

export const getCheckoutSessionUrl = async (token: string, id: string, planId: string, priceId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/checkout-session?planId=${planId}&priceId=${priceId}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ url: string }>;
};

export const deleteFutureSubscription = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/future-subscription`;
    const response = await httpRequest("delete", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ url: string }>;
};

export const deleteSubscription = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/subscription`;
    const response = await httpRequest("delete", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ url: string }>;
};

export const updateWorkspaceLogo = (token: string, workspaceId: string, file: File, callback: UploadCallback) => {
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/logo`;
    const oldCb = callback.onComplete
    callback.onComplete = (res: UpstreamApiResponse) => {
        const response = normalizeResponse(res) as BackendAPIResponse<{ workspace: MyWorkspace }>
        oldCb(response)
    }
    UploadFile('patch', endpoint, token, 'file', file, callback)
}

export const deleteWorkspace = async (token: string, workspaceId: string, body: { reason: string }) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}`;
    const response = await httpRequest("delete", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ user: User }>;
};

export const completeWorkspaceSetup = async (token: string, workspaceId: string, body: {
    orgUseCase: string,
    orgType: string,
    orgSize: string
    orgPhoneNumber: string
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/complete-setup`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ user: User }>;
};

export const switchToWorkspace = async (token: string, workspaceId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/switch-to`;
    const response = await httpRequest("post", endpoint, headers, {});
    return normalizeResponse(response) as BackendAPIResponse<{ user: User }>;
};

export const updateWorkspaceSupportAccess = async (token: string, workspaceId: string, body: { enable: boolean }) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/support-access`;
    const response = await httpRequest("patch", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ user: User }>;
};

export const getWorkspaceUsage = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/usage`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        stats: WorkspaceStats,
        billingCycle: BillingCycle,
        availableCreditInCents: number,
        subscription: Subscription
    }>;
};
export const getWorkspaceRiskLog = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/risklog`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        risklog: WorkspaceRiskLog[]
    }>;
};


export interface ModifyAddOnsData {
    addOnUsers: number
}

export const modifyAddOns = async (token: string, id: string, data: ModifyAddOnsData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/modify-addons`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{
        billingCycle: BillingCycle,
        subscription: Subscription
    }>;
};

export const getPlans = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/plans`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        plans: { [key: string]: WorkspacePlan },
        addOns: { [key: string]: PlanAddOn },
        payPerUsePricing: PayPerUse,
        USDNGNRate: number
        discountPercentage: number
        discountMonthsRemaining: number
    }>;
};

export const purchaseWorkspaceCredit = async (token: string, id: string, body: { amountInCents: number, currency?: PaymentCurrency }) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/purchase-credit`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ checkoutUrl?: string }>;
};

export const deleteInvitation = async (token: string, id: string, inviteId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/invitations/${inviteId}`;
    const response = await httpRequest("delete", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const resendInvitation = async (token: string, id: string, inviteId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/invitations/${inviteId}/resend`;
    const response = await httpRequest("post", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const removeWorkspaceMember = async (token: string, id: string, body: { userId: string }) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/members`;
    const response = await httpRequest("delete", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const updateWorkspaceMember = async (token: string, id: string, body: {
    userId: string,
    role: WorkspaceMemberRole
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/members`;
    const response = await httpRequest("patch", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const makeWorkspaceMemberOwner = async (token: string, id: string, userId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/members/${userId}/make-owner`;
    const response = await httpRequest("post", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const getInvitation = async (token: string, id: string, inviteToken: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/invitation?token=${inviteToken}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ invitation: MyWorkspaceInvitation }>;
}

export const acceptInvitation = async (token: string, id: string, inviteToken: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/invitation/accept?token=${inviteToken}`;
    const response = await httpRequest("post", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ workspace: MyWorkspace }>;
}

export const declineInvitation = async (token: string, id: string, inviteToken: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/invitation/decline?token=${inviteToken}`;
    const response = await httpRequest("post", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
}

export const getWorkspaceSenders = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/senders`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        senders: WorkspaceSenderEmail[],
        domains: WorkspaceDomain[]
    }>;
};

export const addWorkspaceSender = async (token: string, id: string, body: { email: string, name: string }) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/senders`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ sender: WorkspaceSenderEmail, domain: WorkspaceDomain }>;
};

export const verifyWorkspaceDomain = async (token: string, id: string, domainId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/domains/${domainId}/verify`;
    const response = await httpRequest("post", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const resendWorkspaceSenderVerificationEmail = async (token: string, id: string, senderId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/senders/${senderId}/prompt-verification`;
    const response = await httpRequest("post", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const deleteWorkspaceSenderEmail = async (token: string, id: string, senderId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/senders/${senderId}`;
    const response = await httpRequest("delete", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const deleteWorkspaceDomain = async (token: string, id: string, domainId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/domains/${domainId}`;
    const response = await httpRequest("delete", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const getMemberSettings = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/settings`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ settings: WorkspaceMemberSettings }>;
};

export const inviteToPage = async (token: string, id: string, pageId: string, body: {
    emails: string[]
    accessLevel: AccessLevel
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    // /workspaces/{id}/pages/{pageId}/permissions
    const endpoint = `${apiUrl()}/workspaces/${id}/pages/${pageId}/permissions`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ permissions: PagePermission[] }>;
};

export const updatePagePermission = async (token: string, id: string, pageId: string, body: {
    userId: string
    accessLevel: AccessLevel
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/pages/${pageId}/permissions`;
    const response = await httpRequest("patch", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const revokePagePermission = async (token: string, id: string, pageId: string, body: {
    userIds: string[]
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/pages/${pageId}/permissions`;
    const response = await httpRequest("delete", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const uploadFile = (token: string, id: string, file: File, callback: UploadCallback) => {
    const endpoint = `${apiUrl()}/workspaces/${id}/uploads`;
    const oldCb = callback.onComplete
    callback.onComplete = (res: UpstreamApiResponse) => {
        const response = normalizeResponse(res) as BackendAPIResponse<{ upload: WorkspaceUpload }>
        oldCb(response)
    }
    UploadFile('post', endpoint, token, 'file', file, callback)
}

export const uploadRecordCoverImage = (token: string, workspaceId: string, recordId: string, databaseId: string, file: File, callback: UploadCallback) => {
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases/${databaseId}/records/${recordId}/cover-image`;
    const oldCb = callback.onComplete
    callback.onComplete = (res: UpstreamApiResponse) => {
        const response = normalizeResponse(res) as BackendAPIResponse<{upload:WorkspaceUpload}>
        oldCb(response)
    }
    UploadFile('patch', endpoint, token, 'file', file, callback)
}

export const uploadRecordProfileImage = (token: string, workspaceId: string, recordId: string, databaseId: string, file: File, callback: UploadCallback) => {
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/databases/${databaseId}/records/${recordId}/image`;
    const oldCb = callback.onComplete
    callback.onComplete = (res: UpstreamApiResponse) => {
        const response = normalizeResponse(res) as BackendAPIResponse<{upload:WorkspaceUpload}>
        oldCb(response)
    }
    UploadFile('patch', endpoint, token, 'file', file, callback)
}

export const suggestImportMapping = async (token: string, id: string, body: {
    databaseId: string
    importPreview: string[][]
}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/suggest-import-map`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{ jsonStr: string }>;
};

export const getInstalledTemplates = async (token: string, id: string, params: GetMyTemplatePurchasesParams = {}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const queryStr = queryObjectToString(params)
    const endpoint = `${apiUrl()}/workspaces/${id}/templates?${queryStr}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ installedTemplates: InstalledTemplate[] }>;
};

export interface GetNotificationsParams {
    page?: number
    perPage?: number
    filter?: 'unread' | 'read'
}

export const getNotifications = async (token: string, id: string, params: GetNotificationsParams = {}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const queryStr = queryObjectToString(params)
    const endpoint = `${apiUrl()}/workspaces/${id}/notifications?${queryStr}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ notifications: Notification[] }>;
};

export interface UpdateNotificationData {
    id: string
    isSeen: boolean
}

export const updateNotification = async (token: string, id: string, data: UpdateNotificationData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/notifications`;
    const response = await httpRequest("patch", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ notifications: Notification[] }>;
};

export interface CreateNotificationData {
    userId: string;
    title: string;
    message?: string;
    link?: string;
}

export interface NotificationData {
    title: string,
    message?: string,
    image?: string,
    link?: string
}

export interface CreateNotificationData {
    userId: string,
    workspaceId: string,
    options: NotificationData
}

export const createNotification = async (token: string, id: string, data: CreateNotificationData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
 
    const endpoint = `${apiUrl()}/workspaces/${id}/notifications/create`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ notification: Notification }>;
};

export interface SendWorkspaceDirectEmailData {
    senderId: string;
    subject: string;
    to: string;
    cc?: string;
    bcc?: string;
    body: string;
}

export interface SendWorkspaceDirectEmailResponse {
    messageId: string;
    success: boolean;
}

export const sendDirectEmail = async (token: string, workspaceId: string, data: SendWorkspaceDirectEmailData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/send-email`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<SendWorkspaceDirectEmailResponse>;
};

export interface PaginationParams {
    page?: number
    perPage?: number
    query?: string
}

export interface GetNotesParams extends PaginationParams {
    databaseId?: string
    recordId?: string
    noteId?: string
    type?: 'record' | 'user'
}

export const getNotes = async (token: string, id: string, params: GetNotesParams = {}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const queryStr = queryObjectToString(params)
    const endpoint = `${apiUrl()}/workspaces/${id}/notes?${queryStr}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ notes: WorkspaceNote[] }>;
};




export interface CreateNoteData {
    name?: string
    recordId?: string | null
    databaseId?: string | null
}

export const createNote = async (token: string, id: string, data: CreateNoteData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/notes`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ note: WorkspaceNote }>;
};

export interface DeleteNoteParams {
    id: string
}

export const deleteNote = async (token: string, id: string, params: DeleteNoteParams) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/notes`;
    const response = await httpRequest("delete", endpoint, headers, params);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};


export interface UpdateNoteParams {
    id: string
    name?: string,
}

export const updateNote = async (token: string, id: string, params: UpdateNoteParams) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/notes`;
    const response = await httpRequest("patch", endpoint, headers, params);
    return normalizeResponse(response) as BackendAPIResponse<{ note: WorkspaceNote }>;
};
export interface UpdateDocumentNotificationParams {
    documentId?: string,
    notification: DocumentNotificationType
}

export const updateDocumentNotification = async (token: string, id: string, params: UpdateDocumentNotificationParams) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/document-settings`;
    const response = await httpRequest("patch", endpoint, headers, params);
    return normalizeResponse(response) as BackendAPIResponse<{ note: WorkspaceNote }>;
};


export interface ShareNote {
    id: string,
    emails: string[],
    accessLevel: AccessLevel
}
export const ShareNote = async (token: string, id: string, data: ShareNote) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/notes/${data.id}/share`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ note: WorkspaceNote }>;
};
export const getNotePermission = async (token: string, id: string, noteId: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/notes/${noteId}/permissions`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ permission: WorkspaceNoteWithPermission }>;
};
export const RemoveNotePermssion = async (token: string, id: string, noteId: string, data: { userId: string, accessLevel: "none" }) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/notes/${noteId}/permissions`;
    const response = await httpRequest("delete", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const getNotificationStats = async (token: string, id: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/notifications/stats`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ stats: NotificationStats }>;
};


export interface GetReminderParams extends PaginationParams {
    id?: string
    databaseId?: string
    recordId?: string
    type?: 'record' | 'user'
    filter?: 'open' | 'resolved'
}

export const getReminders = async (token: string, id: string, params: GetReminderParams = {}) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const queryStr = queryObjectToString(params)
    const endpoint = `${apiUrl()}/workspaces/${id}/reminders?${queryStr}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ reminders: WorkspaceReminder[] }>;
};


export interface CreateReminderData {
    title: string
    description?: string
    databaseId?: string | null
    recordId?: string | null
    notifyDates: string[]
    assignedToUserIds?: string[]
}

export const createReminder = async (token: string, id: string, data: CreateReminderData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/reminders`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ reminder: WorkspaceReminder }>;
};

export interface UpdateReminderData extends CreateReminderData {
    id: string
}

export const updateReminder = async (token: string, id: string, data: UpdateReminderData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/reminders`;
    const response = await httpRequest("patch", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ reminder: WorkspaceReminder }>;
};

export interface DeleteReminderParams {
    id: string
}

export const deleteReminder = async (token: string, id: string, data: DeleteReminderParams) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/reminders`;
    const response = await httpRequest("delete", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export interface MarkReminderResolvedData {
    resolved?: boolean
}

export const resolveReminder = async (token: string, id: string, reminderId: string, data: MarkReminderResolvedData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/reminders/${reminderId}/resolve`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};


export interface GetDocumentHistoryParams extends PaginationParams {
    documentId: string
    recordId?: string
    createdById?: string
    type?: DocumentType
}

export const getDocumentHistory = async (token: string, id: string, params: GetDocumentHistoryParams) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const queryStr = queryObjectToString(params)
    const endpoint = `${apiUrl()}/workspaces/${id}/document-history?${queryStr}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ histories: DocumentHistory[] }>;
};

export interface GetSecretsParams {
    type: SecretType
}

export const getSecrets = async (token: string, id: string, params: GetSecretsParams) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const queryStr = queryObjectToString(params)
    const endpoint = `${apiUrl()}/workspaces/${id}/secrets?${queryStr}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ secrets: WorkspaceSecret[] }>;
};


export interface CreateWorkspaceSecretData extends Pick<WorkspaceSecret, 'name' | 'value' | 'type'> {}

export const createSecret = async (token: string, id: string, data: CreateWorkspaceSecretData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/secrets`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ secret: WorkspaceSecret }>;
};

export interface UpdateWorkspaceSecretData extends CreateWorkspaceSecretData {
    secretId: number
}

export const updateSecret = async (token: string, id: string, data: UpdateWorkspaceSecretData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/secrets`;
    const response = await httpRequest("patch", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ secret: WorkspaceSecret }>;
};


export interface DeleteWorkspaceSecretData {
    secretId: number
    type: SecretType
}

export const deleteSecret = async (token: string, id: string, data: DeleteWorkspaceSecretData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/secrets`;
    const response = await httpRequest("delete", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
};

export const fetchIntegrationConnections = async (token: string, id: string, integration: string) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/integrations/${integration}/connections`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{ connections: WorkspaceIntegrationConnection[] }>;
}

export interface SaveWorkspaceIntegrationConnectionData {
    connectionId?: string
    credentials: Record<string, string>
    name: string
}

export const saveIntegrationConnection = async (token: string, id: string, integration: string, data: SaveWorkspaceIntegrationConnectionData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/integrations/${integration}/connections`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ connection: WorkspaceIntegrationConnection }>;
}

export interface DeleteWorkspaceIntegrationConnectionsData {
    connectionId: string
}

export const deleteIntegrationConnection = async (token: string, id: string, integration: string, data: DeleteWorkspaceIntegrationConnectionsData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/integrations/${integration}/connections`;
    const response = await httpRequest("delete", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{}>;
}

export interface StartWorkspaceIntegrationOAuth2RedirectData {
    clientId?: string
    clientSecret?: string
    name: string
    connectionId?: string
}

export const startIntegrationOAuth2Redirect = async (token: string, id: string, integration: string, data: StartWorkspaceIntegrationOAuth2RedirectData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/integrations/${integration}/oauth2/redirect`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ url: string }>;
}

export interface GetWorkspaceIntegrationOptionsData {
    propKey: string
    action?: string
    trigger?: string
    connectionId?: string
    propsValue?: Record<string, any>
}

export const getIntegrationDropdownOptions = async (token: string, id: string, integration: string, data: GetWorkspaceIntegrationOptionsData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/integrations/${integration}/options`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ options: ResolveDropdownResponse}>;
}

export interface ExecuteWorkspaceIntegrationActionData {
    name: string
    connectionId?: string
    propsValue?: Record<string, any>
    mode: 'run' | 'test',
    type: 'action' | 'trigger'
    payload?: any
}

export const executeIntegrationAction = async (token: string, id: string, integration: string, data: ExecuteWorkspaceIntegrationActionData) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${id}/integrations/${integration}/${data.type}`;
    const response = await httpRequest("post", endpoint, headers, data);
    return normalizeResponse(response) as BackendAPIResponse<{ result: any }>;
}