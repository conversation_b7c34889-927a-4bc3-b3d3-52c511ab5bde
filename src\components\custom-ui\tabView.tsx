import React, {<PERSON>actNode, useState} from "react";
import {<PERSON><PERSON><PERSON><PERSON>, ScrollBar} from "@/components/ui/scroll-area";
import {Button} from "@/components/ui/button";
import {cn} from "@/lib/utils";

export interface Tab {
    id: string
    title: ReactNode
    content: ReactNode
    scroll?: boolean
}

export interface TabViewProps {
    tabs: Tab[]
    defaultTab?: string
    activeTab?: string | null
    onTabChange?: (tabId: string) => void
    tabTitleExtra?: ReactNode
    className?: string
    tabSwitcherClassName?: string
    tabTitleClassName?: string
    tabContentClassName?: string
    tabHeaderClassName?: string
}

export const TabView = (props: TabViewProps) => {
    const {
        tabs, defaultTab,
        activeTab,
        onTabChange,
        tabTitleClassName,
        tabTitleExtra,
        className,
        tabSwitcherClassName,
        tabHeaderClassName,
        tabContentClassName,
    } = props
    const [internalActiveTab, setInternalActiveTab] = useState(defaultTab || (tabs.length > 0 ? tabs[0].id : ''));

 
    const currentTabId = activeTab !== undefined && activeTab !== null ? activeTab : internalActiveTab;
    
    const handleTabChange = (tabId: string) => {
        if (onTabChange) {
            onTabChange(tabId)
            // if(onTabChange) {
            //     onTabChange?.(tabId)
            // } else setTab(tab)
            
        } else {
            setInternalActiveTab(tabId)
        }
    }

    return (
        <>
            <div className={cn("flex-1 flex flex-col h-full overflow-hidden tabView", className || '')}>
                <TabViewHeader {...props} onTabChange={handleTabChange} tab={currentTabId}/>
                <div className={cn("flex-1 !max-w-full !max-h-full overflow-hidden", tabContentClassName || '')}>
                    {tabs.filter(t => t.id === currentTabId).map(t =>
                        <div key={t.id} className="w-full h-full overflow-hidden">
                            {t.scroll ? <>
                                          <ScrollArea className="w-full h-full scrollBlockChild">
                                              {t.content}
                                          </ScrollArea>
                                      </> :
                             <>{t.content}</>}

                        </div>)}
                </div>
            </div>
        </>
    )
}

interface TabViewHeaderProps {
    tabs: Tab[]
    tab?: string | null
    onTabChange: (tab: string) => void
    tabTitleExtra?: ReactNode
    tabSwitcherClassName?: string
    tabTitleClassName?: string
    tabHeaderClassName?: string
}

export const TabViewHeader = (props: TabViewHeaderProps) => {
    const {
        tabs,
        tabTitleClassName,
        tabTitleExtra,
        tabSwitcherClassName,
        tabHeaderClassName,
        tab
    } = props

    return <>
        <div className={cn("h-12 w-full overflow-hidden flex items-center border-b border-neutral-300 gap-1", tabHeaderClassName || '')}>
            <div className="flex-1 overflow-hidden min-w-0 h-full">
                <div className="w-full max-w-full h-full overflow-hidden">
                    <ScrollArea className="w-full max-w-full h-full">
                        <div
                            className={cn(`flex gap-1 p-2 px-4 pr-10 h-full items-center`, tabSwitcherClassName || '')}>
                            {tabs.map(t => {
                                return <Button variant="ghost"
                                               onClick={e => props.onTabChange(t.id)}
                                               key={t.id}
                                               className={cn(`text-xs rounded-full p-1.5 !px-3 h-auto gap-1 ${tab === t.id && "bg-neutral-200"}`, tabTitleClassName || '')}>
                                    {t.title}
                                </Button>
                            })}
                        </div>
                        <ScrollBar orientation="horizontal"/>
                    </ScrollArea>
                </div>
            </div>
            {tabTitleExtra && <>{tabTitleExtra}</>}
        </div>
    </>
}