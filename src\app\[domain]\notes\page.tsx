"use client"

import React, { useEffect, useRef, useState } from "react";
import { MainContentLayout } from "@/components/workspace/main/common/mainContentLayout";
import { EditReminderDialog, WorkspaceReminderCallback, WorkspaceReminders } from "@/components/workspace/main/common/workspaceReminders";
import { WorkspaceNote, WorkspaceReminder } from "@/typings/workspace";
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { CheckIcon } from "@radix-ui/react-icons";
import { NotesRef, WorkspaceNotes } from "@/components/workspace/main/common/workspaceNotes";
import { createNote } from "@/api/workspace";
import { useAuth } from "@/providers/user";
import { useAlert } from "@/providers/alert";
import { ItemDataLoad } from "@/api/common";
import { useWorkspace } from "@/providers/workspace";

const Page = () => {
    const [isCreating, setIsCreating] = useState(false)
    const noteRef = useRef<NotesRef>(null);
    
    useEffect(() => {
        document.title = 'Notes'
    }, [])

     const handleCreateNote = () => {
    noteRef.current?.createNewNote();
  };
  
    return <>
        <MainContentLayout title={`Notes`}
            titleRightContent={<>
               <div className="flex p-4 gap-2">
                               <Button
                                   disabled={isCreating}
                                   onClick={handleCreateNote}
                                   className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                                   New note
                               </Button>
                           </div>
             
            </>}
            editable={false}>
            <div className='size-full overflow-hidden pt-4'>
            <WorkspaceNotes
                hideTitle
                ref={noteRef}
            />
        </div>
        </MainContentLayout>
    </>
}

export default Page